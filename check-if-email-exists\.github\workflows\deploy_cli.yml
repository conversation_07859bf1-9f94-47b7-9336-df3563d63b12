name: deploy_cli

on:
  push:
    tags:
      - "v*.*.*"

env:
  # TODO: Rename to your binary
  BIN: check_if_email_exists

jobs:
  # This job downloads and stores `cross` as an artifact, so that it can be
  # redownloaded across all of the jobs. Currently this copied pasted between
  # `mean_bean_ci.yml` and `mean_bean_deploy.yml`. Make sure to update both places when making
  # changes.
  install-cross:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
        with:
          depth: 50
      - uses: XAMPPRocky/get-github-release@v1
        id: cross
        with:
          owner: rust-embedded
          repo: cross
          matches: ${{ matrix.platform }}
          token: ${{ secrets.GITHUB_TOKEN }}
      - uses: actions/upload-artifact@v4
        with:
          name: cross-${{ matrix.platform }}
          path: ${{ steps.cross.outputs.install_path }}
    strategy:
      matrix:
        platform: [linux-musl, apple-darwin]

  windows:
    runs-on: windows-latest
    if: false # Disable Windows tests for now, because Reacher Worker is not supported on Windows
    needs: install-cross
    steps:
      - uses: actions/checkout@v2
      - uses: shogo82148/actions-setup-perl@v1
        with:
          perl-version: "5.32"
          distribution: strawberry
      - run: ci/set_rust_version.bash ${{ matrix.channel }} ${{ matrix.target }}
        shell: bash
      - run: ci/build.ps1 cargo ${{ matrix.target }} RELEASE
      - run: tar -czvf ${{ env.BIN }}.tar.gz --directory=target/${{ matrix.target }}/release ${{ env.BIN }}.exe
      - uses: XAMPPRocky/create-release@v1.0.2
        id: create_release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: ${{ github.ref }}
          draft: false
          prerelease: false
      - uses: actions/upload-release-asset@v1
        id: upload-release-asset
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ${{ env.BIN }}.tar.gz
          asset_name: ${{ env.BIN }}-${{ matrix.target }}.tar.gz
          asset_content_type: application/gzip
    strategy:
      fail-fast: true
      matrix:
        channel: [stable]
        target:
          - x86_64-pc-windows-msvc
          - i686-pc-windows-msvc
  macos:
    runs-on: macos-latest
    needs: install-cross
    strategy:
      matrix:
        target:
          # macOS
          - x86_64-apple-darwin
          # iOS
          # - aarch64-apple-ios
          # - armv7-apple-ios
          # - armv7s-apple-ios
          # - i386-apple-ios
          # - x86_64-apple-ios
    steps:
      - uses: actions/checkout@v2
      - uses: actions/download-artifact@v4
        with:
          name: cross-apple-darwin
          path: /usr/local/bin/
      - run: chmod +x /usr/local/bin/cross

      - run: ci/set_rust_version.bash stable ${{ matrix.target }}
      - run: ci/build.bash cross ${{ matrix.target }} RELEASE
      - run: tar -czvf ${{ env.BIN }}.tar.gz --directory=target/${{ matrix.target }}/release ${{ env.BIN }}
      - uses: XAMPPRocky/create-release@v1.0.2
        id: create_release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: ${{ github.ref }}
          draft: false
          prerelease: false
      - uses: actions/upload-release-asset@v1
        id: upload-release-asset
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ${{ env.BIN }}.tar.gz
          asset_name: ${{ env.BIN }}-${{ matrix.target }}.tar.gz
          asset_content_type: application/gzip

  linux:
    runs-on: ubuntu-latest
    needs: install-cross
    strategy:
      matrix:
        target:
          # WASM, off by default as most rust projects aren't compatible yet.
          # - wasm32-unknown-emscripten
          # Linux
          - aarch64-unknown-linux-gnu
          # - arm-unknown-linux-gnueabi
          # - armv7-unknown-linux-gnueabihf
          # - i686-unknown-linux-gnu
          # - i686-unknown-linux-musl
          # - mips-unknown-linux-gnu
          # - mips64-unknown-linux-gnuabi64
          # - mips64el-unknown-linux-gnuabi64
          # - mipsel-unknown-linux-gnu
          # - powerpc-unknown-linux-gnu
          # - powerpc64-unknown-linux-gnu
          # - powerpc64le-unknown-linux-gnu
          # - s390x-unknown-linux-gnu
          - x86_64-unknown-linux-gnu
          - x86_64-unknown-linux-musl
          # Android
          # - aarch64-linux-android
          # - arm-linux-androideabi
          # - armv7-linux-androideabi
          # - i686-linux-android
          # - x86_64-linux-android
          # *BSD
          # The FreeBSD targets can have issues linking so they are disabled
          # by default.
          # - i686-unknown-freebsd
          # - x86_64-unknown-freebsd
          # - x86_64-unknown-netbsd
          # Solaris
          # - sparcv9-sun-solaris
          # Bare Metal
          # These are no-std embedded targets, so they will only build if your
          # crate is `no_std` compatible.
          # - thumbv6m-none-eabi
          # - thumbv7em-none-eabi
          # - thumbv7em-none-eabihf
          # - thumbv7m-none-eabi
    steps:
      - uses: actions/checkout@v2
      - uses: actions/download-artifact@v4
        with:
          name: cross-linux-musl
          path: /tmp/
      - run: chmod +x /tmp/cross

      - run: ci/set_rust_version.bash stable ${{ matrix.target }}
      - run: ci/build.bash /tmp/cross ${{ matrix.target }} RELEASE
      - run: tar -czvf ${{ env.BIN }}.tar.gz --directory=target/${{ matrix.target }}/release ${{ env.BIN }}
      - uses: XAMPPRocky/create-release@v1.0.2
        id: create_release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: ${{ github.ref }}
          draft: false
          prerelease: false
      - name: Upload Release Asset
        id: upload-release-asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ${{ env.BIN }}.tar.gz
          asset_name: ${{ env.BIN }}-${{ matrix.target }}.tar.gz
          asset_content_type: application/gzip
