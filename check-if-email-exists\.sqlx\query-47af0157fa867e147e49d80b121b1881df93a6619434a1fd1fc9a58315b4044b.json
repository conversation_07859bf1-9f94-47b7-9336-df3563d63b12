{"db_name": "PostgreSQL", "query": "\n\t\tSELECT id, created_at, total_records FROM bulk_jobs\n\t\tWHERE id = $1\n\t\tLIMIT 1\n\t\t", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Int4"}, {"ordinal": 1, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 2, "name": "total_records", "type_info": "Int4"}], "parameters": {"Left": ["Int4"]}, "nullable": [false, false, false]}, "hash": "47af0157fa867e147e49d80b121b1881df93a6619434a1fd1fc9a58315b4044b"}