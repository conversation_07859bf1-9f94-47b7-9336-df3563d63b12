"""
Test script to diagnose scraping errors in the overnight extraction script.
"""

import asyncio
import sys
from datetime import datetime
from aganl.perfect_contact_extractor import PerfectContactExtractor

async def test_basic_scraping():
    """Test basic scraping functionality to identify errors."""
    print("🔍 TESTING SCRAPING FUNCTIONALITY")
    print("=" * 50)
    
    # Test URLs - mix of simple and potentially problematic sites
    test_urls = [
        "https://httpbin.org/html",  # Simple, reliable test site
        "https://example.com",       # Basic site
        "https://google.com",        # Major site
    ]
    
    print(f"Testing with {len(test_urls)} URLs...")
    
    # Create extractor with conservative settings
    extractor = PerfectContactExtractor(batch_size=1, max_concurrent=1)
    
    try:
        results = await extractor.extract_perfect(test_urls)
        
        print(f"\n📊 RESULTS:")
        for result in results:
            url = result.get('url', 'Unknown')
            if 'error' in result:
                print(f"❌ {url}: ERROR - {result['error']}")
            else:
                print(f"✅ {url}: SUCCESS")
                print(f"   Email: {result.get('email', 'None')}")
                print(f"   Phone: {result.get('phone', 'None')}")
                print(f"   Pages checked: {result.get('pages_checked', 0)}")
        
        return results
        
    except Exception as e:
        print(f"❌ CRITICAL ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

async def test_crawl4ai_directly():
    """Test crawl4ai directly to isolate issues."""
    print("\n🔍 TESTING CRAWL4AI DIRECTLY")
    print("=" * 50)
    
    try:
        from crawl4ai import AsyncWebCrawler
        print("✅ crawl4ai imported successfully")
        
        async with AsyncWebCrawler(verbose=True) as crawler:
            test_url = "https://httpbin.org/html"
            print(f"Testing URL: {test_url}")
            
            result = await crawler.arun(url=test_url)
            
            if result.success:
                print("✅ Crawl4ai working correctly")
                print(f"Content length: {len(result.cleaned_html) if result.cleaned_html else 0}")
                return True
            else:
                print("❌ Crawl4ai failed")
                print(f"Error: {getattr(result, 'error', 'Unknown error')}")
                return False
                
    except Exception as e:
        print(f"❌ Crawl4ai error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print(f"🚀 SCRAPING ERROR DIAGNOSIS")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python: {sys.version}")
    print("=" * 60)
    
    # Test 1: Direct crawl4ai test
    crawl4ai_ok = await test_crawl4ai_directly()
    
    if not crawl4ai_ok:
        print("\n❌ ISSUE FOUND: crawl4ai is not working properly")
        print("This is likely the root cause of your scraping errors.")
        return
    
    # Test 2: Perfect extractor test
    print("\n" + "="*60)
    results = await test_basic_scraping()
    
    if results:
        error_count = len([r for r in results if 'error' in r])
        success_count = len([r for r in results if 'error' not in r])
        
        print(f"\n📈 SUMMARY:")
        print(f"✅ Successful: {success_count}")
        print(f"❌ Failed: {error_count}")
        
        if error_count > 0:
            print(f"\n🔍 ERROR ANALYSIS:")
            for result in results:
                if 'error' in result:
                    print(f"   • {result['url']}: {result['error']}")
    
    print(f"\n✅ DIAGNOSIS COMPLETE")

if __name__ == "__main__":
    asyncio.run(main())
