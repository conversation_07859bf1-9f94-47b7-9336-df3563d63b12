"""
Test the Contact Extractor with real website data from test_data.csv
This script will extract contact information from the coffee shop websites.
"""

import asyncio
import csv
import json
from datetime import datetime
from contact_extractor import ContactExtractor


def load_test_urls(csv_file: str) -> list:
    """Load URLs from the test CSV file."""
    urls = []
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                url = row.get('website_url', '').strip()
                if url and url.startswith(('http://', 'https://')):
                    urls.append(url)
        print(f"Loaded {len(urls)} URLs from {csv_file}")
        return urls
    except FileNotFoundError:
        print(f"Error: {csv_file} not found")
        return []
    except Exception as e:
        print(f"Error loading URLs: {str(e)}")
        return []


async def test_single_url(extractor: ContactExtractor, url: str) -> dict:
    """Test extraction from a single URL with detailed output."""
    print(f"\n{'='*60}")
    print(f"Testing: {url}")
    print(f"{'='*60}")
    
    try:
        results = await extractor.extract_contacts([url], strategy="comprehensive")
        result = results[0] if results else {}
        
        if 'error' in result:
            print(f"❌ Error: {result['error']}")
            return result
        
        # Display results
        print(f"✅ Successfully extracted from: {result.get('title', 'No title')}")
        
        contacts = result.get('contacts', {})
        
        # Count totals
        total_contacts = 0
        
        # Emails
        emails = contacts.get('emails', [])
        if emails:
            print(f"\n📧 Found {len(emails)} email(s):")
            for email_info in emails:
                print(f"   • {email_info.get('email', 'N/A')} (confidence: {email_info.get('confidence', 'N/A')})")
                total_contacts += 1
        
        # Phones
        phones = contacts.get('phones', [])
        if phones:
            print(f"\n📞 Found {len(phones)} phone number(s):")
            for phone_info in phones:
                print(f"   • {phone_info.get('phone', 'N/A')} (confidence: {phone_info.get('confidence', 'N/A')})")
                total_contacts += 1
        
        # Addresses
        addresses = contacts.get('addresses', [])
        if addresses:
            print(f"\n📍 Found {len(addresses)} address(es):")
            for addr_info in addresses:
                addr_text = addr_info.get('address', 'N/A')
                # Truncate long addresses for display
                if len(addr_text) > 80:
                    addr_text = addr_text[:77] + "..."
                print(f"   • {addr_text} (confidence: {addr_info.get('confidence', 'N/A')})")
                total_contacts += 1
        
        # Social Media
        social_media = contacts.get('social_media', [])
        if social_media:
            print(f"\n🌐 Found {len(social_media)} social media link(s):")
            for social_info in social_media:
                platform = social_info.get('platform', 'Unknown')
                handle = social_info.get('handle', 'N/A')
                print(f"   • {platform.title()}: {handle}")
                total_contacts += 1
        
        # Websites
        websites = contacts.get('websites', [])
        if websites:
            print(f"\n🔗 Found {len(websites)} additional website(s):")
            for website_info in websites:
                print(f"   • {website_info.get('url', 'N/A')}")
        
        if total_contacts == 0:
            print("\n⚠️  No contact information found")
        else:
            print(f"\n✨ Total contact items found: {total_contacts}")
        
        return result
        
    except Exception as e:
        print(f"❌ Exception occurred: {str(e)}")
        return {"url": url, "error": str(e)}


async def test_batch_extraction(urls: list, max_concurrent: int = 3) -> list:
    """Test batch extraction with limited concurrency."""
    print(f"\n{'='*60}")
    print(f"BATCH EXTRACTION TEST - Processing {len(urls)} URLs")
    print(f"Max concurrent requests: {max_concurrent}")
    print(f"{'='*60}")
    
    extractor = ContactExtractor(use_llm=False)
    
    # Process URLs in batches to avoid overwhelming servers
    results = []
    
    for i in range(0, len(urls), max_concurrent):
        batch = urls[i:i + max_concurrent]
        print(f"\nProcessing batch {i//max_concurrent + 1}: {len(batch)} URLs")
        
        try:
            batch_results = await extractor.extract_contacts(batch, strategy="comprehensive")
            results.extend(batch_results)
            
            # Brief summary of batch results
            for result in batch_results:
                url = result.get('url', 'Unknown')
                if 'error' in result:
                    print(f"  ❌ {url}: Error")
                else:
                    contacts = result.get('contacts', {})
                    total = (len(contacts.get('emails', [])) + 
                            len(contacts.get('phones', [])) + 
                            len(contacts.get('addresses', [])))
                    print(f"  ✅ {url}: {total} contacts")
            
            # Small delay between batches to be respectful
            if i + max_concurrent < len(urls):
                print("  Waiting 2 seconds before next batch...")
                await asyncio.sleep(2)
                
        except Exception as e:
            print(f"  ❌ Batch error: {str(e)}")
            # Add error results for this batch
            for url in batch:
                results.append({"url": url, "error": str(e)})
    
    return results


def generate_summary_report(results: list) -> dict:
    """Generate a summary report of all extraction results."""
    summary = {
        "total_urls": len(results),
        "successful_extractions": 0,
        "failed_extractions": 0,
        "total_emails": 0,
        "total_phones": 0,
        "total_addresses": 0,
        "total_social_media": 0,
        "urls_with_contacts": 0,
        "top_contact_sources": []
    }
    
    contact_counts = []
    
    for result in results:
        if 'error' in result:
            summary["failed_extractions"] += 1
            continue
        
        summary["successful_extractions"] += 1
        contacts = result.get('contacts', {})
        
        email_count = len(contacts.get('emails', []))
        phone_count = len(contacts.get('phones', []))
        address_count = len(contacts.get('addresses', []))
        social_count = len(contacts.get('social_media', []))
        
        summary["total_emails"] += email_count
        summary["total_phones"] += phone_count
        summary["total_addresses"] += address_count
        summary["total_social_media"] += social_count
        
        total_contacts = email_count + phone_count + address_count + social_count
        if total_contacts > 0:
            summary["urls_with_contacts"] += 1
            contact_counts.append({
                "url": result.get('url', 'Unknown'),
                "title": result.get('title', 'No title'),
                "total_contacts": total_contacts,
                "emails": email_count,
                "phones": phone_count,
                "addresses": address_count,
                "social_media": social_count
            })
    
    # Sort by total contacts and get top 5
    contact_counts.sort(key=lambda x: x['total_contacts'], reverse=True)
    summary["top_contact_sources"] = contact_counts[:5]
    
    return summary


def print_summary_report(summary: dict):
    """Print a formatted summary report."""
    print(f"\n{'='*60}")
    print("EXTRACTION SUMMARY REPORT")
    print(f"{'='*60}")
    
    print(f"📊 Overall Statistics:")
    print(f"   • Total URLs processed: {summary['total_urls']}")
    print(f"   • Successful extractions: {summary['successful_extractions']}")
    print(f"   • Failed extractions: {summary['failed_extractions']}")
    print(f"   • URLs with contacts: {summary['urls_with_contacts']}")
    
    success_rate = (summary['successful_extractions'] / summary['total_urls'] * 100) if summary['total_urls'] > 0 else 0
    contact_rate = (summary['urls_with_contacts'] / summary['successful_extractions'] * 100) if summary['successful_extractions'] > 0 else 0
    
    print(f"   • Success rate: {success_rate:.1f}%")
    print(f"   • Contact discovery rate: {contact_rate:.1f}%")
    
    print(f"\n📞 Contact Information Found:")
    print(f"   • Total emails: {summary['total_emails']}")
    print(f"   • Total phones: {summary['total_phones']}")
    print(f"   • Total addresses: {summary['total_addresses']}")
    print(f"   • Total social media: {summary['total_social_media']}")
    
    if summary['top_contact_sources']:
        print(f"\n🏆 Top Contact Sources:")
        for i, source in enumerate(summary['top_contact_sources'], 1):
            title = source['title'][:40] + "..." if len(source['title']) > 40 else source['title']
            print(f"   {i}. {title}")
            print(f"      📧 {source['emails']} emails, 📞 {source['phones']} phones, 📍 {source['addresses']} addresses")


async def main():
    """Main function to run the contact extraction tests."""
    print("Contact Extractor Test with Real Data")
    print("=" * 60)
    
    # Load test URLs
    urls = load_test_urls("test_data.csv")
    
    if not urls:
        print("No URLs to process. Exiting.")
        return
    
    print(f"Loaded {len(urls)} URLs to test")
    
    # Ask user for test mode
    print("\nChoose test mode:")
    print("1. Test first URL only (detailed)")
    print("2. Test first 3 URLs (detailed)")
    print("3. Batch test all URLs (summary)")
    
    try:
        choice = input("Enter choice (1-3) or press Enter for option 1: ").strip()
        if not choice:
            choice = "1"
    except KeyboardInterrupt:
        print("\nTest cancelled by user.")
        return
    
    start_time = datetime.now()
    
    if choice == "1":
        # Test single URL with detailed output
        extractor = ContactExtractor(use_llm=False)
        result = await test_single_url(extractor, urls[0])
        results = [result]
        
    elif choice == "2":
        # Test first 3 URLs with detailed output
        extractor = ContactExtractor(use_llm=False)
        results = []
        for url in urls[:3]:
            result = await test_single_url(extractor, url)
            results.append(result)
            
    elif choice == "3":
        # Batch test all URLs
        results = await test_batch_extraction(urls)
        
    else:
        print("Invalid choice. Using option 1.")
        extractor = ContactExtractor(use_llm=False)
        result = await test_single_url(extractor, urls[0])
        results = [result]
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # Generate and display summary
    summary = generate_summary_report(results)
    print_summary_report(summary)
    
    print(f"\n⏱️  Total execution time: {duration:.2f} seconds")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    json_filename = f"contact_extraction_results_{timestamp}.json"
    csv_filename = f"contact_extraction_results_{timestamp}.csv"
    
    extractor = ContactExtractor()
    extractor.export_to_json(results, json_filename)
    extractor.export_to_csv(results, csv_filename)
    
    print(f"\n💾 Results exported to:")
    print(f"   • {json_filename}")
    print(f"   • {csv_filename}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user. Goodbye!")
    except Exception as e:
        print(f"\nUnexpected error: {str(e)}")
