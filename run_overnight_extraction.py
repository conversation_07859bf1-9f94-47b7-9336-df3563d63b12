"""
Overnight Pittsburgh Prospects Extraction
Simple script to run the full dataset extraction overnight.
"""

import asyncio
from full_dataset_extractor import FullDatasetExtractor
from datetime import datetime

async def main():
    """Run the overnight extraction with improved error handling."""
    print("🌙 OVERNIGHT PITTSBURGH PROSPECTS EXTRACTION")
    print("=" * 60)
    print("This will process all prospects with valid websites overnight.")
    print("🎯 MULTI-FORMAT SUPPORT: APIs, JSON-LD, HTML")
    print("🛡️  IMPROVED ERROR HANDLING: Network issues handled gracefully")
    print("🔄 RETRY LOGIC: Failed URLs automatically retried once")
    print("🏥 ADAPTIVE PAGE CHECKING: Gov/medical sites get specialized page lists")
    print("🔍 DYNAMIC PAGE DISCOVERY: Fallback for sites with non-standard pages")
    print("You can safely leave your PC - it will run automatically!")
    print("Expected completion time: 3-4 hours")

    # Configuration
    INPUT_FILE = "pittsburgh_coffee_prospects_FINAL_20250914_164553.csv"
    OUTPUT_FILE = f"pittsburgh_prospects_contacts_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    print(f"\n📁 Input: {INPUT_FILE}")
    print(f"📁 Output: {OUTPUT_FILE}")

    # Create extractor with optimized overnight settings
    # Reduced concurrency to be more gentle on problematic websites
    extractor = FullDatasetExtractor(batch_size=15, max_concurrent=20)

    try:
        # Process the full dataset with robust error handling
        await extractor.process_full_dataset(INPUT_FILE, OUTPUT_FILE)

        print("\n🎉 EXTRACTION COMPLETE! Check your output files.")
        print("💤 You can now wake up to complete lead databases!")
        print("📧 Email database: Perfect for email campaigns")
        print("📞 Phone database: Perfect for cold calling campaigns")

    except KeyboardInterrupt:
        print("\n\n⏹️  Extraction interrupted by user.")
        print("Partial results may have been saved.")
        print("💡 TIP: You can resume by running the script again - it will skip already processed URLs.")
    except ConnectionError as e:
        print(f"\n🌐 Network connection error: {str(e)}")
        print("💡 This often happens when laptop goes to sleep or network disconnects.")
        print("💡 TIP: Run the script again to resume - progress is automatically saved.")
    except Exception as e:
        error_msg = str(e)
        print(f"\n❌ Unexpected error during extraction: {error_msg}")

        # Provide specific guidance for common issues
        if "Target page, context or browser has been closed" in error_msg:
            print("💡 This error often occurs when the laptop goes to sleep or browser crashes.")
            print("💡 SOLUTION: Simply run the script again - it will resume where it left off.")
        elif "Connection refused" in error_msg or "Network is unreachable" in error_msg:
            print("💡 Network connectivity issue detected.")
            print("💡 SOLUTION: Check your internet connection and run the script again.")
        else:
            print("💡 TIP: Run the script again to resume - partial progress is saved automatically.")

        print("\n🔄 TO RESUME: Simply run this script again with the same input file.")

if __name__ == "__main__":
    asyncio.run(main())
