"""
Refined Contact Information Extractor - Returns the BEST single contact for each type
Focuses on quality over quantity with smart selection algorithms.
"""

import asyncio
import json
import re
import csv
from datetime import datetime
from typing import Dict, List, Optional, Union, Tuple
from urllib.parse import urlparse
from collections import Counter

from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai import JsonCssExtractionStrategy, RegexExtractionStrategy


class RefinedContactExtractor:
    """
    Refined contact extractor that returns the BEST single contact for each type:
    - 1 business name
    - 1 email address
    - 1 phone number
    - 1 address
    - 1 social media link
    """
    
    def __init__(self):
        """Initialize the refined contact extractor."""
        self.scoring_weights = self._setup_scoring_weights()
    
    def _setup_scoring_weights(self) -> Dict:
        """Setup scoring weights for selecting the best contacts."""
        return {
            'email_preferences': [
                ('contact@', 10), ('info@', 9), ('hello@', 8), ('support@', 7),
                ('admin@', 6), ('sales@', 5), ('marketing@', 4)
            ],
            'phone_quality_indicators': [
                (r'^\+?1[-.\s]?\(?[2-9]\d{2}\)?[-.\s]?[2-9]\d{2}[-.\s]?\d{4}$', 10),  # Perfect US format
                (r'^\(?[2-9]\d{2}\)?[-.\s]?[2-9]\d{2}[-.\s]?\d{4}$', 9),  # US without country code
                (r'^\+\d{1,3}[-.\s]?\d{2,4}[-.\s]?\d{2,4}[-.\s]?\d{2,6}$', 8)  # International
            ],
            'business_name_sources': [
                ('title', 10), ('h1', 9), ('.company-name', 8), ('.business-name', 7),
                ('.site-title', 6), ('.brand', 5)
            ],
            'address_quality_indicators': [
                (r'\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd)', 10),
                (r'\d+\s+[A-Za-z\s]+,\s*[A-Za-z\s]+,\s*[A-Z]{2}\s*\d{5}', 9),
                (r'.*(?:Street|Avenue|Road|Boulevard|Drive|Lane|Court|Place).*', 7)
            ],
            'social_platform_priority': {
                'instagram': 10, 'facebook': 9, 'twitter': 8, 'linkedin': 7,
                'youtube': 6, 'tiktok': 5
            }
        }
    
    async def extract_best_contacts(self, urls: Union[str, List[str]]) -> List[Dict]:
        """
        Extract the BEST single contact for each type from URLs.
        
        Returns:
            List of dictionaries with single best contact for each type
        """
        if isinstance(urls, str):
            urls = [urls]
        
        results = []
        
        async with AsyncWebCrawler(verbose=True) as crawler:
            for url in urls:
                try:
                    print(f"\n{'='*60}")
                    print(f"Finding best contacts from: {url}")
                    print(f"{'='*60}")
                    
                    # Extract all potential contacts
                    all_contacts = await self._extract_all_contacts(crawler, url)
                    
                    # Select the best single contact for each type
                    best_contacts = self._select_best_contacts(all_contacts, url)
                    
                    results.append(best_contacts)
                    
                    # Display results
                    self._display_best_contacts(best_contacts)
                    
                except Exception as e:
                    print(f"Error extracting from {url}: {str(e)}")
                    results.append({
                        "url": url,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    })
        
        return results
    
    async def _extract_all_contacts(self, crawler: AsyncWebCrawler, url: str) -> Dict:
        """Extract all potential contacts using multiple strategies."""
        
        # Strategy 1: Regex extraction
        regex_data = await self._extract_with_regex(crawler, url)
        
        # Strategy 2: CSS extraction
        css_data = await self._extract_with_css(crawler, url)
        
        # Strategy 3: Get page content for manual analysis
        basic_result = await crawler.arun(url=url, bypass_cache=True)
        content = getattr(basic_result, 'cleaned_html', '') or ''
        title = getattr(basic_result, 'title', '') or ''
        
        # Combine all data
        return {
            'regex_data': regex_data,
            'css_data': css_data,
            'content': content,
            'title': title,
            'url': url
        }
    
    async def _extract_with_regex(self, crawler: AsyncWebCrawler, url: str) -> Dict:
        """Extract using regex patterns."""
        strategy = RegexExtractionStrategy(
            pattern=(
                RegexExtractionStrategy.Email |
                RegexExtractionStrategy.PhoneUS |
                RegexExtractionStrategy.PhoneIntl
            )
        )
        
        config = CrawlerRunConfig(extraction_strategy=strategy)
        result = await crawler.arun(url=url, config=config)
        
        extracted = {"emails": [], "phones": []}
        
        if result.success and result.extracted_content:
            try:
                data = json.loads(result.extracted_content)
                for item in data:
                    label = item.get('label', '').lower()
                    value = item.get('value', '')
                    
                    if 'email' in label and self._is_valid_email(value):
                        extracted["emails"].append(value)
                    elif 'phone' in label and self._is_valid_phone(value):
                        extracted["phones"].append(value)
            except json.JSONDecodeError:
                pass
        
        return extracted
    
    async def _extract_with_css(self, crawler: AsyncWebCrawler, url: str) -> Dict:
        """Extract using CSS selectors."""
        schema = {
            "name": "Contact Information",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "business_names",
                    "selector": "title, h1, .company-name, .business-name, .site-title, .brand, .logo-text",
                    "type": "list",
                    "fields": [{"name": "name", "type": "text"}]
                },
                {
                    "name": "emails",
                    "selector": "a[href^='mailto:']",
                    "type": "list",
                    "fields": [{"name": "email", "type": "attribute", "attribute": "href"}]
                },
                {
                    "name": "phones",
                    "selector": "a[href^='tel:']",
                    "type": "list",
                    "fields": [{"name": "phone", "type": "attribute", "attribute": "href"}]
                },
                {
                    "name": "addresses",
                    "selector": ".address, .location, [itemtype*='PostalAddress'], .contact-address, .street-address",
                    "type": "list",
                    "fields": [{"name": "address", "type": "text"}]
                },
                {
                    "name": "social_links",
                    "selector": "a[href*='instagram.com'], a[href*='facebook.com'], a[href*='twitter.com'], a[href*='linkedin.com']",
                    "type": "list",
                    "fields": [{"name": "url", "type": "attribute", "attribute": "href"}]
                }
            ]
        }
        
        strategy = JsonCssExtractionStrategy(schema, verbose=False)
        config = CrawlerRunConfig(extraction_strategy=strategy)
        result = await crawler.arun(url=url, config=config)
        
        if result.success and result.extracted_content:
            try:
                data = json.loads(result.extracted_content)
                return data[0] if data else {}
            except json.JSONDecodeError:
                pass
        
        return {}
    
    def _select_best_contacts(self, all_data: Dict, url: str) -> Dict:
        """Select the single best contact for each type."""
        
        # Get all potential contacts
        all_emails = self._collect_all_emails(all_data)
        all_phones = self._collect_all_phones(all_data)
        all_names = self._collect_all_names(all_data)
        all_addresses = self._collect_all_addresses(all_data)
        all_social = self._collect_all_social(all_data)
        
        # Select best single contact for each type
        best_email = self._select_best_email(all_emails)
        best_phone = self._select_best_phone(all_phones)
        best_name = self._select_best_name(all_names, all_data.get('title', ''))
        best_address = self._select_best_address(all_addresses)
        best_social = self._select_best_social(all_social)
        
        return {
            "url": url,
            "timestamp": datetime.now().isoformat(),
            "contacts": {
                "business_name": best_name,
                "email": best_email,
                "phone": best_phone,
                "address": best_address,
                "social_media": best_social
            },
            "metadata": {
                "extraction_method": "refined_selection",
                "candidates_found": {
                    "emails": len(all_emails),
                    "phones": len(all_phones),
                    "names": len(all_names),
                    "addresses": len(all_addresses),
                    "social": len(all_social)
                }
            }
        }
    
    def _collect_all_emails(self, all_data: Dict) -> List[str]:
        """Collect all potential email addresses."""
        emails = set()
        
        # From regex
        emails.update(all_data.get('regex_data', {}).get('emails', []))
        
        # From CSS
        css_emails = all_data.get('css_data', {}).get('emails', [])
        for email_item in css_emails:
            if isinstance(email_item, dict):
                email = email_item.get('email', '')
                if email.startswith('mailto:'):
                    email = email[7:]
                if self._is_valid_email(email):
                    emails.add(email.lower())
        
        # From content analysis
        content = all_data.get('content', '')
        email_pattern = r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b'
        content_emails = re.findall(email_pattern, content)
        for email in content_emails:
            if self._is_valid_email(email):
                emails.add(email.lower())
        
        return list(emails)
    
    def _collect_all_phones(self, all_data: Dict) -> List[str]:
        """Collect all potential phone numbers."""
        phones = set()
        
        # From regex
        phones.update(all_data.get('regex_data', {}).get('phones', []))
        
        # From CSS
        css_phones = all_data.get('css_data', {}).get('phones', [])
        for phone_item in css_phones:
            if isinstance(phone_item, dict):
                phone = phone_item.get('phone', '')
                if phone.startswith('tel:'):
                    phone = phone[4:]
                if self._is_valid_phone(phone):
                    phones.add(phone)
        
        return list(phones)
    
    def _collect_all_names(self, all_data: Dict) -> List[str]:
        """Collect all potential business names."""
        names = []
        
        # From CSS
        css_names = all_data.get('css_data', {}).get('business_names', [])
        for name_item in css_names:
            if isinstance(name_item, dict):
                name = name_item.get('name', '').strip()
                if name and len(name) > 2 and len(name) < 100:
                    names.append(name)
        
        # From title
        title = all_data.get('title', '').strip()
        if title:
            names.append(title)
        
        return names
    
    def _collect_all_addresses(self, all_data: Dict) -> List[str]:
        """Collect all potential addresses."""
        addresses = []
        
        # From CSS
        css_addresses = all_data.get('css_data', {}).get('addresses', [])
        for addr_item in css_addresses:
            if isinstance(addr_item, dict):
                address = addr_item.get('address', '').strip()
                if self._is_valid_address(address):
                    addresses.append(address)
        
        return addresses
    
    def _collect_all_social(self, all_data: Dict) -> List[str]:
        """Collect all potential social media links."""
        social_links = set()
        
        # From CSS
        css_social = all_data.get('css_data', {}).get('social_links', [])
        for social_item in css_social:
            if isinstance(social_item, dict):
                url = social_item.get('url', '')
                if url:
                    social_links.add(url)
        
        return list(social_links)
    
    def _select_best_email(self, emails: List[str]) -> Optional[Dict]:
        """Select the best email address."""
        if not emails:
            return None
        
        # Score emails based on common patterns
        scored_emails = []
        for email in emails:
            score = 1  # Base score
            
            # Prefer common business email prefixes
            for prefix, bonus in self.scoring_weights['email_preferences']:
                if email.startswith(prefix):
                    score += bonus
                    break
            
            # Prefer shorter, cleaner emails
            if len(email) < 30:
                score += 2
            
            # Avoid emails with numbers (often less professional)
            if not re.search(r'\d', email.split('@')[0]):
                score += 1
            
            scored_emails.append((email, score))
        
        # Return highest scored email
        best_email = max(scored_emails, key=lambda x: x[1])[0]
        return {
            "email": best_email,
            "confidence": min(0.95, 0.7 + (max(scored_emails, key=lambda x: x[1])[1] / 20))
        }
    
    def _select_best_phone(self, phones: List[str]) -> Optional[Dict]:
        """Select the best phone number."""
        if not phones:
            return None
        
        # Score phones based on format quality
        scored_phones = []
        for phone in phones:
            score = 1  # Base score
            
            # Score based on format quality
            for pattern, bonus in self.scoring_weights['phone_quality_indicators']:
                if re.match(pattern, phone):
                    score += bonus
                    break
            
            # Prefer phones with proper formatting
            if '-' in phone or '(' in phone:
                score += 2
            
            scored_phones.append((phone, score))
        
        # Return highest scored phone
        best_phone = max(scored_phones, key=lambda x: x[1])[0]
        return {
            "phone": best_phone,
            "confidence": min(0.95, 0.7 + (max(scored_phones, key=lambda x: x[1])[1] / 15))
        }
    
    def _select_best_name(self, names: List[str], title: str) -> Optional[Dict]:
        """Select the best business name."""
        if not names:
            return None
        
        # Score names based on source and quality
        scored_names = []
        for name in names:
            score = 1  # Base score
            
            # Clean the name
            clean_name = self._clean_business_name(name)
            if not clean_name:
                continue
            
            # Prefer names from title
            if name == title:
                score += 10
            
            # Prefer shorter, cleaner names
            if 5 <= len(clean_name) <= 50:
                score += 3
            
            # Avoid names with too many special characters
            special_chars = len(re.findall(r'[^\w\s&.-]', clean_name))
            if special_chars < 3:
                score += 2
            
            scored_names.append((clean_name, score))
        
        if not scored_names:
            return None
        
        # Return highest scored name
        best_name = max(scored_names, key=lambda x: x[1])[0]
        return {
            "business_name": best_name,
            "confidence": min(0.95, 0.8 + (max(scored_names, key=lambda x: x[1])[1] / 20))
        }
    
    def _select_best_address(self, addresses: List[str]) -> Optional[Dict]:
        """Select the best address."""
        if not addresses:
            return None
        
        # Score addresses based on completeness
        scored_addresses = []
        for address in addresses:
            score = 1  # Base score
            
            # Score based on address quality indicators
            for pattern, bonus in self.scoring_weights['address_quality_indicators']:
                if re.search(pattern, address, re.IGNORECASE):
                    score += bonus
                    break
            
            # Prefer addresses with zip codes
            if re.search(r'\d{5}(-\d{4})?', address):
                score += 3
            
            # Prefer longer, more complete addresses
            if 20 <= len(address) <= 100:
                score += 2
            
            scored_addresses.append((address.strip(), score))
        
        # Return highest scored address
        best_address = max(scored_addresses, key=lambda x: x[1])[0]
        return {
            "address": best_address,
            "confidence": min(0.95, 0.6 + (max(scored_addresses, key=lambda x: x[1])[1] / 20))
        }
    
    def _select_best_social(self, social_links: List[str]) -> Optional[Dict]:
        """Select the best social media link."""
        if not social_links:
            return None
        
        # Score social links based on platform priority
        scored_social = []
        for link in social_links:
            platform = self._identify_social_platform(link)
            if not platform:
                continue
            
            score = self.scoring_weights['social_platform_priority'].get(platform, 1)
            
            # Prefer main profile pages over specific posts
            if not re.search(r'/(p|posts|status|tweet)/', link):
                score += 3
            
            scored_social.append((link, platform, score))
        
        if not scored_social:
            return None
        
        # Return highest scored social link
        best_social = max(scored_social, key=lambda x: x[2])
        return {
            "platform": best_social[1],
            "url": best_social[0],
            "handle": self._extract_social_handle(best_social[0]),
            "confidence": min(0.95, 0.8 + (best_social[2] / 15))
        }

    def _is_valid_email(self, email: str) -> bool:
        """Validate email address."""
        if not email or len(email) < 5:
            return False

        # Check for image files and other false positives
        if re.search(r'\.(svg|png|jpg|jpeg|gif|webp|ico|css|js)$', email, re.IGNORECASE):
            return False

        # Basic email format validation
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))

    def _is_valid_phone(self, phone: str) -> bool:
        """Validate phone number."""
        if not phone:
            return False

        # Remove common prefixes
        clean_phone = phone.replace('tel:', '').strip()

        # Check for coordinates and other false positives
        if re.match(r'^\d{1,2}\.\d+$', clean_phone):  # Coordinates
            return False
        if re.match(r'^\d+\.\d{6,}$', clean_phone):  # Long decimals
            return False
        if len(re.sub(r'\D', '', clean_phone)) < 7:  # Too few digits
            return False
        if len(re.sub(r'\D', '', clean_phone)) > 15:  # Too many digits
            return False

        return True

    def _is_valid_address(self, address: str) -> bool:
        """Validate address."""
        if not address or len(address.strip()) < 10:
            return False

        # Must contain some address-like keywords
        address_keywords = ['street', 'st', 'avenue', 'ave', 'road', 'rd', 'boulevard', 'blvd', 'lane', 'ln', 'drive', 'dr']
        has_keyword = any(keyword in address.lower() for keyword in address_keywords)

        # Must contain numbers (street numbers)
        has_numbers = bool(re.search(r'\d+', address))

        return has_keyword and has_numbers

    def _clean_business_name(self, name: str) -> str:
        """Clean and normalize business name."""
        if not name:
            return ""

        # Remove common suffixes and prefixes
        name = re.sub(r'^(Welcome to|Home -|About -)\s*', '', name, flags=re.IGNORECASE)
        name = re.sub(r'\s*-\s*(Home|About|Contact).*$', '', name, flags=re.IGNORECASE)

        # Remove excessive whitespace
        name = ' '.join(name.split())

        # Remove very short or very long names
        if len(name) < 3 or len(name) > 80:
            return ""

        return name.strip()

    def _identify_social_platform(self, url: str) -> Optional[str]:
        """Identify social media platform."""
        platforms = {
            'instagram.com': 'instagram',
            'facebook.com': 'facebook',
            'twitter.com': 'twitter',
            'x.com': 'twitter',
            'linkedin.com': 'linkedin',
            'youtube.com': 'youtube',
            'tiktok.com': 'tiktok'
        }

        for domain, platform in platforms.items():
            if domain in url.lower():
                return platform
        return None

    def _extract_social_handle(self, url: str) -> Optional[str]:
        """Extract social media handle."""
        try:
            parsed = urlparse(url)
            path_parts = [p for p in parsed.path.strip('/').split('/') if p]
            if path_parts and not path_parts[0] in ['p', 'posts', 'status', 'tweet']:
                return f"@{path_parts[0]}"
        except:
            pass
        return None

    def _display_best_contacts(self, result: Dict):
        """Display the best contacts found."""
        contacts = result.get('contacts', {})
        metadata = result.get('metadata', {})

        print(f"🎯 BEST CONTACTS FOUND:")

        # Business name
        business = contacts.get('business_name')
        if business:
            print(f"🏢 Business: {business['business_name']} (confidence: {business['confidence']:.2f})")
        else:
            print("🏢 Business: Not found")

        # Email
        email = contacts.get('email')
        if email:
            print(f"📧 Email: {email['email']} (confidence: {email['confidence']:.2f})")
        else:
            print("📧 Email: Not found")

        # Phone
        phone = contacts.get('phone')
        if phone:
            print(f"📞 Phone: {phone['phone']} (confidence: {phone['confidence']:.2f})")
        else:
            print("📞 Phone: Not found")

        # Address
        address = contacts.get('address')
        if address:
            addr_display = address['address'][:60] + "..." if len(address['address']) > 60 else address['address']
            print(f"📍 Address: {addr_display} (confidence: {address['confidence']:.2f})")
        else:
            print("📍 Address: Not found")

        # Social media
        social = contacts.get('social_media')
        if social:
            print(f"🌐 Social: {social['platform'].title()} - {social.get('handle', social['url'])} (confidence: {social['confidence']:.2f})")
        else:
            print("🌐 Social: Not found")

        # Show candidates found
        candidates = metadata.get('candidates_found', {})
        print(f"\n📊 Candidates analyzed: {candidates.get('emails', 0)} emails, {candidates.get('phones', 0)} phones, {candidates.get('names', 0)} names, {candidates.get('addresses', 0)} addresses, {candidates.get('social', 0)} social")

    def export_to_json(self, results: List[Dict], filename: str):
        """Export results to JSON."""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"📄 Results exported to {filename}")

    def export_to_csv(self, results: List[Dict], filename: str):
        """Export results to CSV."""
        if not results:
            return

        flattened_data = []
        for result in results:
            if 'error' in result:
                continue

            contacts = result.get('contacts', {})
            row = {
                'url': result.get('url', ''),
                'timestamp': result.get('timestamp', ''),
                'business_name': contacts.get('business_name', {}).get('business_name', ''),
                'business_confidence': contacts.get('business_name', {}).get('confidence', ''),
                'email': contacts.get('email', {}).get('email', ''),
                'email_confidence': contacts.get('email', {}).get('confidence', ''),
                'phone': contacts.get('phone', {}).get('phone', ''),
                'phone_confidence': contacts.get('phone', {}).get('confidence', ''),
                'address': contacts.get('address', {}).get('address', ''),
                'address_confidence': contacts.get('address', {}).get('confidence', ''),
                'social_platform': contacts.get('social_media', {}).get('platform', ''),
                'social_handle': contacts.get('social_media', {}).get('handle', ''),
                'social_url': contacts.get('social_media', {}).get('url', ''),
                'social_confidence': contacts.get('social_media', {}).get('confidence', '')
            }
            flattened_data.append(row)

        if flattened_data:
            fieldnames = list(flattened_data[0].keys())
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(flattened_data)
            print(f"📊 Results exported to {filename}")


# Convenience function
async def extract_best_contacts_from_urls(urls: Union[str, List[str]]) -> List[Dict]:
    """
    Quick function to extract the best single contact for each type.

    Args:
        urls: Single URL or list of URLs

    Returns:
        List of results with best single contacts
    """
    extractor = RefinedContactExtractor()
    return await extractor.extract_best_contacts(urls)
