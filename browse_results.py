"""
Results Browser - Navigate organized chunk results
"""

import os
from pathlib import Path
import pandas as pd
from datetime import datetime


def show_folder_structure(results_dir="chunk_ui_results"):
    """Display the organized folder structure."""
    results_path = Path(results_dir)
    
    if not results_path.exists():
        print(f"❌ Results directory not found: {results_dir}")
        return
    
    print(f"📁 FOLDER STRUCTURE: {results_dir}")
    print("=" * 60)
    
    # Show main directory
    print(f"📂 {results_dir}/")
    
    # Show chunk folders
    chunk_folders = sorted([f for f in results_path.iterdir() if f.is_dir() and ('chunk' in f.name.lower() or f.name.startswith('test_'))])
    
    if not chunk_folders:
        print("   (No chunk folders found)")
        return
    
    for chunk_folder in chunk_folders:
        print(f"├── 📂 {chunk_folder.name}/")
        
        # Show mini_chunks folder
        mini_chunks_folder = chunk_folder / "mini_chunks"
        if mini_chunks_folder.exists():
            mini_chunk_files = list(mini_chunks_folder.glob("*.csv"))
            print(f"│   ├── 📂 mini_chunks/ ({len(mini_chunk_files)} files)")
            
            # Show first few files as examples
            for i, file in enumerate(sorted(mini_chunk_files)[:3]):
                connector = "│   │   ├──" if i < 2 else "│   │   └──"
                print(f"{connector} 📄 {file.name}")
            
            if len(mini_chunk_files) > 3:
                print(f"│   │   └── ... and {len(mini_chunk_files) - 3} more files")
        
        # Show combined_results folder
        combined_folder = chunk_folder / "combined_results"
        if combined_folder.exists():
            combined_files = list(combined_folder.glob("*.csv"))
            txt_files = list(combined_folder.glob("*.txt"))
            total_files = len(combined_files) + len(txt_files)
            print(f"│   └── 📂 combined_results/ ({total_files} files)")
            
            # Show combined files
            for file in sorted(combined_files + txt_files):
                print(f"│       └── 📄 {file.name}")
        else:
            print(f"│   └── 📂 combined_results/ (empty)")
        
        print("│")
    
    print("└── (End of structure)")


def show_chunk_summary(results_dir="chunk_ui_results"):
    """Show summary statistics for each chunk."""
    results_path = Path(results_dir)
    
    if not results_path.exists():
        print(f"❌ Results directory not found: {results_dir}")
        return
    
    print(f"📊 CHUNK PROCESSING SUMMARY")
    print("=" * 80)
    
    chunk_folders = sorted([f for f in results_path.iterdir() if f.is_dir() and ('chunk' in f.name.lower() or f.name.startswith('test_'))])
    
    if not chunk_folders:
        print("❌ No chunk folders found")
        return
    
    total_urls = 0
    total_emails = 0
    total_phones = 0
    
    for chunk_folder in chunk_folders:
        print(f"\n📂 {chunk_folder.name}")
        print("-" * 40)
        
        mini_chunks_folder = chunk_folder / "mini_chunks"
        if not mini_chunks_folder.exists():
            print("   ❌ No mini_chunks folder found")
            continue
        
        mini_chunk_files = list(mini_chunks_folder.glob("mini_chunk_*_results_*.csv"))
        
        if not mini_chunk_files:
            print("   ❌ No mini-chunk result files found")
            continue
        
        chunk_urls = 0
        chunk_emails = 0
        chunk_phones = 0
        
        print(f"   Mini-chunks processed: {len(mini_chunk_files)}")
        
        for file_path in mini_chunk_files:
            try:
                df = pd.read_csv(file_path)
                urls = len(df)
                emails = len(df[df['email'].notna() & (df['email'] != '')])
                phones = len(df[df['phone'].notna() & (df['phone'] != '')])
                
                chunk_urls += urls
                chunk_emails += emails
                chunk_phones += phones
                
            except Exception as e:
                print(f"   ❌ Error reading {file_path.name}: {e}")
        
        # Calculate rates
        email_rate = (chunk_emails / chunk_urls * 100) if chunk_urls > 0 else 0
        phone_rate = (chunk_phones / chunk_urls * 100) if chunk_urls > 0 else 0
        
        print(f"   URLs processed: {chunk_urls}")
        print(f"   Emails found: {chunk_emails} ({email_rate:.1f}%)")
        print(f"   Phones found: {chunk_phones} ({phone_rate:.1f}%)")
        
        # Check for combined results
        combined_folder = chunk_folder / "combined_results"
        if combined_folder.exists():
            combined_files = list(combined_folder.glob("combined_*.csv"))
            if combined_files:
                print(f"   Combined files: {len(combined_files)} ✅")
            else:
                print(f"   Combined files: 0 (run combine_results.py)")
        
        total_urls += chunk_urls
        total_emails += chunk_emails
        total_phones += chunk_phones
    
    # Overall summary
    print("\n" + "=" * 80)
    print("📈 OVERALL TOTALS")
    print("-" * 40)
    print(f"Total URLs processed: {total_urls}")
    print(f"Total emails found: {total_emails} ({total_emails/total_urls*100:.1f}%)" if total_urls > 0 else "Total emails: 0")
    print(f"Total phones found: {total_phones} ({total_phones/total_urls*100:.1f}%)" if total_urls > 0 else "Total phones: 0")
    print(f"Success rate: {(total_emails + total_phones)/total_urls*100:.1f}%" if total_urls > 0 else "Success rate: 0%")


def open_folder_in_explorer(results_dir="chunk_ui_results", chunk_name=None):
    """Open the results folder in Windows Explorer."""
    results_path = Path(results_dir)
    
    if chunk_name:
        folder_path = results_path / chunk_name
        if not folder_path.exists():
            print(f"❌ Chunk folder not found: {chunk_name}")
            return
    else:
        folder_path = results_path
    
    if not folder_path.exists():
        print(f"❌ Folder not found: {folder_path}")
        return
    
    try:
        os.startfile(str(folder_path))
        print(f"📂 Opened folder: {folder_path}")
    except Exception as e:
        print(f"❌ Failed to open folder: {e}")


def main():
    print("🔍 Results Browser")
    print("=" * 40)
    
    while True:
        print("\nOptions:")
        print("1. Show folder structure")
        print("2. Show chunk processing summary")
        print("3. Open main results folder")
        print("4. Open specific chunk folder")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == "1":
            show_folder_structure()
            
        elif choice == "2":
            show_chunk_summary()
            
        elif choice == "3":
            open_folder_in_explorer()
            
        elif choice == "4":
            results_path = Path("chunk_ui_results")
            if results_path.exists():
                chunk_folders = [f.name for f in results_path.iterdir() if f.is_dir() and f.name.startswith('chunk_')]
                if chunk_folders:
                    print(f"\nAvailable chunks: {', '.join(sorted(chunk_folders))}")
                    chunk_name = input("Enter chunk name: ").strip()
                    if chunk_name in chunk_folders:
                        open_folder_in_explorer(chunk_name=chunk_name)
                    else:
                        print("❌ Invalid chunk name")
                else:
                    print("❌ No chunk folders found")
            else:
                print("❌ Results directory not found")
                
        elif choice == "5":
            print("Goodbye!")
            break
            
        else:
            print("Invalid choice. Please enter 1-5.")


if __name__ == "__main__":
    main()
