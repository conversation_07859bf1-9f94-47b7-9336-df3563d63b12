"""
Dataset Chunker - Break clean dataset into manageable chunks
"""

import pandas as pd
import os
import math
from typing import List, Dict
from datetime import datetime

class DatasetChunker:
    """Break dataset into manageable chunks for processing."""
    
    def __init__(self, chunk_size: int = 1000):
        self.chunk_size = chunk_size
    
    def create_chunks(self, input_file: str, output_dir: str = None) -> Dict:
        """
        Break dataset into chunks.
        
        Args:
            input_file: Path to clean CSV file
            output_dir: Directory to save chunks (optional)
            
        Returns:
            Dictionary with chunk information
        """
        print(f"📦 CHUNKING DATASET: {input_file}")
        print("=" * 60)
        
        # Read the dataset
        print("📖 Reading clean dataset...")
        df = pd.read_csv(input_file)
        
        total_records = len(df)
        num_chunks = math.ceil(total_records / self.chunk_size)
        
        print(f"   • Total records: {total_records:,}")
        print(f"   • Chunk size: {self.chunk_size:,}")
        print(f"   • Number of chunks: {num_chunks}")
        
        # Create output directory
        if output_dir is None:
            base_name = os.path.splitext(os.path.basename(input_file))[0]
            output_dir = f"{base_name}_chunks"
        
        os.makedirs(output_dir, exist_ok=True)
        print(f"   • Output directory: {output_dir}")
        
        # Create chunks
        print(f"\n🔨 Creating {num_chunks} chunks...")
        chunk_files = []
        
        for i in range(num_chunks):
            start_idx = i * self.chunk_size
            end_idx = min((i + 1) * self.chunk_size, total_records)
            
            chunk_df = df.iloc[start_idx:end_idx].copy()
            chunk_filename = f"chunk_{i+1:03d}_of_{num_chunks:03d}.csv"
            chunk_path = os.path.join(output_dir, chunk_filename)
            
            # Save chunk
            chunk_df.to_csv(chunk_path, index=False)
            
            chunk_info = {
                'chunk_number': i + 1,
                'filename': chunk_filename,
                'path': chunk_path,
                'records': len(chunk_df),
                'start_idx': start_idx,
                'end_idx': end_idx - 1,
                'urls': chunk_df['website'].tolist()
            }
            
            chunk_files.append(chunk_info)
            
            print(f"   ✅ Chunk {i+1:3d}/{num_chunks}: {len(chunk_df):4d} records → {chunk_filename}")
        
        # Create chunk manifest
        manifest = {
            'created_at': datetime.now().isoformat(),
            'source_file': input_file,
            'total_records': total_records,
            'chunk_size': self.chunk_size,
            'num_chunks': num_chunks,
            'output_dir': output_dir,
            'chunks': chunk_files
        }
        
        manifest_path = os.path.join(output_dir, 'chunk_manifest.json')
        import json
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)
        
        print(f"\n📋 Chunk manifest saved: {manifest_path}")
        print(f"✅ SUCCESS! {num_chunks} chunks created in: {output_dir}")
        
        return manifest

def main():
    """Main function to chunk the dataset."""
    # Look for clean dataset file
    clean_files = [f for f in os.listdir('.') if f.endswith('_CLEAN.csv')]
    
    if not clean_files:
        print("❌ No clean dataset found!")
        print("Please run dataset_cleaner.py first to create a clean dataset.")
        return
    
    if len(clean_files) > 1:
        print("🤔 Multiple clean datasets found:")
        for i, file in enumerate(clean_files, 1):
            print(f"   {i}. {file}")
        
        try:
            choice = int(input("Select file number: ")) - 1
            input_file = clean_files[choice]
        except (ValueError, IndexError):
            print("Invalid choice. Using first file.")
            input_file = clean_files[0]
    else:
        input_file = clean_files[0]
    
    print(f"📂 Using clean dataset: {input_file}")
    
    # Ask for chunk size
    try:
        chunk_size = int(input("Enter chunk size (default 1000): ") or "1000")
    except ValueError:
        chunk_size = 1000
        print("Invalid input. Using default chunk size of 1000.")
    
    # Create chunks
    chunker = DatasetChunker(chunk_size=chunk_size)
    try:
        manifest = chunker.create_chunks(input_file)
        print(f"\n🎉 Chunking complete! Ready for scraping.")
        
    except Exception as e:
        print(f"❌ Error chunking dataset: {str(e)}")

if __name__ == "__main__":
    main()
