"""
Direct Scraper with Full Output Visibility
Run the scraper directly so you can see all the detailed output
"""

import asyncio
import os
import sys
from datetime import datetime

def main():
    """Run the scraper with full output visibility."""
    print("🚀 LEAD CONTACT SCRAPER WITH FULL OUTPUT")
    print("=" * 60)
    print(f"📂 Current directory: {os.getcwd()}")
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Look for chunk directories
    chunk_dirs = [d for d in os.listdir('.') if d.endswith('_chunks') and os.path.isdir(d)]
    
    if not chunk_dirs:
        print("❌ No chunk directories found!")
        print("Please run dataset_chunker.py first to create chunks.")
        input("Press Enter to exit...")
        return
    
    if len(chunk_dirs) > 1:
        print("🤔 Multiple chunk directories found:")
        for i, dir_name in enumerate(chunk_dirs, 1):
            print(f"   {i}. {dir_name}")
        
        try:
            choice = int(input("Select directory number: ")) - 1
            chunks_dir = chunk_dirs[choice]
        except (ValueError, IndexError):
            print("Invalid choice. Using first directory.")
            chunks_dir = chunk_dirs[0]
    else:
        chunks_dir = chunk_dirs[0]
    
    print(f"📦 Using chunks directory: {chunks_dir}")
    
    # Import and run the scraper
    try:
        from pausable_scraper import PausableScraper
        
        print("\n🔧 Initializing scraper...")
        scraper = PausableScraper(chunks_dir)
        
        print("🚀 Starting contact extraction...")
        print("⏸️  To pause: Press Ctrl+C or create PAUSE_SCRAPING file")
        print("=" * 60)
        
        # Run the scraper
        asyncio.run(scraper.run())
        
        print("\n" + "=" * 60)
        print("🎉 SCRAPING COMPLETED!")
        
        # Ask if user wants to combine results
        combine = input("\nCombine results now? (Y/n): ").lower().strip()
        if combine != 'n':
            print("\n🔗 Combining results...")
            try:
                from results_combiner import ResultsCombiner
                
                results_dir = f"{chunks_dir}_results"
                combiner = ResultsCombiner(results_dir)
                summary = combiner.combine_results()
                
                if summary:
                    print("✅ Results combined successfully!")
                    emails = summary['contacts_found']['emails']
                    phones_only = summary['segmentation']['companies_with_phones_only']
                    
                    print(f"\n📊 FINAL RESULTS:")
                    print(f"   📧 Companies with emails: {emails:,}")
                    print(f"   📞 Companies with phones only: {phones_only:,}")
                    print(f"   📁 Files created in current directory")
                
            except Exception as e:
                print(f"❌ Error combining results: {e}")
        
    except KeyboardInterrupt:
        print("\n⚠️  Scraping interrupted by user")
        print("Progress has been saved. You can resume later.")
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please make sure all required modules are installed.")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🕐 Finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
