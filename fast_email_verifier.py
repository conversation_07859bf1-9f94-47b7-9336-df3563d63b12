"""
Fast Email Verification using check-if-email-exists Docker Backend
High-concurrency email verification for lead generation.
"""

import asyncio
import aiohttp
import pandas as pd
import time
from datetime import datetime
from typing import List, Dict, Any
import json

class FastEmailVerifier:
    def __init__(self, backend_url: str = "http://localhost:8080", max_concurrent: int = 100):
        """
        Initialize the email verifier.

        Args:
            backend_url: URL of the check-if-email-exists backend
            max_concurrent: Maximum concurrent requests (default: 100 for maximum speed)
        """
        self.backend_url = backend_url
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.session = None

    async def __aenter__(self):
        """Async context manager entry."""
        # Max out connection limits for speed
        connector = aiohttp.TCPConnector(limit=200, limit_per_host=100, ttl_dns_cache=300)
        timeout = aiohttp.ClientTimeout(total=45, connect=10)
        self.session = aiohttp.ClientSession(connector=connector, timeout=timeout)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def verify_single_email(self, email: str) -> Dict[str, Any]:
        """
        Verify a single email address.
        
        Args:
            email: Email address to verify
            
        Returns:
            Dictionary with verification results
        """
        async with self.semaphore:
            try:
                payload = {"to_email": email.strip()}
                
                async with self.session.post(
                    f"{self.backend_url}/v0/check_email",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return self._parse_result(email, result)
                    else:
                        return self._error_result(email, f"HTTP {response.status}")
                        
            except asyncio.TimeoutError:
                return self._error_result(email, "Timeout")
            except Exception as e:
                return self._error_result(email, str(e))
    
    def _parse_result(self, email: str, result: Dict[str, Any]) -> Dict[str, Any]:
        """Parse the API response into a clean format."""
        return {
            'email': email,
            'is_reachable': result.get('is_reachable', 'unknown'),
            'is_valid_syntax': result.get('syntax', {}).get('is_valid_syntax', False),
            'domain': result.get('syntax', {}).get('domain', ''),
            'accepts_mail': result.get('mx', {}).get('accepts_mail', False),
            'can_connect_smtp': result.get('smtp', {}).get('can_connect_smtp', False),
            'is_deliverable': result.get('smtp', {}).get('is_deliverable', False),
            'is_disabled': result.get('smtp', {}).get('is_disabled', False),
            'is_catch_all': result.get('smtp', {}).get('is_catch_all', False),
            'has_full_inbox': result.get('smtp', {}).get('has_full_inbox', False),
            'is_disposable': result.get('misc', {}).get('is_disposable', False),
            'is_role_account': result.get('misc', {}).get('is_role_account', False),
            'mx_records': len(result.get('mx', {}).get('records', [])),
            'verification_time': datetime.now().isoformat(),
            'status': 'success'
        }
    
    def _error_result(self, email: str, error: str) -> Dict[str, Any]:
        """Create error result format."""
        return {
            'email': email,
            'is_reachable': 'unknown',
            'is_valid_syntax': False,
            'domain': '',
            'accepts_mail': False,
            'can_connect_smtp': False,
            'is_deliverable': False,
            'is_disabled': False,
            'is_catch_all': False,
            'has_full_inbox': False,
            'is_disposable': False,
            'is_role_account': False,
            'mx_records': 0,
            'verification_time': datetime.now().isoformat(),
            'status': f'error: {error}'
        }
    
    async def verify_emails_batch(self, emails: List[str]) -> List[Dict[str, Any]]:
        """
        Verify a batch of emails concurrently with real-time progress.

        Args:
            emails: List of email addresses to verify

        Returns:
            List of verification results
        """
        valid_emails = [email for email in emails if email.strip()]
        print(f"🚀 MAXIMUM SPEED VERIFICATION: {len(valid_emails)} emails with {self.max_concurrent} concurrent requests!")
        print(f"🔥 This is going to be FAST! Buckle up...")
        start_time = time.time()

        # Progress tracking
        completed = 0
        results = []

        async def verify_with_progress(email):
            nonlocal completed
            result = await self.verify_single_email(email)
            completed += 1

            # Show progress every 10 completions or for interesting results
            if completed % 10 == 0 or result['is_reachable'] in ['safe', 'risky']:
                elapsed = time.time() - start_time
                rate = completed / elapsed if elapsed > 0 else 0
                print(f"⚡ Progress: {completed}/{len(valid_emails)} ({rate:.1f}/sec) - Latest: {email} -> {result['is_reachable']}")

            return result

        # Create tasks for all emails
        tasks = [verify_with_progress(email) for email in valid_emails]

        # Execute all tasks concurrently
        raw_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle any exceptions
        for i, result in enumerate(raw_results):
            if isinstance(result, Exception):
                results.append(self._error_result(valid_emails[i], str(result)))
            else:
                results.append(result)

        end_time = time.time()
        duration = end_time - start_time
        rate = len(valid_emails) / duration if duration > 0 else 0

        print(f"\n🎉 SPEED DEMON COMPLETE!")
        print(f"✅ Processed {len(valid_emails)} emails in {duration:.2f} seconds")
        print(f"⚡ BLAZING RATE: {rate:.2f} emails/second")
        print(f"🔥 That's {rate*60:.0f} emails per minute!")

        return results
    
    def analyze_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze verification results and provide summary statistics."""
        total = len(results)
        if total == 0:
            return {}
        
        # Count by reachability
        reachable_counts = {}
        for result in results:
            status = result['is_reachable']
            reachable_counts[status] = reachable_counts.get(status, 0) + 1
        
        # Count valid/invalid syntax
        valid_syntax = sum(1 for r in results if r['is_valid_syntax'])
        
        # Count deliverable emails
        deliverable = sum(1 for r in results if r['is_deliverable'])
        
        # Count disposable emails
        disposable = sum(1 for r in results if r['is_disposable'])
        
        # Count role accounts
        role_accounts = sum(1 for r in results if r['is_role_account'])
        
        # Count errors
        errors = sum(1 for r in results if r['status'].startswith('error'))
        
        analysis = {
            'total_emails': total,
            'reachability_breakdown': reachable_counts,
            'valid_syntax': valid_syntax,
            'valid_syntax_percentage': (valid_syntax / total) * 100,
            'deliverable': deliverable,
            'deliverable_percentage': (deliverable / total) * 100,
            'disposable': disposable,
            'disposable_percentage': (disposable / total) * 100,
            'role_accounts': role_accounts,
            'role_accounts_percentage': (role_accounts / total) * 100,
            'errors': errors,
            'error_percentage': (errors / total) * 100,
            'success_rate': ((total - errors) / total) * 100
        }
        
        return analysis

async def main():
    """Main function to run email verification on test dataset."""
    print("🔥 FAST EMAIL VERIFICATION SYSTEM")
    print("=" * 50)
    print("Using check-if-email-exists Docker backend")
    print("High-concurrency parallel processing")
    
    # Load test emails
    try:
        df = pd.read_csv('test_emails.csv')
        emails = df['email'].dropna().tolist()
        print(f"📧 Loaded {len(emails)} emails from test_emails.csv")
    except Exception as e:
        print(f"❌ Error loading CSV: {e}")
        return
    
    # Verify emails with maximum concurrency
    async with FastEmailVerifier(max_concurrent=100) as verifier:
        results = await verifier.verify_emails_batch(emails)
        
        # Analyze results
        analysis = verifier.analyze_results(results)
        
        # Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f'email_verification_results_{timestamp}.csv'
        
        results_df = pd.DataFrame(results)
        results_df.to_csv(output_file, index=False)
        
        print(f"\n💾 Results saved to: {output_file}")
        
        # Print analysis
        print("\n📊 VERIFICATION ANALYSIS")
        print("=" * 30)
        print(f"Total emails processed: {analysis['total_emails']}")
        print(f"Success rate: {analysis['success_rate']:.1f}%")
        print(f"Valid syntax: {analysis['valid_syntax']} ({analysis['valid_syntax_percentage']:.1f}%)")
        print(f"Deliverable: {analysis['deliverable']} ({analysis['deliverable_percentage']:.1f}%)")
        print(f"Disposable: {analysis['disposable']} ({analysis['disposable_percentage']:.1f}%)")
        print(f"Role accounts: {analysis['role_accounts']} ({analysis['role_accounts_percentage']:.1f}%)")
        print(f"Errors: {analysis['errors']} ({analysis['error_percentage']:.1f}%)")
        
        print("\n🎯 REACHABILITY BREAKDOWN:")
        for status, count in analysis['reachability_breakdown'].items():
            percentage = (count / analysis['total_emails']) * 100
            if status == 'safe':
                print(f"  ✅ {status}: {count} ({percentage:.1f}%) - GOOD TO EMAIL!")
            elif status == 'risky':
                print(f"  ⚠️  {status}: {count} ({percentage:.1f}%) - Proceed with caution")
            elif status == 'invalid':
                print(f"  ❌ {status}: {count} ({percentage:.1f}%) - Don't email these")
            elif status == 'unknown':
                print(f"  ❓ {status}: {count} ({percentage:.1f}%) - Verification inconclusive")
            else:
                print(f"  🔍 {status}: {count} ({percentage:.1f}%)")

        # Show some examples of each category
        print("\n📋 SAMPLE RESULTS BY CATEGORY:")
        categories = {}
        for result in results[:20]:  # Look at first 20 for examples
            status = result['is_reachable']
            if status not in categories:
                categories[status] = []
            if len(categories[status]) < 3:  # Max 3 examples per category
                categories[status].append(result['email'])

        for status, emails in categories.items():
            print(f"  {status}: {', '.join(emails)}")

if __name__ == "__main__":
    asyncio.run(main())
