version: "3.8"

services:
    rabbitmq:
        image: rabbitmq:4.0-management
        container_name: rabbitmq
        ports:
            - "5672:5672"
            - "15672:15672"
        environment:
            RABBITMQ_DEFAULT_USER: guest
            RABBITMQ_DEFAULT_PASS: guest
        restart: always

    postgres:
        image: postgres:14
        container_name: postgres
        ports:
            - "5432:5432"
        environment:
            POSTGRES_USER: postgres
            POSTGRES_PASSWORD: postgres
            POSTGRES_DB: reacher_db
        restart: always

    worker1:
        image: reacherhq/backend:beta
        container_name: worker1
        ports:
            - "8080:8080"
        depends_on:
            - postgres
            - rabbitmq
        environment:
            RCH__BACKEND_NAME: worker1
            RUST_LOG: reacher=info
            RCH__WORKER__ENABLE: true
            RCH__WORKER__RABBITMQ__URL: amqp://guest:guest@rabbitmq:5672
            RCH__STORAGE__POSTGRES__DB_URL: ******************************************/reacher_db
            RCH__WORKER__THROTTLE__MAX_REQUESTS_PER_DAY: 10000 # Recommended limit per IP per day for SMTP requests
        restart: always

    worker2:
        image: reacherhq/backend:beta
        container_name: worker2
        ports:
            - "8081:8080"
        depends_on:
            - postgres
            - rabbitmq
        environment:
            RCH__BACKEND_NAME: worker2
            RUST_LOG: reacher=info
            RCH__WORKER__ENABLE: true
            RCH__WORKER__RABBITMQ__URL: amqp://guest:guest@rabbitmq:5672
            RCH__STORAGE__POSTGRES__DB_URL: ******************************************/reacher_db
            RCH__WORKER__THROTTLE__MAX_REQUESTS_PER_DAY: 10000 # Recommended limit per IP per day for SMTP requests
        restart: always
