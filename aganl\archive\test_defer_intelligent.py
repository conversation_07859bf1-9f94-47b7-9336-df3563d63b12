"""
Test the intelligent extractor specifically on <PERSON><PERSON> Coffee to ensure it finds the email
"""

import asyncio
from intelligent_contact_extractor import IntelligentContactExtractor


async def test_defer_detailed():
    """Test Defer Coffee with detailed logging to see what's happening."""
    print("🔍 DETAILED DEFER COFFEE TEST")
    print("=" * 50)
    print("URL: https://www.defer.coffee/strip-district")
    print("Expected: Should discover and check contact pages")
    print("=" * 50)
    
    extractor = IntelligentContactExtractor(batch_size=5, max_concurrent=2)
    results = await extractor.extract_intelligent(['https://www.defer.coffee/strip-district'])
    
    result = results[0]
    
    if 'error' not in result:
        email = result.get('email')
        social = result.get('social_media')
        pages_discovered = result.get('pages_discovered', 0)
        pages_checked = result.get('pages_checked', 0)
        
        print(f"\n📊 DEFER COFFEE DETAILED RESULTS:")
        print(f"   🔗 URL: {result['url']}")
        print(f"   📄 Pages discovered: {pages_discovered}")
        print(f"   🔍 Pages checked: {pages_checked}")
        
        if email:
            print(f"   ✅ Email: {email['email']} (confidence: {email['confidence']:.1%})")
        else:
            print(f"   ❌ Email: Not found")
        
        if social:
            print(f"   ✅ Social: {social['platform']} {social.get('handle', '')} (confidence: {social['confidence']:.1%})")
        else:
            print(f"   ❌ Social: Not found")
        
        if not email:
            print(f"\n⚠️  ISSUE: Still no email found!")
            print(f"   This suggests the contact page isn't being checked properly")
            print(f"   or the email isn't in a mailto: link format")
    
    else:
        print(f"❌ ERROR: {result['error']}")
    
    return results


async def test_multiple_with_known_emails():
    """Test multiple sites where we know emails should be found."""
    print("\n🔍 TESTING MULTIPLE SITES WITH KNOWN EMAILS")
    print("=" * 60)
    
    # Sites where we know there should be emails
    test_urls = [
        "https://www.defer.coffee/strip-district",  # Should have email on contact page
        "https://thejamescafe.com/",               # Should have email on main page
        "https://convivecoffee.com/",              # Should have email
        "http://www.yinzcoffee.com/"               # Should have email
    ]
    
    print(f"Testing {len(test_urls)} sites with detailed logging...")
    
    extractor = IntelligentContactExtractor(batch_size=10, max_concurrent=3)
    results = await extractor.extract_intelligent(test_urls)
    
    print(f"\n📊 DETAILED RESULTS COMPARISON:")
    
    for i, result in enumerate(results, 1):
        if 'error' in result:
            print(f"{i}. ❌ {result['url']}: {result['error']}")
            continue
        
        url = result['url']
        email = result.get('email')
        social = result.get('social_media')
        pages_discovered = result.get('pages_discovered', 0)
        pages_checked = result.get('pages_checked', 0)
        
        domain = url.split('/')[2].replace('www.', '')
        
        print(f"\n{i}. 🏪 {domain}")
        print(f"   📄 Pages discovered: {pages_discovered}")
        print(f"   🔍 Pages checked: {pages_checked}")
        
        if email:
            print(f"   ✅ Email: {email['email']} (confidence: {email['confidence']:.1%})")
        else:
            print(f"   ❌ Email: Not found")
        
        if social:
            print(f"   ✅ Social: {social['platform']} {social.get('handle', '')} (confidence: {social['confidence']:.1%})")
        else:
            print(f"   ❌ Social: Not found")
        
        # Analysis
        if not email and pages_discovered > 1:
            print(f"   ⚠️  CONCERN: {pages_discovered} pages discovered but no email found")
        elif not email and pages_discovered == 1:
            print(f"   ℹ️  INFO: Only main page found, may not have contact page")
    
    # Summary
    successful = len([r for r in results if 'error' not in r])
    emails_found = len([r for r in results if 'error' not in r and r.get('email')])
    
    print(f"\n📈 SUMMARY:")
    print(f"   • URLs tested: {len(test_urls)}")
    print(f"   • Successful: {successful}")
    print(f"   • Emails found: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)" if successful > 0 else "")
    
    if emails_found < successful:
        print(f"\n🔧 RECOMMENDATIONS:")
        print(f"   • Check if contact pages are being properly discovered")
        print(f"   • Verify email extraction from contact page content")
        print(f"   • Consider checking for emails in plain text, not just mailto: links")
    
    return results


async def main():
    """Main test function."""
    print("🔍 INTELLIGENT EXTRACTOR - DEFER COFFEE FOCUS TEST")
    print("Debugging why Defer Coffee email isn't being found")
    print("=" * 60)
    
    try:
        # Test Defer Coffee specifically
        await test_defer_detailed()
        
        # Test comparison with other sites
        await test_multiple_with_known_emails()
        
        print(f"\n🎯 CONCLUSION:")
        print("If Defer Coffee still shows no email, the issue is likely:")
        print("1. Contact page not being discovered (HEAD request fails)")
        print("2. Email not in mailto: link format on contact page")
        print("3. Need to add regex fallback for plain text emails")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n🔍 Test interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
