"""
Test the improved contact extractor with coffee shop data from test_data.csv
"""

import asyncio
import csv
from datetime import datetime
from typing import List
from improved_contact_extractor import ImprovedContactExtractor


def load_coffee_shop_urls(csv_file: str = "test_data.csv") -> List[str]:
    """Load URLs from the CSV file."""
    urls = []
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                url = row.get('website_url', '').strip()
                if url and url.startswith(('http://', 'https://')):
                    urls.append(url)
        print(f"📋 Loaded {len(urls)} coffee shop URLs from {csv_file}")
        return urls
    except FileNotFoundError:
        print(f"❌ Error: {csv_file} not found")
        return []
    except Exception as e:
        print(f"❌ Error loading URLs: {str(e)}")
        return []


async def test_single_coffee_shop():
    """Test extraction from a single coffee shop."""
    print("☕ Testing Single Coffee Shop")
    print("=" * 50)
    
    urls = load_coffee_shop_urls()
    if not urls:
        print("No URLs to test")
        return
    
    # Test the first URL
    test_url = urls[0]
    print(f"Testing: {test_url}")
    
    extractor = ImprovedContactExtractor()
    results = await extractor.extract_contacts([test_url])
    
    if results:
        result = results[0]
        if 'error' not in result:
            print("\n🎉 Extraction completed successfully!")
            
            # Export results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            extractor.export_to_json(results, f"single_coffee_shop_{timestamp}.json")
            extractor.export_to_csv(results, f"single_coffee_shop_{timestamp}.csv")
        else:
            print(f"❌ Error: {result['error']}")


async def test_multiple_coffee_shops(max_shops: int = 5):
    """Test extraction from multiple coffee shops."""
    print(f"☕ Testing Multiple Coffee Shops (max {max_shops})")
    print("=" * 50)
    
    urls = load_coffee_shop_urls()
    if not urls:
        print("No URLs to test")
        return
    
    # Limit the number of URLs to test
    test_urls = urls[:max_shops]
    print(f"Testing {len(test_urls)} coffee shops...")
    
    extractor = ImprovedContactExtractor()
    results = await extractor.extract_contacts(test_urls)
    
    # Generate summary
    print(f"\n{'='*60}")
    print("COFFEE SHOP CONTACT EXTRACTION SUMMARY")
    print(f"{'='*60}")
    
    successful = 0
    total_emails = 0
    total_phones = 0
    total_addresses = 0
    total_social = 0
    shops_with_contacts = 0
    
    for result in results:
        if 'error' in result:
            print(f"❌ {result['url']}: {result['error']}")
            continue
        
        successful += 1
        contacts = result.get('contacts', {})
        
        email_count = len(contacts.get('emails', []))
        phone_count = len(contacts.get('phones', []))
        address_count = len(contacts.get('addresses', []))
        social_count = len(contacts.get('social_media', []))
        
        total_emails += email_count
        total_phones += phone_count
        total_addresses += address_count
        total_social += social_count
        
        if email_count + phone_count + address_count + social_count > 0:
            shops_with_contacts += 1
        
        business_name = contacts.get('business_name', 'Unknown')[:30]
        print(f"✅ {business_name}: 📧{email_count} 📞{phone_count} 📍{address_count} 🌐{social_count}")
    
    print(f"\n📊 SUMMARY STATISTICS:")
    print(f"   • Total shops processed: {len(test_urls)}")
    print(f"   • Successful extractions: {successful}")
    print(f"   • Shops with contact info: {shops_with_contacts}")
    print(f"   • Success rate: {(successful/len(test_urls)*100):.1f}%")
    print(f"   • Contact discovery rate: {(shops_with_contacts/successful*100):.1f}%" if successful > 0 else "   • Contact discovery rate: 0%")
    
    print(f"\n📞 TOTAL CONTACTS FOUND:")
    print(f"   • Emails: {total_emails}")
    print(f"   • Phone numbers: {total_phones}")
    print(f"   • Addresses: {total_addresses}")
    print(f"   • Social media: {total_social}")
    print(f"   • Total contact items: {total_emails + total_phones + total_addresses + total_social}")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    extractor.export_to_json(results, f"coffee_shops_contacts_{timestamp}.json")
    extractor.export_to_csv(results, f"coffee_shops_contacts_{timestamp}.csv")
    
    return results


async def test_specific_coffee_shops():
    """Test specific coffee shops that are likely to have good contact info."""
    print("☕ Testing Specific Coffee Shops")
    print("=" * 50)
    
    # These URLs are more likely to have contact information
    priority_urls = [
        "https://thejamescafe.com/",
        "https://www.centredcoffee.com/",
        "https://redstartroasters.com/",
        "https://convivecoffee.com/"
    ]
    
    print(f"Testing {len(priority_urls)} priority coffee shops...")
    
    extractor = ImprovedContactExtractor()
    results = await extractor.extract_contacts(priority_urls)
    
    # Show detailed results for each
    for result in results:
        if 'error' not in result:
            contacts = result.get('contacts', {})
            business_name = contacts.get('business_name', 'Unknown Business')
            
            print(f"\n🏪 {business_name}")
            print(f"   URL: {result['url']}")
            
            emails = contacts.get('emails', [])
            if emails:
                print(f"   📧 Emails: {', '.join([e['email'] for e in emails])}")
            
            phones = contacts.get('phones', [])
            if phones:
                print(f"   📞 Phones: {', '.join([p['phone'] for p in phones])}")
            
            addresses = contacts.get('addresses', [])
            if addresses:
                for addr in addresses:
                    print(f"   📍 Address: {addr['address'][:80]}...")
            
            social_media = contacts.get('social_media', [])
            if social_media:
                for social in social_media:
                    print(f"   🌐 {social['platform'].title()}: {social.get('handle', social['url'])}")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    extractor.export_to_json(results, f"priority_coffee_shops_{timestamp}.json")
    extractor.export_to_csv(results, f"priority_coffee_shops_{timestamp}.csv")
    
    return results


async def main():
    """Main function to run coffee shop contact extraction tests."""
    print("☕ COFFEE SHOP CONTACT EXTRACTOR")
    print("Using crawl4ai with improved validation")
    print("=" * 60)
    
    print("\nChoose test mode:")
    print("1. Test single coffee shop (detailed)")
    print("2. Test 5 coffee shops (summary)")
    print("3. Test priority coffee shops (detailed)")
    print("4. Test all coffee shops (batch)")
    
    try:
        choice = input("\nEnter choice (1-4) or press Enter for option 1: ").strip()
        if not choice:
            choice = "1"
    except KeyboardInterrupt:
        print("\n\nTest cancelled by user.")
        return
    
    start_time = datetime.now()
    
    try:
        if choice == "1":
            await test_single_coffee_shop()
        elif choice == "2":
            await test_multiple_coffee_shops(5)
        elif choice == "3":
            await test_specific_coffee_shops()
        elif choice == "4":
            urls = load_coffee_shop_urls()
            await test_multiple_coffee_shops(len(urls))
        else:
            print("Invalid choice. Using option 1.")
            await test_single_coffee_shop()
    
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print(f"\n⏱️  Total execution time: {duration:.2f} seconds")
    print("\n🎉 Coffee shop contact extraction completed!")
    print("Check the generated JSON and CSV files for detailed results.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n☕ Coffee break! Test interrupted by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        print("Please check your internet connection and try again.")
