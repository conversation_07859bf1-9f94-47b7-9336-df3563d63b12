"""
Test script to simulate a longer run and identify potential issues.
"""

import asyncio
import pandas as pd
from datetime import datetime
from aganl.perfect_contact_extractor import PerfectContactExtractor

async def test_extended_run():
    """Test with more URLs to simulate longer runs and identify issues."""
    print("🔍 TESTING EXTENDED RUN (50 URLs)")
    print("=" * 50)
    
    # Load more URLs from the dataset
    try:
        df = pd.read_csv("pittsburgh_coffee_prospects_FINAL_20250914_164553.csv")
        urls = df['website'].dropna().head(50).tolist()  # Test 50 URLs
        print(f"Loaded {len(urls)} URLs from dataset")
        
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return
    
    print(f"\n🚀 Testing with production settings...")
    print(f"   • Batch size: 15")
    print(f"   • Max concurrent: 20")
    print(f"   • Expected time: ~{len(urls) * 2 / 60:.1f} minutes")
    
    # Use the same settings as the overnight script
    extractor = PerfectContactExtractor(batch_size=15, max_concurrent=20)
    
    start_time = datetime.now()
    
    try:
        results = await extractor.extract_perfect(urls)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n📊 EXTENDED RUN RESULTS:")
        success_count = 0
        error_count = 0
        error_types = {}
        emails_found = 0
        phones_found = 0
        
        for result in results:
            url = result.get('url', 'Unknown')
            if 'error' in result:
                error_count += 1
                error = result['error']
                
                # Categorize error types
                if 'DNS resolution failed' in error or 'ERR_NAME_NOT_RESOLVED' in error:
                    error_types['DNS Issues'] = error_types.get('DNS Issues', 0) + 1
                elif 'Connection failed' in error or 'Connection aborted' in error or 'ERR_CONNECTION' in error:
                    error_types['Connection Issues'] = error_types.get('Connection Issues', 0) + 1
                elif 'timeout' in error.lower() or 'ERR_CONNECTION_TIMED_OUT' in error:
                    error_types['Timeouts'] = error_types.get('Timeouts', 0) + 1
                elif 'SSL' in error or 'ERR_SSL' in error:
                    error_types['SSL Issues'] = error_types.get('SSL Issues', 0) + 1
                elif 'Navigation failed' in error:
                    error_types['Navigation Issues'] = error_types.get('Navigation Issues', 0) + 1
                else:
                    error_types['Other'] = error_types.get('Other', 0) + 1
                    
                print(f"❌ {url}: {error}")
            else:
                success_count += 1
                if result.get('email'):
                    emails_found += 1
                if result.get('phone'):
                    phones_found += 1
        
        success_rate = (success_count / len(results)) * 100 if results else 0
        
        print(f"\n📈 EXTENDED RUN SUMMARY:")
        print(f"⏱️  Duration: {duration}")
        print(f"⚡ Rate: {len(results) / duration.total_seconds():.2f} URLs/second")
        print(f"✅ Successful: {success_count} ({success_rate:.1f}%)")
        print(f"❌ Failed: {error_count} ({100-success_rate:.1f}%)")
        print(f"📧 Emails found: {emails_found}")
        print(f"📞 Phones found: {phones_found}")
        
        if error_types:
            print(f"\n🔍 ERROR BREAKDOWN:")
            for error_type, count in error_types.items():
                print(f"   • {error_type}: {count}")
        
        # Analyze error patterns
        if error_count > 0:
            print(f"\n🔍 ERROR ANALYSIS:")
            if error_count > len(results) * 0.1:  # More than 10% errors
                print("⚠️  HIGH ERROR RATE DETECTED!")
                print("   This suggests systemic issues that need addressing.")
            else:
                print("✅ Error rate is within acceptable range.")
                print("   Most errors are likely due to problematic websites.")
        
        return results
        
    except Exception as e:
        print(f"❌ CRITICAL ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

async def test_problematic_urls():
    """Test some potentially problematic URLs to see common error patterns."""
    print("\n🔍 TESTING POTENTIALLY PROBLEMATIC URLs")
    print("=" * 50)
    
    # Some URLs that might cause issues
    problematic_urls = [
        "http://nonexistentdomain12345.com",  # DNS failure
        "https://httpstat.us/500",  # Server error
        "https://httpstat.us/timeout",  # Timeout
        "https://expired.badssl.com/",  # SSL issue
        "https://self-signed.badssl.com/",  # SSL issue
    ]
    
    print(f"Testing {len(problematic_urls)} potentially problematic URLs...")
    
    extractor = PerfectContactExtractor(batch_size=1, max_concurrent=1)
    
    try:
        results = await extractor.extract_perfect(problematic_urls)
        
        print(f"\n📊 PROBLEMATIC URL RESULTS:")
        for result in results:
            url = result.get('url', 'Unknown')
            if 'error' in result:
                print(f"❌ {url}")
                print(f"   Error: {result['error']}")
            else:
                print(f"✅ {url} (unexpected success)")
        
        return results
        
    except Exception as e:
        print(f"❌ Error testing problematic URLs: {str(e)}")
        return None

async def main():
    """Run extended tests."""
    print(f"🚀 EXTENDED SCRAPING TEST")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Test 1: Extended run with real URLs
    results1 = await test_extended_run()
    
    # Test 2: Test problematic URLs
    results2 = await test_problematic_urls()
    
    print(f"\n✅ EXTENDED TESTING COMPLETE")
    
    if results1:
        error_count = len([r for r in results1 if 'error' in r])
        total_count = len(results1)
        
        print(f"\n🎯 FINAL ASSESSMENT:")
        if error_count == 0:
            print("✅ NO ERRORS DETECTED - Script should work perfectly")
        elif error_count < total_count * 0.05:  # Less than 5%
            print("✅ MINIMAL ERRORS - Script is working well")
            print("   Errors are likely due to problematic websites")
        elif error_count < total_count * 0.15:  # Less than 15%
            print("⚠️  MODERATE ERRORS - Some optimization may be needed")
        else:
            print("❌ HIGH ERROR RATE - Significant issues detected")
            
        print(f"\n💡 RECOMMENDATIONS:")
        if error_count > 0:
            print("   • Add more robust retry logic for failed URLs")
            print("   • Consider reducing concurrency for problematic sites")
            print("   • Implement exponential backoff for rate limiting")
        else:
            print("   • Current settings appear optimal")
            print("   • Script should handle overnight runs successfully")

if __name__ == "__main__":
    asyncio.run(main())
