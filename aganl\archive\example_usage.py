"""
Example usage of the Contact Extractor using crawl4ai
Demonstrates various extraction strategies and export options.
"""

import asyncio
import json
from contact_extractor import ContactExtractor, extract_contacts_from_url


async def basic_example():
    """Basic example of extracting contacts from a single URL."""
    print("=== Basic Contact Extraction Example ===")
    
    # Example URL (replace with actual website)
    url = "https://example.com/contact"
    
    try:
        # Quick extraction using the convenience function
        result = await extract_contacts_from_url(url, strategy="comprehensive")
        
        print(f"Extracted contacts from: {result.get('url', 'Unknown')}")
        print(f"Page title: {result.get('title', 'No title')}")
        print(f"Extraction strategy: {result.get('strategy', 'Unknown')}")
        
        contacts = result.get('contacts', {})
        
        # Display emails
        emails = contacts.get('emails', [])
        if emails:
            print(f"\nFound {len(emails)} email(s):")
            for email_info in emails:
                print(f"  - {email_info.get('email', 'N/A')} (confidence: {email_info.get('confidence', 'N/A')})")
        
        # Display phones
        phones = contacts.get('phones', [])
        if phones:
            print(f"\nFound {len(phones)} phone number(s):")
            for phone_info in phones:
                print(f"  - {phone_info.get('phone', 'N/A')} (confidence: {phone_info.get('confidence', 'N/A')})")
        
        # Display addresses
        addresses = contacts.get('addresses', [])
        if addresses:
            print(f"\nFound {len(addresses)} address(es):")
            for addr_info in addresses:
                print(f"  - {addr_info.get('address', 'N/A')} (confidence: {addr_info.get('confidence', 'N/A')})")
        
        # Display social media
        social_media = contacts.get('social_media', [])
        if social_media:
            print(f"\nFound {len(social_media)} social media link(s):")
            for social_info in social_media:
                print(f"  - {social_info.get('platform', 'Unknown')}: {social_info.get('url', 'N/A')}")
        
        return result
        
    except Exception as e:
        print(f"Error in basic example: {str(e)}")
        return None


async def advanced_example():
    """Advanced example with multiple URLs and different strategies."""
    print("\n=== Advanced Contact Extraction Example ===")
    
    # Multiple URLs to extract from
    urls = [
        "https://example.com/contact",
        "https://example.com/about",
        "https://example.com/team"
    ]
    
    # Initialize extractor
    extractor = ContactExtractor(use_llm=False)  # Set to True if you have LLM API configured
    
    try:
        # Extract using comprehensive strategy
        results = await extractor.extract_contacts(urls, strategy="comprehensive")
        
        print(f"Processed {len(results)} URLs")
        
        all_contacts = {
            'emails': set(),
            'phones': set(),
            'addresses': set(),
            'social_media': []
        }
        
        # Aggregate results from all URLs
        for result in results:
            if 'error' in result:
                print(f"Error processing {result.get('url', 'Unknown')}: {result.get('error', 'Unknown error')}")
                continue
            
            contacts = result.get('contacts', {})
            
            # Collect unique emails
            for email_info in contacts.get('emails', []):
                all_contacts['emails'].add(email_info.get('email', ''))
            
            # Collect unique phones
            for phone_info in contacts.get('phones', []):
                all_contacts['phones'].add(phone_info.get('phone', ''))
            
            # Collect unique addresses
            for addr_info in contacts.get('addresses', []):
                all_contacts['addresses'].add(addr_info.get('address', ''))
            
            # Collect social media (may have duplicates, but that's OK)
            all_contacts['social_media'].extend(contacts.get('social_media', []))
        
        # Display aggregated results
        print(f"\n=== Aggregated Results ===")
        print(f"Total unique emails: {len(all_contacts['emails'])}")
        print(f"Total unique phones: {len(all_contacts['phones'])}")
        print(f"Total unique addresses: {len(all_contacts['addresses'])}")
        print(f"Total social media links: {len(all_contacts['social_media'])}")
        
        # Export results
        extractor.export_to_json(results, "extracted_contacts.json")
        extractor.export_to_csv(results, "extracted_contacts.csv")
        
        return results
        
    except Exception as e:
        print(f"Error in advanced example: {str(e)}")
        return None


async def strategy_comparison():
    """Compare different extraction strategies."""
    print("\n=== Strategy Comparison Example ===")
    
    url = "https://example.com/contact"
    extractor = ContactExtractor()
    
    strategies = ["css", "regex", "comprehensive"]
    results = {}
    
    for strategy in strategies:
        try:
            print(f"\nTesting {strategy} strategy...")
            result = await extractor.extract_contacts([url], strategy=strategy)
            results[strategy] = result[0] if result else {}
            
            contacts = results[strategy].get('contacts', {})
            email_count = len(contacts.get('emails', []))
            phone_count = len(contacts.get('phones', []))
            address_count = len(contacts.get('addresses', []))
            
            print(f"  Emails: {email_count}, Phones: {phone_count}, Addresses: {address_count}")
            
        except Exception as e:
            print(f"  Error with {strategy} strategy: {str(e)}")
            results[strategy] = {"error": str(e)}
    
    return results


async def custom_extraction_demo():
    """Demonstrate custom extraction patterns."""
    print("\n=== Custom Extraction Demo ===")
    
    # You can customize the extraction strategies
    from crawl4ai import JsonCssExtractionStrategy, RegexExtractionStrategy
    
    # Custom CSS strategy for specific website structure
    custom_css_strategy = JsonCssExtractionStrategy({
        "contact_section": {
            "selector": "#contact, .contact-info, .contact-section",
            "attribute": "text"
        },
        "team_members": {
            "selector": ".team-member, .staff-card, .employee",
            "attribute": "text"
        },
        "footer_contacts": {
            "selector": "footer a[href^='mailto:'], footer a[href^='tel:']",
            "attribute": "href"
        }
    })
    
    # Custom regex patterns
    custom_regex_strategy = RegexExtractionStrategy({
        "business_hours": r'(?:hours?|open|closed).*?(?:\d{1,2}:\d{2}.*?\d{1,2}:\d{2}|24/7|24 hours)',
        "fax_numbers": r'(?:fax|facsimile).*?(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})',
        "po_boxes": r'P\.?O\.?\s*Box\s*\d+'
    })
    
    print("Custom extraction strategies created successfully!")
    print("You can use these in your ContactExtractor by modifying the _setup_extraction_strategies method")
    
    return {
        "custom_css": custom_css_strategy,
        "custom_regex": custom_regex_strategy
    }


async def main():
    """Main function to run all examples."""
    print("Contact Extractor Examples using crawl4ai")
    print("=" * 50)
    
    # Run basic example
    basic_result = await basic_example()
    
    # Run advanced example
    advanced_result = await advanced_example()
    
    # Compare strategies
    strategy_results = await strategy_comparison()
    
    # Custom extraction demo
    custom_demo = await custom_extraction_demo()
    
    print("\n" + "=" * 50)
    print("All examples completed!")
    
    # Summary
    if basic_result:
        print(f"Basic extraction found contacts from: {basic_result.get('url', 'Unknown')}")
    
    if advanced_result:
        print(f"Advanced extraction processed {len(advanced_result)} URLs")
    
    print("Check the generated files: extracted_contacts.json and extracted_contacts.csv")


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
