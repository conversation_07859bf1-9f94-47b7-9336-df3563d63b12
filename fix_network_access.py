"""
Fix Network Access for Email Verification
This will restart Docker with host network mode to bypass port 25 blocking
"""

import subprocess
import time
import requests

def stop_current_backend():
    """Stop current backend."""
    print("🛑 Stopping current backend...")
    try:
        subprocess.run(['docker', 'stop', 'email-verifier-improved'], capture_output=True)
        subprocess.run(['docker', 'rm', 'email-verifier-improved'], capture_output=True)
        print("✅ Current backend stopped")
    except Exception as e:
        print(f"⚠️  Error stopping backend: {e}")

def start_host_network_backend():
    """Start backend with host network mode for direct SMTP access."""
    print("🌐 Starting backend with HOST NETWORK MODE...")
    print("This bypasses Docker network restrictions for SMTP port 25")
    
    try:
        # Use host network mode - this is key for SMTP access
        cmd = [
            'docker', 'run', '-d',
            '--name', 'email-verifier-host',
            '--network', 'host',  # This is the magic fix!
            '-e', 'RCH__HELLO_NAME=orchidwebsolution.com',
            '-e', 'RCH__FROM_EMAIL=<EMAIL>',
            '-e', 'RCH__SMTP_TIMEOUT=60',
            '-e', 'RCH__HTTP_HOST=127.0.0.1',
            '-e', 'RCH__HTTP_PORT=8080',
            'reacherhq/backend:latest'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            container_id = result.stdout.strip()
            print(f"✅ Host network backend started! Container ID: {container_id[:12]}")
            print("🔥 Now using your host's network directly - SMTP should work!")
            return True
        else:
            print(f"❌ Failed to start host network backend: {result.stderr}")
            return False
                
    except Exception as e:
        print(f"❌ Error starting host network backend: {e}")
        return False

def wait_for_backend(max_wait=60):
    """Wait for backend to be ready."""
    print("⏳ Waiting for host network backend...")
    
    for i in range(max_wait):
        try:
            response = requests.get("http://localhost:8080/v0/check_email", timeout=5)
            if response.status_code == 405:
                print("✅ Host network backend is ready!")
                return True
        except requests.exceptions.RequestException:
            pass
        
        if i % 10 == 0 and i > 0:
            print(f"   Still waiting... ({i}s)")
        
        time.sleep(1)
    
    print("❌ Backend did not start within 60 seconds")
    return False

def test_smtp_connectivity():
    """Test if SMTP connectivity is working now."""
    print("🧪 Testing SMTP connectivity...")
    
    try:
        import socket
        
        # Test if we can reach Gmail's SMTP server
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex(('aspmx.l.google.com', 25))
        sock.close()
        
        if result == 0:
            print("✅ SMTP connectivity test PASSED!")
            print("🎉 Port 25 is accessible - email verification should work!")
            return True
        else:
            print("❌ SMTP connectivity test FAILED")
            print("⚠️  Your ISP/network is blocking port 25")
            print("💡 You may need a VPN or different network")
            return False
            
    except Exception as e:
        print(f"❌ SMTP test error: {e}")
        return False

def main():
    """Main function to fix network access."""
    print("🔧 FIXING NETWORK ACCESS FOR EMAIL VERIFICATION")
    print("=" * 55)
    print("Problem: Docker network blocking SMTP port 25")
    print("Solution: Use host network mode for direct access")
    print()
    
    # Test current SMTP connectivity
    if not test_smtp_connectivity():
        print("\n⚠️  WARNING: Your network blocks port 25")
        print("This is common with ISPs and corporate networks")
        print("Solutions:")
        print("1. 🌐 Try a VPN")
        print("2. 🏢 Use a different network")
        print("3. ☁️  Run on a VPS/cloud server")
        print("\nContinuing anyway - let's see if host mode helps...")
    
    # Stop current backend
    stop_current_backend()
    
    # Start with host network
    if not start_host_network_backend():
        print("❌ Failed to start host network backend")
        return
    
    # Wait for it to be ready
    if not wait_for_backend():
        print("❌ Host network backend failed to start")
        return
    
    print("\n🎉 HOST NETWORK BACKEND IS READY!")
    print("=" * 40)
    print("Now try:")
    print("1. python debug_email_verifier.py  # Test SMTP connectivity")
    print("2. python fast_email_verifier.py   # Run full verification")
    print()
    print("🔥 If SMTP still fails, you'll need a VPN or different network")

if __name__ == "__main__":
    main()
