"""
Chunked Dataset Scraper
Processes chunked datasets using the existing PerfectContactExtractor.
Saves results after each chunk and supports resumption.
"""

import asyncio
import pandas as pd
import json
import os
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
from aganl.perfect_contact_extractor import PerfectContactExtractor


class ChunkedDatasetScraper:
    """Scraper that processes chunked datasets with save-after-each-chunk functionality."""
    
    def __init__(self, 
                 chunks_dir: str,
                 results_dir: str,
                 batch_size: int = 30,
                 max_concurrent: int = 12):
        """
        Initialize the chunked scraper.
        
        Args:
            chunks_dir: Directory containing chunk CSV files
            results_dir: Directory to save results
            batch_size: URLs per batch (optimized for speed)
            max_concurrent: Concurrent requests (optimized for speed)
        """
        self.chunks_dir = Path(chunks_dir)
        self.results_dir = Path(results_dir)
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        
        # Create results directory if it doesn't exist
        self.results_dir.mkdir(exist_ok=True)
        
        # Progress tracking file
        self.progress_file = self.results_dir / "scraping_progress.json"
        
        # Initialize extractor with high-performance settings
        self.extractor = PerfectContactExtractor(
            batch_size=batch_size,
            max_concurrent=max_concurrent
        )
        
        print(f"🚀 CHUNKED DATASET SCRAPER INITIALIZED")
        print(f"   • Chunks directory: {self.chunks_dir}")
        print(f"   • Results directory: {self.results_dir}")
        print(f"   • Batch size: {self.batch_size}")
        print(f"   • Max concurrent: {self.max_concurrent}")
    
    def load_progress(self) -> Dict:
        """Load progress from tracking file."""
        if self.progress_file.exists():
            with open(self.progress_file, 'r') as f:
                return json.load(f)
        else:
            return {
                "started_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "completed_chunks": [],
                "current_chunk": 1,
                "total_processed": 0,
                "total_with_emails": 0,
                "total_with_phones": 0
            }
    
    def save_progress(self, progress: Dict):
        """Save progress to tracking file."""
        progress["last_updated"] = datetime.now().isoformat()
        with open(self.progress_file, 'w') as f:
            json.dump(progress, f, indent=2)
    
    def load_chunk_manifest(self) -> Dict:
        """Load the chunk manifest file."""
        manifest_file = self.chunks_dir / "chunk_manifest.json"
        if not manifest_file.exists():
            raise FileNotFoundError(f"Chunk manifest not found: {manifest_file}")
        
        with open(manifest_file, 'r') as f:
            return json.load(f)
    
    def get_chunk_urls(self, chunk_file: str) -> List[str]:
        """Extract URLs from a chunk CSV file."""
        chunk_path = self.chunks_dir / chunk_file
        
        if not chunk_path.exists():
            raise FileNotFoundError(f"Chunk file not found: {chunk_path}")
        
        try:
            df = pd.read_csv(chunk_path)
            urls = df['website'].dropna().tolist()
            
            # Clean URLs - remove any invalid entries
            valid_urls = []
            for url in urls:
                if isinstance(url, str) and url.strip() and url.startswith(('http://', 'https://')):
                    valid_urls.append(url.strip())
            
            return valid_urls
        except Exception as e:
            print(f"❌ Error loading chunk {chunk_file}: {e}")
            return []
    
    async def process_chunk(self, chunk_number: int, chunk_file: str) -> Dict:
        """Process a single chunk and return results."""
        print(f"\n{'='*60}")
        print(f"🔥 PROCESSING CHUNK {chunk_number}")
        print(f"📁 File: {chunk_file}")
        print(f"{'='*60}")
        
        # Load URLs from chunk
        urls = self.get_chunk_urls(chunk_file)
        if not urls:
            print(f"⚠️  No valid URLs found in chunk {chunk_number}")
            return {"chunk": chunk_number, "results": [], "urls_processed": 0}
        
        print(f"📊 Found {len(urls)} URLs to process")
        
        # Process URLs with the extractor
        start_time = datetime.now()
        print(f"⏰ Started at: {start_time.strftime('%H:%M:%S')}")
        
        try:
            results = await self.extractor.extract_perfect(urls)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            rate = len(urls) / duration if duration > 0 else 0
            
            print(f"✅ Chunk {chunk_number} completed!")
            print(f"   • Duration: {duration:.1f} seconds")
            print(f"   • Rate: {rate:.2f} URLs/second")
            print(f"   • Results: {len(results)} items")
            
            return {
                "chunk": chunk_number,
                "results": results,
                "urls_processed": len(urls),
                "duration": duration,
                "rate": rate,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Error processing chunk {chunk_number}: {e}")
            return {
                "chunk": chunk_number,
                "results": [],
                "urls_processed": len(urls),
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def save_chunk_results(self, chunk_data: Dict):
        """Save results for a single chunk."""
        chunk_number = chunk_data["chunk"]
        results = chunk_data["results"]
        
        if not results:
            print(f"⚠️  No results to save for chunk {chunk_number}")
            return
        
        # Save individual chunk results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chunk_filename = f"chunk_{chunk_number:03d}_results_{timestamp}.csv"
        chunk_filepath = self.results_dir / chunk_filename
        
        try:
            self.extractor.export_to_csv(results, str(chunk_filepath))
            print(f"💾 Chunk {chunk_number} results saved: {chunk_filename}")
            
            # Count successful extractions
            emails_found = sum(1 for r in results if r.get('email') and 'error' not in r)
            phones_found = sum(1 for r in results if r.get('phone') and 'error' not in r)
            
            print(f"📧 Emails found: {emails_found}")
            print(f"📞 Phones found: {phones_found}")
            
            return {
                "filename": chunk_filename,
                "filepath": str(chunk_filepath),
                "emails_found": emails_found,
                "phones_found": phones_found
            }
            
        except Exception as e:
            print(f"❌ Error saving chunk {chunk_number} results: {e}")
            return None
    
    async def process_all_chunks(self):
        """Process all chunks in the dataset."""
        print(f"\n🚀 STARTING CHUNKED DATASET PROCESSING")
        print(f"{'='*70}")
        
        # Load manifest and progress
        manifest = self.load_chunk_manifest()
        progress = self.load_progress()
        
        chunks = manifest["chunks"]
        total_chunks = len(chunks)
        
        print(f"📊 Dataset Overview:")
        print(f"   • Total chunks: {total_chunks}")
        print(f"   • Total records: {manifest['total_records']}")
        print(f"   • Chunk size: {manifest['chunk_size']}")
        print(f"   • Completed chunks: {len(progress['completed_chunks'])}")
        
        # Process each chunk
        overall_start = datetime.now()
        
        for i, chunk_info in enumerate(chunks, 1):
            chunk_number = chunk_info["chunk_number"]
            chunk_filename = chunk_info["filename"]
            
            # Skip if already completed
            if chunk_number in progress["completed_chunks"]:
                print(f"⏭️  Skipping chunk {chunk_number} (already completed)")
                continue
            
            # Update current chunk in progress
            progress["current_chunk"] = chunk_number
            self.save_progress(progress)
            
            # Process the chunk
            chunk_data = await self.process_chunk(chunk_number, chunk_filename)
            
            # Save chunk results
            save_info = self.save_chunk_results(chunk_data)
            
            if save_info:
                # Update progress
                progress["completed_chunks"].append(chunk_number)
                progress["total_processed"] += chunk_data["urls_processed"]
                progress["total_with_emails"] += save_info["emails_found"]
                progress["total_with_phones"] += save_info["phones_found"]
                self.save_progress(progress)
                
                print(f"✅ Chunk {chunk_number}/{total_chunks} completed and saved")
            else:
                print(f"⚠️  Chunk {chunk_number} processed but not saved properly")
            
            # Show overall progress
            completed = len(progress["completed_chunks"])
            remaining = total_chunks - completed
            elapsed = (datetime.now() - overall_start).total_seconds()
            
            if completed > 0:
                avg_time_per_chunk = elapsed / completed
                estimated_remaining = avg_time_per_chunk * remaining
                
                print(f"📈 Overall Progress: {completed}/{total_chunks} chunks")
                print(f"⏱️  Estimated time remaining: {estimated_remaining/3600:.1f} hours")
        
        # Final summary
        self.print_final_summary(progress, overall_start)
    
    def print_final_summary(self, progress: Dict, start_time: datetime):
        """Print final processing summary."""
        total_time = (datetime.now() - start_time).total_seconds()
        
        print(f"\n🎉 CHUNKED DATASET PROCESSING COMPLETED!")
        print(f"{'='*70}")
        print(f"📊 Final Statistics:")
        print(f"   • Total chunks processed: {len(progress['completed_chunks'])}")
        print(f"   • Total URLs processed: {progress['total_processed']}")
        print(f"   • Total emails found: {progress['total_with_emails']}")
        print(f"   • Total phones found: {progress['total_with_phones']}")
        print(f"   • Total processing time: {total_time/3600:.1f} hours")
        print(f"   • Average rate: {progress['total_processed']/total_time:.2f} URLs/second")
        print(f"💾 Results saved in: {self.results_dir}")


async def main():
    """Main function to run the chunked scraper."""
    # Configuration
    chunks_dir = "pittsburgh_coffee_prospects_FINAL_20250914_164553_CLEAN_chunks"
    results_dir = "pittsburgh_coffee_prospects_FINAL_20250914_164553_CLEAN_chunks_results"
    
    # Create scraper with high-performance settings for speed
    scraper = ChunkedDatasetScraper(
        chunks_dir=chunks_dir,
        results_dir=results_dir,
        batch_size=30,      # Optimized for speed
        max_concurrent=12   # High concurrency for fast processing
    )
    
    # Process all chunks
    await scraper.process_all_chunks()


if __name__ == "__main__":
    print("🔥 CHUNKED DATASET SCRAPER")
    print("Processes chunked datasets with save-after-each-chunk functionality")
    print("Optimized for high-speed processing with resumption support")
    print()
    
    asyncio.run(main())
