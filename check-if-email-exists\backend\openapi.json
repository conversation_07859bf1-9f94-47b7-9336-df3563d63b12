{"openapi": "3.0.0", "info": {"title": "<PERSON>", "version": "0.11.0", "description": "### What is <PERSON>?\n\nReacher is a robust, open-source email verification API service available as both a SaaS and self-hosted solution.", "license": {"name": "AGPL-3.0 OR Commercial", "url": "https://github.com/reacherhq/check-if-email-exists/blob/master/LICENSE.md"}, "contact": {"name": "<PERSON>", "url": "https://reacher.email", "email": "<EMAIL>"}, "termsOfService": "https://github.com/reacherhq/policies/blob/master/terms/index.fr.md"}, "servers": [{"url": "https://api.reacher.email", "description": "Reacher Production"}], "paths": {"/v1/check_email": {"post": {"summary": "/v1/check_email", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "$ref": "#/components/schemas/CheckEmailOutput"}}}}}, "operationId": "post-v1-check-email", "x-stoplight": {"id": "yho6jrv7p04qv"}, "description": "Perform a comprehensive verification of an email address. This endpoint supersedes the previous `/v0/check_email` endpoint, maintaining the same input and output format. Unlike the `/v0/check_email` endpoint, the new `/v1/check_email` endpoint queues the email for verification, and the Reacher server processes the queue based on its configuration settings such as throttle and concurrency.", "requestBody": {"description": "Request object containing all parameters necessary for an email verification.", "content": {"application/json": {"schema": {"type": "object", "$ref": "#/components/schemas/CheckEmailRequest"}}}}}, "parameters": [], "servers": []}, "/v1/bulk": {"post": {"summary": "/v1/bulk", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "required": ["job_id"], "properties": {"job_id": {"type": "integer", "x-stoplight": {"id": "614agdy9mxybk"}, "description": "The unique ID generated for this bulk verification job. Use this `job_id` to query the progress or results of the bulk verification."}}}}}}}, "operationId": "post-v1-bulk", "x-stoplight": {"id": "jpoyy08arq31b"}, "description": "Initiate a bulk email verification.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["input"], "properties": {"input": {"type": "array", "x-stoplight": {"id": "fsitj4yhkzk8e"}, "description": "A list of emails to verify.", "items": {"x-stoplight": {"id": "kgcb3i4u5sfgr"}, "type": "string"}}, "webhook": {"$ref": "#/components/schemas/TaskWebhook"}}}}}}}}, "/v1/bulk/{job_id}": {"parameters": [{"schema": {"type": "integer"}, "name": "job_id", "in": "path", "required": true, "description": "The unique bulk verification job ID"}], "get": {"summary": "/v1/bulk/{job_id}", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "required": ["job_id", "created_at", "total_records", "total_processed", "summary", "job_status"], "properties": {"job_id": {"type": "integer", "x-stoplight": {"id": "4f6vqudy7zank"}}, "created_at": {"type": "string", "x-stoplight": {"id": "7baymbmoaj5vo"}, "format": "date-time", "description": "The date and time when the bulk verification job was created."}, "finished_at": {"type": "string", "x-stoplight": {"id": "7lhzxsb1ixnvz"}, "format": "date-time", "description": "If the bulk verification job is completed, the date and time when it was finished."}, "total_records": {"type": "integer", "x-stoplight": {"id": "y8q2zhagq8zd6"}, "description": "The number of emails to verify in the bulk verification job."}, "total_processed": {"type": "integer", "x-stoplight": {"id": "oqxvsorhd06ch"}, "description": "The number of emails that have been verified at the time of the query."}, "summary": {"type": "object", "x-stoplight": {"id": "n5q382zces1lq"}, "description": "A summary of the processed emails.", "required": ["total_safe", "total_invalid", "total_risky", "total_unknown"], "properties": {"total_safe": {"type": "integer", "x-stoplight": {"id": "4nss0vedcrjc4"}, "description": "The number of emails where `is_reachable` is \"safe\"."}, "total_invalid": {"type": "integer", "x-stoplight": {"id": "uzzk7ija2l5sv"}, "description": "The number of emails where `is_reachable` is \"invalid\"."}, "total_risky": {"type": "integer", "x-stoplight": {"id": "sqjtoggge6us3"}, "description": "The number of emails where `is_reachable` is \"risky\"."}, "total_unknown": {"type": "integer", "x-stoplight": {"id": "8<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "description": "The number of emails where `is_reachable` is \"unknown\"."}}}, "job_status": {"x-stoplight": {"id": "tsw5sp5cxe7ad"}, "enum": ["Running", "Completed"], "description": "The status of the job, either \"Running\" or \"Completed\"."}}}}}}}, "operationId": "get-v1-bulk", "x-stoplight": {"id": "phrhxrak01ja6"}, "description": "Retrieve the progress of a bulk verification job."}}, "/v1/bulk/{job_id}/results": {"parameters": [{"schema": {"type": "string"}, "name": "job_id", "in": "path", "required": true, "description": "The unique bulk verification job ID"}], "get": {"summary": "Retrieve bulk verification results", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "required": ["results"], "properties": {"results": {"$ref": "#/components/schemas/CheckEmailOutput"}}}}}}}, "operationId": "get-v1-bulk-results", "x-stoplight": {"id": "skx9nlfib3but"}, "description": "Retrieve the results of a bulk verification job. This endpoint will return an error if the job is still running. Please query `GET /v1/bulk/{job_id}` first to check the job's progress.", "parameters": [{"schema": {"type": "integer", "default": 50}, "in": "query", "name": "limit", "description": "The number of results to return."}, {"schema": {"type": "integer"}, "in": "query", "name": "offset", "description": "The offset from which to return the results, equivalent to the number of elements in the array to skip."}]}}, "/v0/check_email": {"post": {"summary": "/v0/check_email", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckEmailOutput"}, "examples": {}}}}}, "operationId": "post-v0-check-email", "description": "**Sunset notice: please use `/v1/check_email` instead.** Both endpoints accept the same input arguments and return the same output; only their internal implementation differs. Perform a comprehensive verification of an email address. Unlike the `/v1/check_email` endpoint, this endpoint performs an email verification immediately, without considering the Reacher server's throttling, concurrency, and other configurations. As such, this endpoint is slightly riskier than `/v1/check_email`, as the Reacher server's IP reputation can be impacted if this endpoint is called too frequently.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckEmailRequest"}}}, "description": "Request object containing all parameters necessary for an email verification."}, "parameters": [{"schema": {"type": "string"}, "in": "header", "name": "Authorization", "description": "Your personal Reacher API key", "required": true}], "x-stoplight": {"id": "9cw3vilj3q88h"}, "deprecated": true}, "parameters": []}}, "components": {"schemas": {"CheckEmailRequest": {"title": "CheckEmailRequest", "x-stoplight": {"id": "834398a8ce480"}, "type": "object", "description": "A request object to perform an email verification. The `to_email` field is required, all other fields are optional.", "required": ["to_email"], "properties": {"from_email": {"type": "string", "description": "In the SMTP connection, the FROM email address."}, "to_email": {"type": "string", "description": "The email address to check."}, "hello_name": {"type": "string", "description": "In the SMTP connection, the EHLO hostname."}, "proxy": {"$ref": "#/components/schemas/CheckEmailInputProxy"}, "smtp_port": {"type": "number", "description": "SMTP port to use for email validation. Defaults to 25, but 465, 587, and 2525 are sometimes also used."}, "gmail_verif_method": {"$ref": "#/components/schemas/GmailVerifMethod"}, "hotmailb2b_verif_method": {"$ref": "#/components/schemas/HotmailB2BVerifMethod"}, "hotmailb2c_verif_method": {"$ref": "#/components/schemas/HotmailB2CVerifMethod"}, "yahoo_verif_method": {"$ref": "#/components/schemas/YahooVerifMethod"}, "check_gravatar": {"type": "boolean", "description": "Whether to check if a Gravatar image exists for the given email."}}}, "CheckEmailOutput": {"title": "CheckEmailOutput", "type": "object", "x-examples": {"<NAME_EMAIL>": {"input": "<EMAIL>", "is_reachable": "invalid", "misc": {"is_disposable": false, "is_role_account": true}, "mx": {"accepts_mail": true, "records": ["alt4.gmail-smtp-in.l.google.com.", "alt2.gmail-smtp-in.l.google.com.", "alt3.gmail-smtp-in.l.google.com.", "gmail-smtp-in.l.google.com.", "alt1.gmail-smtp-in.l.google.com."]}, "smtp": {"can_connect_smtp": true, "has_full_inbox": false, "is_catch_all": false, "is_deliverable": false, "is_disabled": false}, "syntax": {"domain": "gmail.com", "is_valid_syntax": true, "username": "test"}}}, "description": "The result of the email verification process.", "required": ["input", "is_reachable", "misc", "mx", "smtp", "syntax"], "properties": {"input": {"type": "string", "format": "email", "description": "The email address that was verified."}, "is_reachable": {"$ref": "#/components/schemas/Reachable"}, "misc": {"oneOf": [{"$ref": "#/components/schemas/MiscDetails"}, {"$ref": "#/components/schemas/CoreError"}], "description": "Additional information about the email account."}, "mx": {"oneOf": [{"$ref": "#/components/schemas/MxDetails"}, {"$ref": "#/components/schemas/CoreError"}], "description": "Details obtained from querying the mail server's MX records."}, "smtp": {"oneOf": [{"$ref": "#/components/schemas/SmtpDetails"}, {"$ref": "#/components/schemas/CoreError"}], "description": "Results from connecting to the mail server via SMTP."}, "syntax": {"$ref": "#/components/schemas/SyntaxDetails"}, "debug": {"$ref": "#/components/schemas/DebugDetails"}}}, "Reachable": {"type": "string", "title": "Reachable", "enum": ["invalid", "unknown", "safe", "risky"], "description": "An enumeration describing the confidence level that the recipient address is valid: `safe`, `risky`, `invalid`, or `unknown`. Refer to our FAQ for detailed definitions: https://help.reacher.email/email-attributes-inside-json."}, "MiscDetails": {"title": "MiscDetails", "type": "object", "description": "Additional information about the email account.", "required": ["is_disposable", "is_role_account", "is_b2c"], "properties": {"is_disposable": {"type": "boolean", "description": "Indicates if the email address is from a known disposable email provider."}, "is_role_account": {"type": "boolean", "description": "Indicates if the email address is a role-based account."}, "gravatar_url": {"type": "string", "description": "URL to the <PERSON><PERSON><PERSON><PERSON> profile picture associated with the email, if available and requested."}, "is_b2c": {"type": "boolean", "x-stoplight": {"id": "0cxn26qlxy8r4"}, "description": "Is this a B2C email address?"}}}, "MxDetails": {"title": "MxDetails", "type": "object", "properties": {"accepts_mail": {"type": "boolean", "description": "Indicates if the mail server accepts emails."}, "records": {"type": "array", "description": "List of Fully Qualified Domain Names (FQDN) of the mail server.", "items": {"type": "string"}}}, "required": ["accepts_mail", "records"], "description": "Details about the mail server's MX records."}, "SmtpDetails": {"title": "SmtpDetails", "type": "object", "description": "Results from SMTP connection attempts to the mail server.", "properties": {"can_connect_smtp": {"type": "boolean", "description": "Indicates if the mail exchanger can be contacted successfully."}, "has_full_inbox": {"type": "boolean", "description": "Indicates if the mailbox is full."}, "is_catch_all": {"type": "boolean", "description": "Indicates if the email address is a catch-all address."}, "is_deliverable": {"type": "boolean", "description": "Indicates if an email sent to this address is deliverable."}, "is_disabled": {"type": "boolean", "description": "Indicates if the email address has been disabled by the provider."}}, "required": ["can_connect_smtp", "has_full_inbox", "is_catch_all", "is_deliverable", "is_disabled"]}, "SyntaxDetails": {"title": "SyntaxDetails", "type": "object", "description": "Validation of the email address syntax.", "properties": {"domain": {"type": "string", "description": "The domain part of the email address."}, "is_valid_syntax": {"type": "boolean", "description": "Indicates if the email address syntax is valid."}, "username": {"type": "string", "description": "The username part of the email address."}}, "required": ["domain", "is_valid_syntax", "username"]}, "CoreError": {"title": "CoreError", "x-stoplight": {"id": "a872ead6474cd"}, "type": "object", "description": "Details of an error encountered during the verification process.", "properties": {"type": {"type": "string", "description": "The type of error."}, "message": {"type": "string", "description": "A human-readable description of the error."}}, "required": ["type", "message"]}, "YahooVerifMethod": {"type": "string", "title": "YahooVerifMethod", "enum": ["Api", "Headless", "Smtp"], "description": "Enumeration describing the method used to verify Yahoo emails."}, "HotmailB2BVerifMethod": {"type": "string", "x-stoplight": {"id": "ntdugsleyotut"}, "title": "HotmailB2BVerifMethod", "enum": ["Smtp"], "description": "Enumeration describing the method used to verify Hotmail B2B emails."}, "HotmailB2CVerifMethod": {"type": "string", "x-stoplight": {"id": "cuc5bj6ra2t0i"}, "title": "HotmailB2CVerifMethod", "enum": ["Smtp", "Headless"], "description": "Enumeration describing the method used to verify Hotmail B2C emails."}, "GmailVerifMethod": {"type": "string", "x-stoplight": {"id": "xo5r48yhtxiwr"}, "title": "GmailVerifMethod", "enum": ["Smtp"], "description": "Enumeration describing the method used to verify Gmail emails.", "x-internal": false}, "CheckEmailInputProxy": {"title": "CheckEmailInputProxy", "type": "object", "x-examples": {"example-1": {"value": {"host": "my-proxy.io", "port": 1080}}}, "properties": {"host": {"type": "string", "description": "The proxy host address."}, "port": {"type": "integer", "description": "The proxy port number."}, "username": {"type": "string", "description": "Username for proxy authentication."}, "password": {"type": "string", "description": "Password for proxy authentication."}}, "required": ["host", "port"], "description": "Proxy configuration for email verification."}, "DebugDetails": {"title": "DebugDetails", "x-stoplight": {"id": "4wxlk39h8v9kz"}, "type": "object", "properties": {"start_time": {"type": "string", "x-stoplight": {"id": "60i65k60m8e8d"}, "description": "The timestamp when the email verification started."}, "end_time": {"type": "string", "x-stoplight": {"id": "zw4ccbvwsoh6q"}, "description": "The timestamp when the email verification ended."}, "duration": {"$ref": "#/components/schemas/Duration"}, "server_name": {"type": "string", "x-stoplight": {"id": "2jrbdecvqh4t5"}, "description": "The name of the server that performed the verification."}, "smtp": {"$ref": "#/components/schemas/DebugDetailsSmtp"}}, "required": ["start_time", "end_time", "duration", "server_name", "smtp"]}, "Duration": {"title": "Duration", "x-stoplight": {"id": "bg9y0iez4zati"}, "type": "object", "description": "An object representing a duration in seconds and nanoseconds.", "properties": {"secs": {"type": "number", "description": "Duration in seconds."}, "nanos": {"type": "number", "description": "Duration in nanoseconds."}}, "required": ["secs", "nanos"]}, "DebugDetailsSmtp": {"title": "DebugDetailsSmtp", "x-stoplight": {"id": "2a90bzapppo0j"}, "type": "object", "properties": {"verif_method": {"$ref": "#/components/schemas/VerifMethod"}}, "description": "SMTP details used for debugging, including the verification method."}, "VerifMethod": {"title": "VerifMethod", "x-stoplight": {"id": "9xw9e1jwti230"}, "type": "object", "description": "The method used for email verification.", "required": ["type"], "properties": {"type": {"x-stoplight": {"id": "4ogsz639tcdb6"}, "enum": ["Smtp", "Headless", "Api", "Skipped"], "description": "The method used for the email verification."}}}, "TaskWebhook": {"title": "TaskWebhook", "x-stoplight": {"id": "6053d3ngu2hfn"}, "type": "object", "description": "Optional webhook configuration for sending email verification results during bulk verification.", "properties": {"on_each_email": {"$ref": "#/components/schemas/Webhook"}}}, "Webhook": {"title": "Webhook", "x-stoplight": {"id": "p9emghvcb92fj"}, "type": "object", "description": "Configuration for a webhook to receive email verification results. The method will be POST, and the body will contain the email verification response.", "required": ["url"], "properties": {"url": {"type": "string", "x-stoplight": {"id": "iraaa1ism4bzi"}, "description": "The URL to send the email verification results to."}, "extra": {"type": "object", "x-stoplight": {"id": "f20boz81d2fei"}}}}}, "securitySchemes": {"Authorization": {"name": "Authorization", "type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "description": "A Reacher API key is required for all requests. Sign up on https://reacher.email to get your personal API key."}}, "requestBodies": {}}, "security": [{"Authorization": []}]}