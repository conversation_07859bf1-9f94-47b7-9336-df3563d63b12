"""
Perfect Contact Extractor - Usage Example
The ultimate solution for extracting emails and social media at scale.
"""

import asyncio
import csv
from datetime import datetime
from perfect_contact_extractor import PerfectContactExtractor


def load_urls_from_csv(filename: str) -> list:
    """Load URLs from a CSV file."""
    urls = []
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                url = row.get('website_url', '').strip()
                if url and url.startswith(('http://', 'https://')):
                    urls.append(url)
        return urls
    except Exception as e:
        print(f"Error loading URLs: {e}")
        return []


async def extract_contacts_example():
    """Example of using the perfect contact extractor."""
    print("⭐ PERFECT CONTACT EXTRACTOR - EXAMPLE")
    print("=" * 50)
    
    # Example 1: Single URL
    print("📧 Example 1: Single URL")
    single_url = "https://thejamescafe.com/"
    
    extractor = PerfectContactExtractor(batch_size=10, max_concurrent=3)
    results = await extractor.extract_perfect([single_url])
    
    result = results[0]
    if 'error' not in result:
        email = result.get('email')
        social = result.get('social_media')
        
        print(f"URL: {result['url']}")
        if email:
            print(f"📧 Email: {email['email']} (confidence: {email['confidence']:.1%})")
        if social:
            print(f"🌐 Social: {social['platform']} {social.get('handle', '')}")
    
    # Example 2: Multiple URLs
    print(f"\n📊 Example 2: Multiple URLs")
    test_urls = [
        "https://thejamescafe.com/",
        "https://www.defer.coffee/strip-district",
        "https://convivecoffee.com/"
    ]
    
    results = await extractor.extract_perfect(test_urls)
    
    for i, result in enumerate(results, 1):
        if 'error' not in result:
            email = result.get('email')
            social = result.get('social_media')
            pages_checked = result.get('pages_checked', 0)
            
            domain = result['url'].split('/')[2].replace('www.', '')
            
            print(f"\n{i}. {domain} ({pages_checked} pages checked)")
            if email:
                print(f"   📧 {email['email']}")
            if social:
                print(f"   🌐 {social['platform']} {social.get('handle', '')}")
    
    # Example 3: Load from CSV file
    print(f"\n📁 Example 3: Load from CSV file")
    urls = load_urls_from_csv("test_data.csv")
    
    if urls:
        print(f"Loaded {len(urls)} URLs from CSV")
        
        # Process with production settings
        start_time = datetime.now()
        results = await extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        # Print summary
        extractor.print_summary(results)
        
        # Export results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"contact_results_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        
        print(f"\n✅ Results exported to: {filename}")
        print(f"⚡ Processing rate: {len(urls)/duration:.2f} URLs/second")
    
    print(f"\n🎉 Example completed!")


async def production_example():
    """Example for production use with large datasets."""
    print("\n🚀 PRODUCTION EXAMPLE")
    print("=" * 50)
    print("For processing thousands of URLs:")
    
    # Production settings
    extractor = PerfectContactExtractor(
        batch_size=25,      # Process 25 URLs per batch
        max_concurrent=5    # 5 concurrent requests per batch
    )
    
    # Load your URLs (replace with your data source)
    urls = load_urls_from_csv("test_data.csv")
    
    if urls:
        print(f"🎯 Processing {len(urls)} URLs with production settings")
        
        start_time = datetime.now()
        results = await extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        # Comprehensive analysis
        extractor.print_summary(results)
        
        # Export with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"production_results_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        
        print(f"\n💾 Production results exported to: {filename}")
        print(f"⚡ Production rate: {len(urls)/duration:.2f} URLs/second")
        
        # Scaling projections
        rate = len(urls) / duration
        print(f"\n📈 SCALING PROJECTIONS:")
        print(f"   • 1,000 URLs: ~{1000/rate/60:.1f} minutes")
        print(f"   • 10,000 URLs: ~{10000/rate/60:.1f} minutes")
        print(f"   • 100,000 URLs: ~{100000/rate/3600:.1f} hours")


async def main():
    """Main example function."""
    try:
        await extract_contacts_example()
        await production_example()
        
        print(f"\n⭐ PERFECT CONTACT EXTRACTOR FEATURES:")
        print("✅ Discovers available pages intelligently")
        print("✅ Prioritizes pages by contact likelihood")
        print("✅ Checks pages until finding information")
        print("✅ Stops early when both contacts found")
        print("✅ Handles thousands of URLs efficiently")
        print("✅ Production-ready with robust error handling")
        
    except Exception as e:
        print(f"❌ Example failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⭐ Example interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
