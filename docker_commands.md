# Docker Commands to Fix Email Verification

## 🛑 Stop Current Container
```bash
docker stop email-verifier-improved
docker rm email-verifier-improved
```

## 🌐 Start with Host Network (THE FIX!)
```bash
docker run -d \
  --name email-verifier-host \
  --network host \
  -e RCH__HELLO_NAME=orchidwebsolution.com \
  -e RCH__FROM_EMAIL=<EMAIL> \
  -e RCH__SMTP_TIMEOUT=60 \
  -e RCH__HTTP_HOST=127.0.0.1 \
  -e RCH__HTTP_PORT=8080 \
  reacherhq/backend:latest
```

## 🧪 Test if it's working
```bash
# Check if container is running
docker ps

# Check logs
docker logs email-verifier-host

# Test the API
curl -X POST http://localhost:8080/v0/check_email \
  -H "Content-Type: application/json" \
  -d '{"to_email": "<EMAIL>"}'
```

## 🔥 Alternative: One-liner for Windows PowerShell
```powershell
docker run -d --name email-verifier-host --network host -e RCH__HELLO_NAME=orchidwebsolution.com -e RCH__FROM_EMAIL=<EMAIL> -e RCH__SMTP_TIMEOUT=60 -e RCH__HTTP_HOST=127.0.0.1 -e RCH__HTTP_PORT=8080 reacherhq/backend:latest
```

## 🎯 What This Does:
- `--network host`: Uses your computer's network directly (bypasses Docker network)
- `-e RCH__HELLO_NAME=orchidwebsolution.com`: Your domain for SMTP HELLO
- `-e RCH__FROM_EMAIL=<EMAIL>`: Your email for SMTP FROM
- `-e RCH__SMTP_TIMEOUT=60`: Longer timeout for slow servers

## 🚨 If Port 25 is Still Blocked:
Your ISP might be blocking port 25. Try:
1. **VPN**: Use a VPN to bypass ISP restrictions
2. **Different Network**: Try mobile hotspot or different WiFi
3. **Cloud Server**: Run on AWS/DigitalOcean/etc where port 25 works

## ✅ Once Running:
```bash
# Test with our scripts
python debug_email_verifier.py
python fast_email_verifier.py
```
