# Improved Backend Configuration for Better Email Verification
# This config uses more realistic SMTP settings that corporate servers trust

backend_name = "leadgen-verifier"

# Host and port
http_host = "127.0.0.1"
http_port = 8080

# CRITICAL: Use realistic SMTP settings instead of localhost
# Many corporate servers reject "localhost" immediately
hello_name = "orchidwebsolution.com"
from_email = "<EMAIL>"

# Increase timeout for better results with slow corporate servers
smtp_timeout = 60

# WebDriver for headless verification (some providers need this)
webdriver_addr = "http://localhost:9515"

# Throttle settings - be more aggressive for speed
[throttle]
max_requests_per_second = 50
max_requests_per_minute = 200
max_requests_per_hour = 5000
max_requests_per_day = 50000

# Override verification methods for better corporate email detection
[overrides]

# Use SMTP for Gmail (more reliable than API for corporate Gmail)
[overrides.gmail]
type = "smtp"
hello_name = "orchidwebsolution.com"
from_email = "<EMAIL>"

# Use SMTP for Hotmail B2B (corporate Outlook)
[overrides.hotmailb2b]
type = "smtp"
hello_name = "orchidwebsolution.com"
from_email = "<EMAIL>"

# Use headless for consumer Hotmail (better detection)
[overrides.hotmailb2c]
type = "headless"

# Use headless for Yahoo (better than SMTP)
[overrides.yahoo]
type = "headless"
