"""
Test the smart contact extractor that discovers contact pages first
"""

import asyncio
import csv
from datetime import datetime
from typing import List
from smart_contact_extractor import SmartContactExtractor


def load_test_urls() -> List[str]:
    """Load URLs from test_data.csv"""
    urls = []
    try:
        with open("test_data.csv", 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                url = row.get('website_url', '').strip()
                if url and url.startswith(('http://', 'https://')):
                    urls.append(url)
        return urls
    except Exception as e:
        print(f"Error loading URLs: {e}")
        return []


async def test_smart_extraction_demo():
    """Demo the smart extraction on a few URLs."""
    print("🧠 SMART CONTACT EXTRACTOR - DEMO")
    print("=" * 60)
    print("Strategy:")
    print("1. 🔍 Discover contact pages (/contact, /about, etc.)")
    print("2. 📊 Score pages by contact info likelihood") 
    print("3. 🎯 Extract from highest-scoring pages only")
    print("4. ✨ Return best single contact of each type")
    print("=" * 60)
    
    # Test with a few URLs
    test_urls = [
        "https://thejamescafe.com/",
        "https://www.defer.coffee/strip-district",
        "https://convivecoffee.com/"
    ]
    
    extractor = SmartContactExtractor(max_concurrent=3)
    results = await extractor.extract_contacts_smart(test_urls)
    
    print(f"\n{'='*80}")
    print("SMART EXTRACTION RESULTS")
    print(f"{'='*80}")
    
    for i, result in enumerate(results, 1):
        if 'error' in result:
            print(f"\n{i}. ❌ ERROR: {result['url']}")
            continue
        
        url = result['url']
        contacts = result.get('contacts', {})
        metadata = result.get('metadata', {})
        
        print(f"\n{i}. 🏪 {url}")
        print(f"   📄 Pages checked: {metadata.get('pages_checked', 0)}")
        print(f"   🎯 Contact pages found: {metadata.get('contact_pages_found', 0)}")
        
        # Show results
        email = contacts.get('email')
        phone = contacts.get('phone')
        address = contacts.get('address')
        social = contacts.get('social_media')
        
        if email:
            print(f"   📧 Email: {email['email']} (confidence: {email['confidence']:.1%})")
        else:
            print(f"   📧 Email: Not found")
        
        if phone:
            print(f"   📞 Phone: {phone['phone']} (confidence: {phone['confidence']:.1%})")
        else:
            print(f"   📞 Phone: Not found")
        
        if address:
            addr_display = address['address'][:60] + "..." if len(address['address']) > 60 else address['address']
            print(f"   📍 Address: {addr_display} (confidence: {address['confidence']:.1%})")
        else:
            print(f"   📍 Address: Not found")
        
        if social:
            print(f"   🌐 Social: {social['platform'].title()} {social.get('handle', '')} (confidence: {social['confidence']:.1%})")
        else:
            print(f"   🌐 Social: Not found")
    
    # Export demo results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    extractor.export_to_csv(results, f"smart_demo_{timestamp}.csv")
    
    return results


async def test_smart_batch_processing():
    """Test smart extraction on a larger batch."""
    print("\n🚀 SMART BATCH PROCESSING TEST")
    print("=" * 60)
    
    urls = load_test_urls()
    if not urls:
        print("No URLs found")
        return
    
    # Test with first 10 URLs
    test_urls = urls[:10]
    print(f"Processing {len(test_urls)} URLs with smart extraction...")
    
    start_time = datetime.now()
    
    extractor = SmartContactExtractor(max_concurrent=5)
    results = await extractor.extract_contacts_smart(test_urls)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # Analyze results
    print(f"\n{'='*80}")
    print("BATCH PROCESSING RESULTS")
    print(f"{'='*80}")
    
    successful = 0
    total_pages_checked = 0
    total_contact_pages = 0
    
    contact_counts = {'email': 0, 'phone': 0, 'address': 0, 'social': 0}
    
    for result in results:
        if 'error' in result:
            print(f"❌ {result['url']}: {result['error']}")
            continue
        
        successful += 1
        metadata = result.get('metadata', {})
        contacts = result.get('contacts', {})
        
        total_pages_checked += metadata.get('pages_checked', 0)
        total_contact_pages += len(metadata.get('contact_pages_found', []))
        
        # Count successful extractions
        for contact_type in ['email', 'phone', 'address', 'social_media']:
            if contacts.get(contact_type):
                key = contact_type.replace('_media', '')
                contact_counts[key] += 1
    
    print(f"\n📊 PERFORMANCE STATISTICS:")
    print(f"   • URLs processed: {len(test_urls)}")
    print(f"   • Successful: {successful}")
    print(f"   • Total pages checked: {total_pages_checked}")
    print(f"   • Contact pages found: {total_contact_pages}")
    print(f"   • Processing time: {duration:.2f} seconds")
    print(f"   • Average time per URL: {duration/len(test_urls):.2f} seconds")
    print(f"   • Average pages per URL: {total_pages_checked/successful:.1f}" if successful > 0 else "")
    
    print(f"\n📞 EXTRACTION SUCCESS RATES:")
    for contact_type, count in contact_counts.items():
        percentage = (count / successful * 100) if successful > 0 else 0
        print(f"   • {contact_type.title()}: {count}/{successful} ({percentage:.1f}%)")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    extractor.export_to_csv(results, f"smart_batch_{timestamp}.csv")
    
    print(f"\n💾 Results exported to: smart_batch_{timestamp}.csv")
    
    return results


async def test_scalability_simulation():
    """Simulate processing larger batches to test scalability."""
    print("\n⚡ SCALABILITY SIMULATION")
    print("=" * 60)
    
    urls = load_test_urls()
    if len(urls) < 5:
        print("Need more URLs for scalability test")
        return
    
    # Test different batch sizes and concurrency levels
    test_configs = [
        {"batch_size": 5, "concurrent": 3},
        {"batch_size": 10, "concurrent": 5},
        {"batch_size": len(urls), "concurrent": 8}
    ]
    
    for config in test_configs:
        batch_size = config["batch_size"]
        concurrent = config["concurrent"]
        
        test_urls = urls[:batch_size]
        
        print(f"\n🧪 Testing: {batch_size} URLs, {concurrent} concurrent")
        
        start_time = datetime.now()
        
        extractor = SmartContactExtractor(max_concurrent=concurrent)
        results = await extractor.extract_contacts_smart(test_urls)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        successful = len([r for r in results if 'error' not in r])
        
        print(f"   ✅ Processed {successful}/{batch_size} URLs in {duration:.2f}s")
        print(f"   ⚡ Rate: {successful/duration:.2f} URLs/second")
        
        # Estimate time for 1000 URLs
        estimated_time_1000 = (1000 / successful) * duration if successful > 0 else 0
        print(f"   📈 Estimated time for 1000 URLs: {estimated_time_1000/60:.1f} minutes")


async def main():
    """Main test function."""
    print("🧠 SMART CONTACT EXTRACTOR TESTS")
    print("Discovers contact pages first, then extracts efficiently")
    print("=" * 60)
    
    print("\nChoose test mode:")
    print("1. Demo (3 URLs with detailed analysis)")
    print("2. Batch test (10 URLs)")
    print("3. Scalability simulation")
    print("4. All tests")
    
    try:
        choice = input("\nEnter choice (1-4) or press Enter for option 1: ").strip()
        if not choice:
            choice = "1"
    except KeyboardInterrupt:
        print("\n\nTest cancelled.")
        return
    
    try:
        if choice == "1":
            await test_smart_extraction_demo()
        elif choice == "2":
            await test_smart_batch_processing()
        elif choice == "3":
            await test_scalability_simulation()
        elif choice == "4":
            await test_smart_extraction_demo()
            await test_smart_batch_processing()
            await test_scalability_simulation()
        else:
            print("Invalid choice. Running demo.")
            await test_smart_extraction_demo()
    
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
    
    print(f"\n🎉 Smart extraction testing completed!")
    print("\nKey advantages of smart extraction:")
    print("✅ Discovers contact pages automatically")
    print("✅ Focuses on high-value pages only")
    print("✅ Scales efficiently for thousands of URLs")
    print("✅ Returns clean, normalized contact information")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n🧠 Smart extraction test interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
