"""
Test the optimized smart contact extractor with bug fixes and performance improvements
"""

import asyncio
import csv
from datetime import datetime
from typing import List
from smart_contact_extractor import SmartContactExtractor


def load_test_urls() -> List[str]:
    """Load URLs from test_data.csv"""
    urls = []
    try:
        with open("test_data.csv", 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                url = row.get('website_url', '').strip()
                if url and url.startswith(('http://', 'https://')):
                    urls.append(url)
        return urls
    except Exception as e:
        print(f"Error loading URLs: {e}")
        return []


async def test_phone_fix():
    """Test the phone number extraction fix specifically."""
    print("🔧 TESTING PHONE NUMBER FIX")
    print("=" * 60)
    
    # URLs that should have clear phone numbers
    test_urls = [
        "https://thejamescafe.com/",
        "https://convivecoffee.com/",
        "https://www.defer.coffee/strip-district"
    ]
    
    extractor = SmartContactExtractor(max_concurrent=3)
    results = await extractor.extract_contacts_smart(test_urls)
    
    print("\n📞 PHONE NUMBER EXTRACTION RESULTS:")
    
    for i, result in enumerate(results, 1):
        if 'error' in result:
            print(f"{i}. ❌ {result['url']}: Error")
            continue
        
        url = result['url']
        phone = result.get('contacts', {}).get('phone')
        
        print(f"\n{i}. {url}")
        if phone:
            print(f"   📞 Phone: {phone['phone']} (confidence: {phone['confidence']:.1%})")
        else:
            print(f"   📞 Phone: Not found")
    
    return results


async def test_speed_optimization():
    """Test speed improvements with larger batch."""
    print("\n⚡ TESTING SPEED OPTIMIZATION")
    print("=" * 60)
    
    urls = load_test_urls()
    if len(urls) < 10:
        print("Need more URLs for speed test")
        return
    
    # Test with 10 URLs
    test_urls = urls[:10]
    
    print(f"Processing {len(test_urls)} URLs with optimizations...")
    print("Optimizations:")
    print("• Reduced contact page patterns")
    print("• Early stopping when complete contact info found")
    print("• Better phone number validation")
    print("• Increased concurrency")
    
    start_time = datetime.now()
    
    extractor = SmartContactExtractor(max_concurrent=8)  # Increased concurrency
    results = await extractor.extract_contacts_smart(test_urls)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # Analyze results
    print(f"\n📊 SPEED TEST RESULTS:")
    print(f"   • URLs processed: {len(test_urls)}")
    print(f"   • Processing time: {duration:.2f} seconds")
    print(f"   • Average per URL: {duration/len(test_urls):.2f} seconds")
    print(f"   • Processing rate: {len(test_urls)/duration:.2f} URLs/second")
    
    # Estimate for larger batches
    estimated_100 = (100 / len(test_urls)) * duration
    estimated_1000 = (1000 / len(test_urls)) * duration
    
    print(f"\n📈 SCALING ESTIMATES:")
    print(f"   • 100 URLs: ~{estimated_100/60:.1f} minutes")
    print(f"   • 1000 URLs: ~{estimated_1000/60:.1f} minutes")
    
    # Quality analysis
    successful = len([r for r in results if 'error' not in r])
    contact_counts = {'email': 0, 'phone': 0, 'address': 0, 'social': 0}
    total_pages_checked = 0
    
    for result in results:
        if 'error' in result:
            continue
        
        contacts = result.get('contacts', {})
        metadata = result.get('metadata', {})
        
        total_pages_checked += metadata.get('pages_checked', 0)
        
        for contact_type in ['email', 'phone', 'address', 'social_media']:
            if contacts.get(contact_type):
                key = contact_type.replace('_media', '')
                contact_counts[key] += 1
    
    print(f"\n📞 EXTRACTION QUALITY:")
    for contact_type, count in contact_counts.items():
        percentage = (count / successful * 100) if successful > 0 else 0
        print(f"   • {contact_type.title()}: {count}/{successful} ({percentage:.1f}%)")
    
    avg_pages = total_pages_checked / successful if successful > 0 else 0
    print(f"   • Average pages checked per URL: {avg_pages:.1f}")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    extractor.export_to_csv(results, f"optimized_speed_test_{timestamp}.csv")
    
    return results


async def test_accuracy_improvements():
    """Test accuracy improvements on challenging URLs."""
    print("\n🎯 TESTING ACCURACY IMPROVEMENTS")
    print("=" * 60)
    
    # Test with URLs that had issues before
    challenging_urls = [
        "https://convivecoffee.com/",  # Had wrong phone number before
        "https://www.bittyandbeauscoffee.com/location/pittsburgh/",  # Complex page structure
        "https://www.novariacoffee.com/",  # Minimal contact info
        "https://redstartroasters.com/",  # Different layout
        "https://www.centredcoffee.com/"  # Simple site
    ]
    
    print(f"Testing {len(challenging_urls)} challenging URLs...")
    
    extractor = SmartContactExtractor(max_concurrent=5)
    results = await extractor.extract_contacts_smart(challenging_urls)
    
    print(f"\n🎯 ACCURACY TEST RESULTS:")
    
    for i, result in enumerate(results, 1):
        if 'error' in result:
            print(f"\n{i}. ❌ ERROR: {result['url']}")
            continue
        
        url = result['url']
        contacts = result.get('contacts', {})
        metadata = result.get('metadata', {})
        
        # Extract domain for display
        domain = url.split('/')[2].replace('www.', '')
        
        print(f"\n{i}. 🏪 {domain}")
        print(f"   🔗 {url}")
        print(f"   📄 Pages checked: {metadata.get('pages_checked', 0)}")
        
        # Show all contact info found
        email = contacts.get('email')
        phone = contacts.get('phone')
        address = contacts.get('address')
        social = contacts.get('social_media')
        
        if email:
            print(f"   📧 Email: {email['email']} (confidence: {email['confidence']:.1%})")
        else:
            print(f"   📧 Email: ❌ Not found")
        
        if phone:
            print(f"   📞 Phone: {phone['phone']} (confidence: {phone['confidence']:.1%})")
        else:
            print(f"   📞 Phone: ❌ Not found")
        
        if address:
            addr_display = address['address'][:50] + "..." if len(address['address']) > 50 else address['address']
            print(f"   📍 Address: {addr_display} (confidence: {address['confidence']:.1%})")
        else:
            print(f"   📍 Address: ❌ Not found")
        
        if social:
            print(f"   🌐 Social: {social['platform'].title()} {social.get('handle', '')} (confidence: {social['confidence']:.1%})")
        else:
            print(f"   🌐 Social: ❌ Not found")
        
        # Count successful extractions
        found_count = sum(1 for contact in [email, phone, address, social] if contact)
        print(f"   ✨ Found {found_count}/4 contact types")
    
    # Summary
    successful = len([r for r in results if 'error' not in r])
    contact_totals = {'email': 0, 'phone': 0, 'address': 0, 'social': 0}
    
    for result in results:
        if 'error' in result:
            continue
        contacts = result.get('contacts', {})
        for contact_type in ['email', 'phone', 'address', 'social_media']:
            if contacts.get(contact_type):
                key = contact_type.replace('_media', '')
                contact_totals[key] += 1
    
    print(f"\n📊 OVERALL ACCURACY SUMMARY:")
    print(f"   • URLs processed: {len(challenging_urls)}")
    print(f"   • Successful: {successful}")
    
    for contact_type, count in contact_totals.items():
        percentage = (count / successful * 100) if successful > 0 else 0
        print(f"   • {contact_type.title()} found: {count}/{successful} ({percentage:.1f}%)")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    extractor.export_to_csv(results, f"accuracy_test_{timestamp}.csv")
    
    return results


async def comprehensive_performance_test():
    """Run comprehensive performance test with all URLs."""
    print("\n🚀 COMPREHENSIVE PERFORMANCE TEST")
    print("=" * 60)
    
    urls = load_test_urls()
    if not urls:
        print("No URLs found")
        return
    
    print(f"Processing ALL {len(urls)} URLs with optimized extractor...")
    
    start_time = datetime.now()
    
    # Use high concurrency for speed
    extractor = SmartContactExtractor(max_concurrent=10)
    results = await extractor.extract_contacts_smart(urls)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # Comprehensive analysis
    print(f"\n🎉 COMPREHENSIVE TEST COMPLETED!")
    print(f"   • Total URLs: {len(urls)}")
    print(f"   • Processing time: {duration:.2f} seconds ({duration/60:.1f} minutes)")
    print(f"   • Average per URL: {duration/len(urls):.2f} seconds")
    print(f"   • Processing rate: {len(urls)/duration:.2f} URLs/second")
    
    # Quality metrics
    successful = 0
    errors = 0
    contact_counts = {'email': 0, 'phone': 0, 'address': 0, 'social': 0}
    total_pages = 0
    high_confidence_contacts = 0
    
    for result in results:
        if 'error' in result:
            errors += 1
            continue
        
        successful += 1
        contacts = result.get('contacts', {})
        metadata = result.get('metadata', {})
        
        total_pages += metadata.get('pages_checked', 0)
        
        # Count contact types and high confidence ones
        for contact_type in ['email', 'phone', 'address', 'social_media']:
            contact = contacts.get(contact_type)
            if contact:
                key = contact_type.replace('_media', '')
                contact_counts[key] += 1
                if contact.get('confidence', 0) >= 0.9:
                    high_confidence_contacts += 1
    
    print(f"\n📊 FINAL PERFORMANCE METRICS:")
    print(f"   • Successful extractions: {successful}/{len(urls)} ({successful/len(urls)*100:.1f}%)")
    print(f"   • Errors: {errors}")
    print(f"   • Average pages per URL: {total_pages/successful:.1f}" if successful > 0 else "")
    print(f"   • High confidence contacts (≥90%): {high_confidence_contacts}")
    
    print(f"\n📞 FINAL CONTACT DISCOVERY RATES:")
    for contact_type, count in contact_counts.items():
        percentage = (count / successful * 100) if successful > 0 else 0
        print(f"   • {contact_type.title()}: {count}/{successful} ({percentage:.1f}%)")
    
    # Export comprehensive results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    extractor.export_to_csv(results, f"comprehensive_optimized_{timestamp}.csv")
    
    print(f"\n💾 Results exported to: comprehensive_optimized_{timestamp}.csv")
    
    return results


async def main():
    """Main test function."""
    print("🔧 OPTIMIZED SMART CONTACT EXTRACTOR TESTS")
    print("Bug fixes + Performance improvements + Accuracy enhancements")
    print("=" * 80)
    
    print("\nChoose test mode:")
    print("1. Phone fix test (3 URLs)")
    print("2. Speed optimization test (10 URLs)")
    print("3. Accuracy improvements test (5 challenging URLs)")
    print("4. Comprehensive performance test (all URLs)")
    print("5. All tests")
    
    try:
        choice = input("\nEnter choice (1-5) or press Enter for option 1: ").strip()
        if not choice:
            choice = "1"
    except KeyboardInterrupt:
        print("\n\nTest cancelled.")
        return
    
    try:
        if choice == "1":
            await test_phone_fix()
        elif choice == "2":
            await test_speed_optimization()
        elif choice == "3":
            await test_accuracy_improvements()
        elif choice == "4":
            await comprehensive_performance_test()
        elif choice == "5":
            await test_phone_fix()
            await test_speed_optimization()
            await test_accuracy_improvements()
            await comprehensive_performance_test()
        else:
            print("Invalid choice. Running phone fix test.")
            await test_phone_fix()
    
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🎉 Optimized extraction testing completed!")
    print("\nKey improvements made:")
    print("✅ Fixed phone number extraction and normalization")
    print("✅ Enhanced phone number validation (filters coordinates)")
    print("✅ Reduced contact page patterns for speed")
    print("✅ Added early stopping when complete contact info found")
    print("✅ Increased concurrency for faster processing")
    print("✅ Better error handling and validation")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n🔧 Optimized test interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
