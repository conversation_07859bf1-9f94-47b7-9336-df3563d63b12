"""
Lead Scraper Master Control - Complete pipeline for processing lead datasets
"""

import os
import sys
import asyncio
from datetime import datetime

def print_banner():
    """Print application banner."""
    print("=" * 80)
    print("🚀 LEAD SCRAPER MASTER CONTROL")
    print("   Complete pipeline for processing lead datasets with contact extraction")
    print("=" * 80)

def print_menu():
    """Print main menu."""
    print("\n📋 AVAILABLE OPERATIONS:")
    print("   1. 🧹 Clean Dataset (Remove invalid URLs)")
    print("   2. 📦 Create Chunks (Break into 1k URL chunks)")
    print("   3. 🔍 Start Scraping (Pausable contact extraction)")
    print("   4. ⏸️  Pause Scraping (Create pause file)")
    print("   5. ▶️  Resume Scraping (Remove pause file)")
    print("   6. 📊 Check Progress (View scraping status)")
    print("   7. 🔗 Combine Results (Create final CSV files)")
    print("   8. 🏃 Run Full Pipeline (Clean → Chunk → Scrape)")
    print("   9. ❌ Exit")

def check_dependencies():
    """Check if required dependencies are available."""
    try:
        import pandas as pd
        from aganl.perfect_contact_extractor import PerfectContactExtractor
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please install required packages:")
        print("   pip install pandas crawl4ai")
        return False

def clean_dataset():
    """Clean the dataset."""
    print("\n" + "="*60)
    print("🧹 CLEANING DATASET")
    print("="*60)
    
    from dataset_cleaner import DatasetCleaner
    
    # Look for dataset files
    csv_files = [f for f in os.listdir('.') if f.endswith('.csv') and not f.endswith('_CLEAN.csv')]
    
    if not csv_files:
        print("❌ No CSV files found in current directory!")
        return False
    
    if len(csv_files) > 1:
        print("🤔 Multiple CSV files found:")
        for i, file in enumerate(csv_files, 1):
            print(f"   {i}. {file}")
        
        try:
            choice = int(input("Select file number: ")) - 1
            input_file = csv_files[choice]
        except (ValueError, IndexError):
            print("Invalid choice. Using first file.")
            input_file = csv_files[0]
    else:
        input_file = csv_files[0]
    
    cleaner = DatasetCleaner()
    try:
        output_file, stats = cleaner.clean_dataset(input_file)
        print(f"✅ Dataset cleaned successfully: {output_file}")
        return True
    except Exception as e:
        print(f"❌ Error cleaning dataset: {e}")
        return False

def create_chunks():
    """Create chunks from clean dataset."""
    print("\n" + "="*60)
    print("📦 CREATING CHUNKS")
    print("="*60)
    
    from dataset_chunker import DatasetChunker
    
    # Look for clean dataset
    clean_files = [f for f in os.listdir('.') if f.endswith('_CLEAN.csv')]
    
    if not clean_files:
        print("❌ No clean dataset found!")
        print("Please run 'Clean Dataset' first.")
        return False
    
    if len(clean_files) > 1:
        print("🤔 Multiple clean datasets found:")
        for i, file in enumerate(clean_files, 1):
            print(f"   {i}. {file}")
        
        try:
            choice = int(input("Select file number: ")) - 1
            input_file = clean_files[choice]
        except (ValueError, IndexError):
            input_file = clean_files[0]
    else:
        input_file = clean_files[0]
    
    try:
        chunk_size = int(input("Enter chunk size (default 1000): ") or "1000")
    except ValueError:
        chunk_size = 1000
    
    chunker = DatasetChunker(chunk_size=chunk_size)
    try:
        manifest = chunker.create_chunks(input_file)
        print(f"✅ Chunks created successfully: {manifest['output_dir']}")
        return True
    except Exception as e:
        print(f"❌ Error creating chunks: {e}")
        return False

async def start_scraping():
    """Start the scraping process."""
    print("\n" + "="*60)
    print("🔍 STARTING SCRAPING")
    print("="*60)
    
    from pausable_scraper import PausableScraper
    
    # Look for chunk directories
    chunk_dirs = [d for d in os.listdir('.') if d.endswith('_chunks') and os.path.isdir(d)]
    
    if not chunk_dirs:
        print("❌ No chunk directories found!")
        print("Please run 'Create Chunks' first.")
        return False
    
    if len(chunk_dirs) > 1:
        print("🤔 Multiple chunk directories found:")
        for i, dir_name in enumerate(chunk_dirs, 1):
            print(f"   {i}. {dir_name}")
        
        try:
            choice = int(input("Select directory number: ")) - 1
            chunks_dir = chunk_dirs[choice]
        except (ValueError, IndexError):
            chunks_dir = chunk_dirs[0]
    else:
        chunks_dir = chunk_dirs[0]
    
    scraper = PausableScraper(chunks_dir)
    try:
        await scraper.run()
        return True
    except Exception as e:
        print(f"❌ Error during scraping: {e}")
        return False

def pause_scraping():
    """Pause the scraping process."""
    print("\n⏸️  PAUSING SCRAPING")
    
    # Look for results directories
    results_dirs = [d for d in os.listdir('.') if d.endswith('_results') and os.path.isdir(d)]
    
    if not results_dirs:
        print("❌ No active scraping sessions found!")
        return
    
    for results_dir in results_dirs:
        pause_file = os.path.join(results_dir, 'PAUSE_SCRAPING')
        with open(pause_file, 'w') as f:
            f.write(f"Scraping paused at: {datetime.now().isoformat()}\n")
        print(f"✅ Pause file created: {pause_file}")

def resume_scraping():
    """Resume the scraping process."""
    print("\n▶️  RESUMING SCRAPING")
    
    # Look for pause files
    results_dirs = [d for d in os.listdir('.') if d.endswith('_results') and os.path.isdir(d)]
    
    paused_sessions = []
    for results_dir in results_dirs:
        pause_file = os.path.join(results_dir, 'PAUSE_SCRAPING')
        if os.path.exists(pause_file):
            paused_sessions.append((results_dir, pause_file))
    
    if not paused_sessions:
        print("❌ No paused scraping sessions found!")
        return
    
    for results_dir, pause_file in paused_sessions:
        os.remove(pause_file)
        print(f"✅ Pause file removed: {pause_file}")
    
    print("🚀 Scraping can now be resumed. Run 'Start Scraping' to continue.")

def check_progress():
    """Check scraping progress."""
    print("\n📊 CHECKING PROGRESS")
    
    # Look for progress files
    results_dirs = [d for d in os.listdir('.') if d.endswith('_results') and os.path.isdir(d)]
    
    if not results_dirs:
        print("❌ No scraping sessions found!")
        return
    
    import json
    
    for results_dir in results_dirs:
        progress_file = os.path.join(results_dir, 'scraping_progress.json')
        if os.path.exists(progress_file):
            with open(progress_file, 'r') as f:
                progress = json.load(f)
            
            print(f"\n📂 Session: {results_dir}")
            print(f"   • Started: {progress.get('started_at', 'Unknown')}")
            print(f"   • Last updated: {progress.get('last_updated', 'Unknown')}")
            print(f"   • Completed chunks: {len(progress.get('completed_chunks', []))}")
            print(f"   • Current chunk: {progress.get('current_chunk', 'None')}")
            print(f"   • URLs processed: {progress.get('total_processed', 0):,}")
            print(f"   • Emails found: {progress.get('total_with_emails', 0):,}")
            print(f"   • Phones found: {progress.get('total_with_phones', 0):,}")
            
            # Check if paused
            pause_file = os.path.join(results_dir, 'PAUSE_SCRAPING')
            if os.path.exists(pause_file):
                print(f"   ⏸️  Status: PAUSED")
            else:
                print(f"   ▶️  Status: ACTIVE/READY")

def combine_results():
    """Combine chunk results."""
    print("\n" + "="*60)
    print("🔗 COMBINING RESULTS")
    print("="*60)
    
    from results_combiner import ResultsCombiner
    
    # Look for results directories
    results_dirs = [d for d in os.listdir('.') if d.endswith('_results') and os.path.isdir(d)]
    
    if not results_dirs:
        print("❌ No results directories found!")
        return False
    
    if len(results_dirs) > 1:
        print("🤔 Multiple results directories found:")
        for i, dir_name in enumerate(results_dirs, 1):
            print(f"   {i}. {dir_name}")
        
        try:
            choice = int(input("Select directory number: ")) - 1
            results_dir = results_dirs[choice]
        except (ValueError, IndexError):
            results_dir = results_dirs[0]
    else:
        results_dir = results_dirs[0]
    
    output_prefix = input("Enter output file prefix (or press Enter for default): ").strip()
    if not output_prefix:
        output_prefix = None
    
    combiner = ResultsCombiner(results_dir)
    try:
        summary = combiner.combine_results(output_prefix)
        print("✅ Results combined successfully!")
        return True
    except Exception as e:
        print(f"❌ Error combining results: {e}")
        return False

async def run_full_pipeline():
    """Run the complete pipeline."""
    print("\n🏃 RUNNING FULL PIPELINE")
    print("This will: Clean Dataset → Create Chunks → Start Scraping")
    
    confirm = input("Continue? (y/N): ").lower().strip()
    if confirm != 'y':
        print("Pipeline cancelled.")
        return
    
    # Step 1: Clean dataset
    if not clean_dataset():
        print("❌ Pipeline failed at cleaning step.")
        return
    
    # Step 2: Create chunks
    if not create_chunks():
        print("❌ Pipeline failed at chunking step.")
        return
    
    # Step 3: Start scraping
    print("\n🔍 Starting scraping phase...")
    print("You can pause anytime by pressing Ctrl+C or creating a PAUSE_SCRAPING file.")
    
    if await start_scraping():
        print("✅ Pipeline completed successfully!")
        
        # Ask if user wants to combine results
        combine = input("\nCombine results now? (Y/n): ").lower().strip()
        if combine != 'n':
            combine_results()
    else:
        print("❌ Pipeline failed at scraping step.")

async def main():
    """Main application loop."""
    print_banner()
    
    # Check dependencies
    if not check_dependencies():
        return
    
    while True:
        print_menu()
        
        try:
            choice = input("\nSelect operation (1-9): ").strip()
            
            if choice == '1':
                clean_dataset()
            elif choice == '2':
                create_chunks()
            elif choice == '3':
                await start_scraping()
            elif choice == '4':
                pause_scraping()
            elif choice == '5':
                resume_scraping()
            elif choice == '6':
                check_progress()
            elif choice == '7':
                combine_results()
            elif choice == '8':
                await run_full_pipeline()
            elif choice == '9':
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please select 1-9.")
                
        except KeyboardInterrupt:
            print("\n⚠️  Operation interrupted by user.")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
        
        input("\nPress Enter to continue...")

if __name__ == "__main__":
    asyncio.run(main())
