"""
Test the intelligent contact extractor with smart page discovery
"""

import asyncio
import csv
from datetime import datetime
from typing import List
from intelligent_contact_extractor import IntelligentContactExtractor


def load_test_urls() -> List[str]:
    """Load URLs from test_data.csv"""
    urls = []
    try:
        with open("test_data.csv", 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                url = row.get('website_url', '').strip()
                if url and url.startswith(('http://', 'https://')):
                    urls.append(url)
        return urls
    except Exception as e:
        print(f"Error loading URLs: {e}")
        return []


async def test_intelligent_discovery():
    """Test intelligent page discovery on a few URLs."""
    print("🧠 INTELLIGENT CONTACT EXTRACTOR TEST")
    print("=" * 60)
    print("Features:")
    print("• Discovers available pages first (HEAD requests)")
    print("• Prioritizes pages by contact likelihood")
    print("• Only checks the most promising pages")
    print("• Early stopping when contacts found")
    print("=" * 60)
    
    # Test with URLs that should have different page structures
    test_urls = [
        "https://www.defer.coffee/strip-district",  # Has contact-us page
        "https://thejamescafe.com/",               # Simple structure
        "https://convivecoffee.com/",              # Multiple pages
        "http://www.yinzcoffee.com/",              # Different structure
        "https://www.novariacoffee.com/"           # Minimal pages
    ]
    
    print(f"🎯 Testing {len(test_urls)} URLs with intelligent discovery...")
    
    start_time = datetime.now()
    
    extractor = IntelligentContactExtractor(batch_size=10, max_concurrent=4)
    results = await extractor.extract_intelligent(test_urls)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print(f"\n🧠 INTELLIGENT DISCOVERY RESULTS:")
    
    for i, result in enumerate(results, 1):
        if 'error' in result:
            print(f"{i}. ❌ {result['url']}: {result['error']}")
            continue
        
        url = result['url']
        email = result.get('email')
        social = result.get('social_media')
        pages_discovered = result.get('pages_discovered', 0)
        pages_checked = result.get('pages_checked', 0)
        
        domain = url.split('/')[2].replace('www.', '')
        
        print(f"\n{i}. 🏪 {domain}")
        print(f"   📄 Pages discovered: {pages_discovered}")
        print(f"   🔍 Pages checked: {pages_checked}")
        print(f"   ⚡ Efficiency: {pages_checked}/{pages_discovered} ({pages_checked/pages_discovered*100:.1f}%)" if pages_discovered > 0 else "")
        
        if email:
            print(f"   📧 Email: {email['email']} (confidence: {email['confidence']:.1%})")
        else:
            print(f"   📧 Email: Not found")
        
        if social:
            print(f"   🌐 Social: {social['platform']} {social.get('handle', '')} (confidence: {social['confidence']:.1%})")
        else:
            print(f"   🌐 Social: Not found")
    
    # Summary
    extractor.print_summary(results)
    
    print(f"\n⚡ PERFORMANCE COMPARISON:")
    print(f"   • Processing time: {duration:.2f} seconds")
    print(f"   • Rate: {len(test_urls)/duration:.2f} URLs/second")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    extractor.export_to_csv(results, f"intelligent_test_{timestamp}.csv")
    
    return results


async def test_full_intelligent_extraction():
    """Test intelligent extraction on all URLs."""
    print("\n🚀 FULL INTELLIGENT EXTRACTION TEST")
    print("=" * 60)
    
    urls = load_test_urls()
    if not urls:
        print("No URLs found")
        return
    
    print(f"🎯 Processing ALL {len(urls)} URLs with intelligent discovery...")
    
    start_time = datetime.now()
    
    extractor = IntelligentContactExtractor(batch_size=25, max_concurrent=5)
    results = await extractor.extract_intelligent(urls)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # Detailed analysis
    extractor.print_summary(results)
    
    # Show efficiency gains
    successful_results = [r for r in results if 'error' not in r]
    
    if successful_results:
        total_discovered = sum(r.get('pages_discovered', 0) for r in successful_results)
        total_checked = sum(r.get('pages_checked', 0) for r in successful_results)
        
        print(f"\n🧠 INTELLIGENCE METRICS:")
        print(f"   • Total pages discovered: {total_discovered}")
        print(f"   • Total pages checked: {total_checked}")
        print(f"   • Pages saved by intelligence: {total_discovered - total_checked}")
        print(f"   • Efficiency gain: {(total_discovered - total_checked)/total_discovered*100:.1f}%" if total_discovered > 0 else "")
        
        # Show sample successful extractions
        successful_contacts = [r for r in successful_results if r.get('email') or r.get('social_media')]
        
        print(f"\n🏆 SAMPLE SUCCESSFUL EXTRACTIONS:")
        for result in successful_contacts[:8]:
            url = result['url']
            email = result.get('email')
            social = result.get('social_media')
            pages_discovered = result.get('pages_discovered', 0)
            pages_checked = result.get('pages_checked', 0)
            
            domain = url.split('/')[2].replace('www.', '')
            
            email_text = email['email'] if email else "None"
            social_text = f"{social['platform']} {social.get('handle', '')}" if social else "None"
            
            print(f"   • {domain} ({pages_checked}/{pages_discovered} pages)")
            print(f"     📧 {email_text}")
            print(f"     🌐 {social_text}")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    extractor.export_to_csv(results, f"intelligent_full_{timestamp}.csv")
    
    print(f"\n💾 Results exported to: intelligent_full_{timestamp}.csv")
    
    # Performance projections
    print(f"\n📈 SCALING PROJECTIONS:")
    rate = len(urls) / duration
    print(f"   • Current rate: {rate:.2f} URLs/second")
    print(f"   • 1,000 URLs: ~{1000/rate/60:.1f} minutes")
    print(f"   • 10,000 URLs: ~{10000/rate/60:.1f} minutes")
    
    return results


async def compare_with_previous():
    """Compare intelligent extractor with previous versions."""
    print("\n📊 COMPARISON WITH PREVIOUS VERSIONS")
    print("=" * 60)
    
    # Test a few URLs to compare efficiency
    test_urls = [
        "https://www.defer.coffee/strip-district",
        "https://thejamescafe.com/",
        "https://convivecoffee.com/"
    ]
    
    print("Comparing extraction approaches:")
    print("• Previous: Check all possible contact pages blindly")
    print("• Intelligent: Discover pages first, prioritize best ones")
    
    extractor = IntelligentContactExtractor(batch_size=5, max_concurrent=3)
    results = await extractor.extract_intelligent(test_urls)
    
    print(f"\n📈 EFFICIENCY COMPARISON:")
    
    for result in results:
        if 'error' in result:
            continue
        
        url = result['url']
        pages_discovered = result.get('pages_discovered', 0)
        pages_checked = result.get('pages_checked', 0)
        
        domain = url.split('/')[2].replace('www.', '')
        
        # Estimate what previous version would have done
        estimated_previous_pages = 4  # Main + contact + contact-us + about
        pages_saved = max(0, estimated_previous_pages - pages_checked)
        
        print(f"   • {domain}:")
        print(f"     Previous approach: ~{estimated_previous_pages} pages")
        print(f"     Intelligent approach: {pages_checked} pages")
        print(f"     Pages saved: {pages_saved}")
        print(f"     Time saved: ~{pages_saved * 3:.0f} seconds")
    
    return results


async def main():
    """Main test function."""
    print("🧠 INTELLIGENT CONTACT EXTRACTOR TESTS")
    print("Smart page discovery + prioritization for maximum efficiency")
    print("=" * 70)
    
    print("\nChoose test mode:")
    print("1. Intelligent discovery demo (5 URLs)")
    print("2. Full intelligent extraction (all URLs)")
    print("3. Efficiency comparison")
    print("4. All tests")
    
    try:
        choice = input("\nEnter choice (1-4) or press Enter for option 1: ").strip()
        if not choice:
            choice = "1"
    except KeyboardInterrupt:
        print("\n\nTest cancelled.")
        return
    
    try:
        if choice == "1":
            await test_intelligent_discovery()
        elif choice == "2":
            await test_full_intelligent_extraction()
        elif choice == "3":
            await compare_with_previous()
        elif choice == "4":
            await test_intelligent_discovery()
            await test_full_intelligent_extraction()
            await compare_with_previous()
        else:
            print("Invalid choice. Running intelligent discovery demo.")
            await test_intelligent_discovery()
    
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🎉 Intelligent extraction testing completed!")
    print("\nIntelligent features:")
    print("✅ Discovers available pages with HEAD requests")
    print("✅ Prioritizes pages by contact likelihood")
    print("✅ Only checks the most promising pages")
    print("✅ Early stopping when both contacts found")
    print("✅ Maximum efficiency for large-scale processing")
    print("✅ Ready for thousands of URLs!")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n🧠 Intelligent test interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
