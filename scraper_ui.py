"""
Tkinter UI for Pausable Contact Scraper
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import asyncio
import threading
import json
import os
from datetime import datetime
from pathlib import Path

# Import the pausable scraper
from pausable_scraper import PausableScraper

class PausableScraperWithUI(PausableScraper):
    """Enhanced scraper with UI progress callbacks."""

    def __init__(self, chunks_dir: str, results_dir: str = None, progress_callback=None):
        super().__init__(chunks_dir, results_dir)
        self.progress_callback = progress_callback
        self.total_chunks = 0
        self.completed_chunks = 0
        self.cumulative_stats = {
            'total_processed': 0,
            'total_with_emails': 0,
            'total_with_phones': 0
        }

    async def run(self):
        """Run with UI progress updates."""
        # Load manifest and progress
        manifest = self.load_chunk_manifest()
        progress = self.load_progress()

        self.total_chunks = manifest['num_chunks']
        completed_chunks_set = set(progress['completed_chunks'])
        self.completed_chunks = len(completed_chunks_set)

        # Update cumulative stats from existing progress
        self.cumulative_stats.update({
            'total_processed': progress.get('total_processed', 0),
            'total_with_emails': progress.get('total_with_emails', 0),
            'total_with_phones': progress.get('total_with_phones', 0)
        })

        # Initial progress update
        if self.progress_callback:
            self.progress_callback(self.completed_chunks, self.total_chunks, self.cumulative_stats)

        # Process remaining chunks
        for chunk_info in manifest['chunks']:
            chunk_num = chunk_info['chunk_number']

            # Skip completed chunks
            if chunk_num in completed_chunks_set:
                continue

            # Check for pause request
            if self.is_pause_requested():
                break

            self.current_chunk = chunk_num
            progress['current_chunk'] = chunk_num
            self.save_progress(progress)

            try:
                # Process chunk
                chunk_stats = await self.process_chunk(chunk_info)

                # Update progress
                progress['completed_chunks'].append(chunk_num)
                progress['total_processed'] += chunk_stats['urls_processed']
                progress['total_with_emails'] += chunk_stats['emails_found']
                progress['total_with_phones'] += chunk_stats['phones_found']
                progress['current_chunk'] = None

                self.save_progress(progress)

                # Update cumulative stats
                self.cumulative_stats.update({
                    'total_processed': progress['total_processed'],
                    'total_with_emails': progress['total_with_emails'],
                    'total_with_phones': progress['total_with_phones']
                })

                # Add chunk-specific stats
                chunk_stats.update(self.cumulative_stats)

                self.completed_chunks += 1

                # Update UI
                if self.progress_callback:
                    self.progress_callback(self.completed_chunks, self.total_chunks, chunk_stats)

            except Exception as e:
                print(f"Error processing chunk {chunk_num}: {str(e)}")
                continue

class ScraperUI:
    """Tkinter UI for the pausable contact scraper."""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🔍 Lead Contact Scraper")
        self.root.geometry("900x700")
        self.root.configure(bg='#f0f0f0')
        
        # State variables
        self.scraper = None
        self.scraper_thread = None
        self.is_running = False
        self.chunks_dir = None
        self.results_dir = None

        # Create UI
        self.create_widgets()
        self.load_available_sessions()
    
    def create_widgets(self):
        """Create the UI widgets."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="🔍 Lead Contact Scraper", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Session selection
        ttk.Label(main_frame, text="Chunks Directory:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.chunks_var = tk.StringVar()
        self.chunks_combo = ttk.Combobox(main_frame, textvariable=self.chunks_var,
                                        state="readonly", width=50)
        self.chunks_combo.grid(row=1, column=1, sticky="ew", pady=5, padx=(5, 0))
        self.chunks_combo.bind('<<ComboboxSelected>>', self.on_session_selected)
        
        ttk.Button(main_frame, text="Browse", command=self.browse_chunks_dir).grid(
            row=1, column=2, pady=5, padx=(5, 0))
        
        # Control buttons frame
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=3, pady=20, sticky=(tk.W, tk.E))
        control_frame.columnconfigure(0, weight=1)
        control_frame.columnconfigure(1, weight=1)
        control_frame.columnconfigure(2, weight=1)
        control_frame.columnconfigure(3, weight=1)
        
        # Control buttons
        self.start_btn = ttk.Button(control_frame, text="▶️ Start Scraping", 
                                   command=self.start_scraping, style="Accent.TButton")
        self.start_btn.grid(row=0, column=0, padx=5, sticky=(tk.W, tk.E))
        
        self.pause_btn = ttk.Button(control_frame, text="⏸️ Pause", 
                                   command=self.pause_scraping, state=tk.DISABLED)
        self.pause_btn.grid(row=0, column=1, padx=5, sticky=(tk.W, tk.E))
        
        self.resume_btn = ttk.Button(control_frame, text="▶️ Resume", 
                                    command=self.resume_scraping, state=tk.DISABLED)
        self.resume_btn.grid(row=0, column=2, padx=5, sticky=(tk.W, tk.E))
        
        self.stop_btn = ttk.Button(control_frame, text="⏹️ Stop", 
                                  command=self.stop_scraping, state=tk.DISABLED)
        self.stop_btn.grid(row=0, column=3, padx=5, sticky=(tk.W, tk.E))
        
        # Progress frame
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding="10")
        progress_frame.grid(row=3, column=0, columnspan=3, pady=10, sticky=(tk.W, tk.E))
        progress_frame.columnconfigure(1, weight=1)
        
        # Progress bars and labels
        ttk.Label(progress_frame, text="Overall Progress:").grid(row=0, column=0, sticky=tk.W)
        self.overall_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.overall_progress.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        self.overall_label = ttk.Label(progress_frame, text="0/0 chunks")
        self.overall_label.grid(row=0, column=2, padx=(10, 0))
        
        ttk.Label(progress_frame, text="Current Chunk:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.chunk_progress = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.chunk_progress.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=(5, 0))
        self.chunk_label = ttk.Label(progress_frame, text="Ready")
        self.chunk_label.grid(row=1, column=2, padx=(10, 0), pady=(5, 0))
        
        # Statistics frame
        stats_frame = ttk.Frame(progress_frame)
        stats_frame.grid(row=2, column=0, columnspan=3, pady=(10, 0), sticky=(tk.W, tk.E))
        stats_frame.columnconfigure(0, weight=1)
        stats_frame.columnconfigure(1, weight=1)
        stats_frame.columnconfigure(2, weight=1)
        
        self.urls_label = ttk.Label(stats_frame, text="URLs: 0")
        self.urls_label.grid(row=0, column=0)
        
        self.emails_label = ttk.Label(stats_frame, text="📧 Emails: 0")
        self.emails_label.grid(row=0, column=1)
        
        self.phones_label = ttk.Label(stats_frame, text="📞 Phones: 0")
        self.phones_label.grid(row=0, column=2)
        
        # Log frame
        log_frame = ttk.LabelFrame(main_frame, text="Scraping Log", padding="5")
        log_frame.grid(row=4, column=0, columnspan=3, pady=10, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready - Select chunks directory to begin")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def log_message(self, message):
        """Add message to log."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def load_available_sessions(self):
        """Load available chunk directories."""
        chunk_dirs = [d for d in os.listdir('.') if d.endswith('_chunks') and os.path.isdir(d)]
        self.chunks_combo['values'] = chunk_dirs
        
        if chunk_dirs:
            self.chunks_combo.set(chunk_dirs[0])
            self.on_session_selected()
    
    def browse_chunks_dir(self):
        """Browse for chunks directory."""
        directory = filedialog.askdirectory(title="Select Chunks Directory")
        if directory:
            self.chunks_var.set(directory)
            self.on_session_selected()
    
    def on_session_selected(self, event=None):
        """Handle session selection."""
        chunks_dir = self.chunks_var.get()
        if not chunks_dir or not os.path.exists(chunks_dir):
            return
        
        self.chunks_dir = chunks_dir
        self.results_dir = f"{chunks_dir}_results"
        
        # Load manifest to get chunk info
        try:
            manifest_path = os.path.join(chunks_dir, 'chunk_manifest.json')
            with open(manifest_path, 'r') as f:
                manifest = json.load(f)
            
            total_chunks = manifest['num_chunks']
            total_records = manifest['total_records']
            
            self.log_message(f"Selected: {chunks_dir}")
            self.log_message(f"Total chunks: {total_chunks}, Total URLs: {total_records:,}")
            
            # Update progress
            self.overall_progress['maximum'] = total_chunks
            self.overall_label.config(text=f"0/{total_chunks} chunks")
            
            # Load existing progress if available
            self.load_progress()
            
            self.status_var.set(f"Ready - {total_chunks} chunks, {total_records:,} URLs")
            
        except Exception as e:
            self.log_message(f"Error loading session: {e}")
            self.status_var.set("Error loading session")
    
    def load_progress(self):
        """Load existing progress."""
        if not self.results_dir:
            return
        
        progress_file = os.path.join(self.results_dir, 'scraping_progress.json')
        if os.path.exists(progress_file):
            try:
                with open(progress_file, 'r') as f:
                    progress = json.load(f)
                
                completed = len(progress.get('completed_chunks', []))
                total_processed = progress.get('total_processed', 0)
                emails_found = progress.get('total_with_emails', 0)
                phones_found = progress.get('total_with_phones', 0)
                
                self.overall_progress['value'] = completed
                self.urls_label.config(text=f"URLs: {total_processed:,}")
                self.emails_label.config(text=f"📧 Emails: {emails_found:,}")
                self.phones_label.config(text=f"📞 Phones: {phones_found:,}")
                
                # Check if paused
                pause_file = os.path.join(self.results_dir, 'PAUSE_SCRAPING')
                if os.path.exists(pause_file):
                    self.status_var.set("Paused - Click Resume to continue")
                    self.resume_btn.config(state=tk.NORMAL)
                
                self.log_message(f"Loaded progress: {completed} chunks completed")
                
            except Exception as e:
                self.log_message(f"Error loading progress: {e}")
    
    def start_scraping(self):
        """Start the scraping process."""
        if not self.chunks_dir:
            messagebox.showerror("Error", "Please select a chunks directory first")
            return

        if not os.path.exists(self.chunks_dir):
            messagebox.showerror("Error", f"Chunks directory not found: {self.chunks_dir}")
            return

        if self.is_running:
            messagebox.showwarning("Warning", "Scraping is already running")
            return

        # Check if manifest exists
        manifest_path = os.path.join(self.chunks_dir, 'chunk_manifest.json')
        if not os.path.exists(manifest_path):
            messagebox.showerror("Error", f"Chunk manifest not found: {manifest_path}")
            return

        self.is_running = True
        self.start_btn.config(state=tk.DISABLED)
        self.pause_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.NORMAL)
        self.resume_btn.config(state=tk.DISABLED)

        self.status_var.set("Starting scraper...")
        self.log_message("🚀 Starting contact extraction...")

        # Start scraping in a separate thread
        self.scraper_thread = threading.Thread(target=self.run_scraper, daemon=True)
        self.scraper_thread.start()
    
    def run_scraper(self):
        """Run the scraper in async context."""
        try:
            # Ensure we have valid directories
            if not self.chunks_dir or not self.results_dir:
                raise ValueError("Chunks or results directory not set")

            # Create scraper with progress callback
            self.scraper = PausableScraperWithUI(self.chunks_dir, self.results_dir, self.update_progress)

            # Start chunk progress animation
            self.root.after(0, lambda: self.chunk_progress.start(10))

            # Run scraper using asyncio.run (simpler approach)
            asyncio.run(self.scraper.run())

            self.root.after(0, self.scraping_completed)

        except Exception as e:
            error_msg = str(e)
            self.root.after(0, lambda msg=error_msg: self.scraping_error(msg))

    def update_progress(self, chunk_num, total_chunks, chunk_stats=None):
        """Update progress from scraper."""
        def update_ui():
            self.overall_progress['value'] = chunk_num
            self.overall_label.config(text=f"{chunk_num}/{total_chunks} chunks")

            if chunk_stats:
                self.urls_label.config(text=f"URLs: {chunk_stats.get('total_processed', 0):,}")
                self.emails_label.config(text=f"📧 Emails: {chunk_stats.get('total_with_emails', 0):,}")
                self.phones_label.config(text=f"📞 Phones: {chunk_stats.get('total_with_phones', 0):,}")

                self.log_message(f"✅ Chunk {chunk_num} completed - "
                               f"Emails: {chunk_stats.get('emails_found', 0)}, "
                               f"Phones: {chunk_stats.get('phones_found', 0)}")

            if chunk_num < total_chunks:
                self.chunk_label.config(text=f"Processing chunk {chunk_num + 1}")
                self.status_var.set(f"Processing chunk {chunk_num + 1}/{total_chunks}")
            else:
                self.chunk_progress.stop()
                self.chunk_label.config(text="Completed")

        self.root.after(0, update_ui)
    
    def scraping_completed(self):
        """Handle scraping completion."""
        self.is_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.pause_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.DISABLED)
        self.resume_btn.config(state=tk.DISABLED)
        
        self.chunk_progress.stop()
        self.chunk_label.config(text="Completed")
        self.status_var.set("Scraping completed!")
        self.log_message("🎉 Contact extraction completed!")
        
        # Load final progress
        self.load_progress()
        
        # Ask if user wants to combine results
        if messagebox.askyesno("Complete", "Scraping completed! Would you like to combine results now?"):
            self.combine_results()
    
    def scraping_error(self, error_msg):
        """Handle scraping error."""
        self.is_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.pause_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.DISABLED)
        
        self.chunk_progress.stop()
        self.status_var.set("Error occurred")
        self.log_message(f"❌ Error: {error_msg}")
        
        messagebox.showerror("Scraping Error", f"An error occurred:\n{error_msg}")
    
    def pause_scraping(self):
        """Pause the scraping process."""
        if not self.results_dir:
            return
        
        pause_file = os.path.join(self.results_dir, 'PAUSE_SCRAPING')
        with open(pause_file, 'w') as f:
            f.write(f"Scraping paused at: {datetime.now().isoformat()}\n")
        
        self.pause_btn.config(state=tk.DISABLED)
        self.resume_btn.config(state=tk.NORMAL)
        self.status_var.set("Pausing scraper...")
        self.log_message("⏸️ Pause requested - will pause after current chunk")
    
    def resume_scraping(self):
        """Resume the scraping process."""
        if not self.results_dir:
            return
        
        pause_file = os.path.join(self.results_dir, 'PAUSE_SCRAPING')
        if os.path.exists(pause_file):
            os.remove(pause_file)
        
        self.resume_btn.config(state=tk.DISABLED)
        self.status_var.set("Resuming...")
        self.log_message("▶️ Pause file removed - ready to resume")
        
        # Start scraping again
        self.start_scraping()
    
    def stop_scraping(self):
        """Stop the scraping process."""
        if messagebox.askyesno("Confirm", "Are you sure you want to stop scraping?\nProgress will be saved."):
            self.pause_scraping()  # This will cause graceful stop
            self.is_running = False
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            self.log_message("⏹️ Scraping stopped by user")
    
    def combine_results(self):
        """Combine results using the results combiner."""
        if not self.results_dir or not os.path.exists(self.results_dir):
            messagebox.showerror("Error", "No results directory found")
            return
        
        try:
            from results_combiner import ResultsCombiner
            
            self.log_message("🔗 Combining results...")
            self.status_var.set("Combining results...")
            
            combiner = ResultsCombiner(self.results_dir)
            summary = combiner.combine_results()
            
            if summary:
                self.log_message("✅ Results combined successfully!")
                self.status_var.set("Results combined!")
                
                # Show summary
                emails = summary['contacts_found']['emails']
                phones_only = summary['segmentation']['companies_with_phones_only']
                
                messagebox.showinfo("Results Combined", 
                    f"Results combined successfully!\n\n"
                    f"📧 Companies with emails: {emails:,}\n"
                    f"📞 Companies with phones only: {phones_only:,}\n\n"
                    f"Files created in current directory.")
            
        except Exception as e:
            self.log_message(f"❌ Error combining results: {e}")
            messagebox.showerror("Error", f"Error combining results:\n{e}")

def main():
    """Main function to run the UI."""
    # Check dependencies first
    try:
        import pandas as pd
        from aganl.perfect_contact_extractor import PerfectContactExtractor
    except ImportError as e:
        import tkinter.messagebox as mb
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        mb.showerror("Missing Dependencies",
                    f"Required modules not found: {e}\n\n"
                    "Please install required packages:\n"
                    "pip install pandas crawl4ai")
        return

    root = tk.Tk()

    # Configure ttk styles
    style = ttk.Style()
    style.theme_use('clam')

    # Create custom style for accent button
    style.configure("Accent.TButton", foreground="white", background="#0078d4")
    style.map("Accent.TButton", background=[('active', '#106ebe')])

    try:
        app = ScraperUI(root)
        root.mainloop()
    except KeyboardInterrupt:
        print("Application interrupted")
    except Exception as e:
        import tkinter.messagebox as mb
        mb.showerror("Application Error", f"An error occurred:\n{e}")
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
