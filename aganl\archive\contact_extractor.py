"""
Advanced Contact Information Extractor using crawl4ai
Leverages crawl4ai's built-in extraction strategies for comprehensive contact data extraction.
"""

import asyncio
import json
import re
import csv
from datetime import datetime
from typing import Dict, List, Optional, Union
from urllib.parse import urljoin, urlparse

from crawl4ai import Async<PERSON>ebCrawler, CrawlResult
from crawl4ai import JsonCssExtractionStrategy, RegexExtractionStrategy, LLMExtractionStrategy


class ContactExtractor:
    """
    Advanced contact information extractor using crawl4ai's extraction strategies.
    """
    
    def __init__(self, use_llm: bool = False, llm_config: Optional[Dict] = None):
        """
        Initialize the ContactExtractor.
        
        Args:
            use_llm: Whether to use LLM-based extraction for enhanced accuracy
            llm_config: Configuration for LLM extraction strategy
        """
        self.use_llm = use_llm
        self.llm_config = llm_config or {}
        self.extraction_strategies = self._setup_extraction_strategies()
    
    def _setup_extraction_strategies(self) -> Dict:
        """Setup various extraction strategies for different contact information types."""
        
        # CSS-based extraction strategy for structured contact information
        css_strategy = JsonCssExtractionStrategy({
            "emails": {
                "selector": "a[href^='mailto:'], [data-email], .email, .contact-email",
                "attribute": "href",
                "transform": "lambda x: x.replace('mailto:', '') if x.startswith('mailto:') else x"
            },
            "phones": {
                "selector": "a[href^='tel:'], [data-phone], .phone, .contact-phone, .telephone",
                "attribute": "href",
                "transform": "lambda x: x.replace('tel:', '') if x.startswith('tel:') else x"
            },
            "addresses": {
                "selector": ".address, .location, .contact-address, [itemtype*='PostalAddress']",
                "attribute": "text"
            },
            "social_links": {
                "selector": "a[href*='facebook.com'], a[href*='twitter.com'], a[href*='linkedin.com'], a[href*='instagram.com'], a[href*='youtube.com']",
                "attribute": "href"
            },
            "contact_forms": {
                "selector": "form[action*='contact'], form.contact-form, #contact-form",
                "attribute": "action"
            },
            "names_titles": {
                "selector": ".name, .contact-name, .staff-name, .team-member, h1, h2, h3",
                "attribute": "text"
            }
        })
        
        # Regex-based extraction for pattern matching
        regex_strategy = RegexExtractionStrategy(
            custom={
                "emails": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
                "phones": r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})|(?:\+?[1-9]\d{0,3}[-.\s]?)?\(?([0-9]{1,4})\)?[-.\s]?([0-9]{1,4})[-.\s]?([0-9]{1,9})',
                "addresses": r'\d+\s+[A-Za-z0-9\s,.-]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr|Court|Ct|Place|Pl)\s*,?\s*[A-Za-z\s]+,?\s*[A-Z]{2}\s*\d{5}(?:-\d{4})?',
                "websites": r'https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?)?'
            }
        )
        
        strategies = {
            "css": css_strategy,
            "regex": regex_strategy
        }
        
        # Add LLM strategy if enabled
        if self.use_llm:
            llm_strategy = LLMExtractionStrategy(
                provider="openai",  # or other providers
                api_token=self.llm_config.get("api_token"),
                instruction="""
                Extract all contact information from this webpage including:
                1. Email addresses
                2. Phone numbers (with country codes if available)
                3. Physical addresses
                4. Social media profiles
                5. Contact person names and titles
                6. Company information
                
                Return the information in a structured JSON format with confidence scores.
                """,
                schema={
                    "type": "object",
                    "properties": {
                        "emails": {"type": "array", "items": {"type": "string"}},
                        "phones": {"type": "array", "items": {"type": "string"}},
                        "addresses": {"type": "array", "items": {"type": "string"}},
                        "social_media": {"type": "array", "items": {"type": "object"}},
                        "contacts": {"type": "array", "items": {"type": "object"}},
                        "company": {"type": "object"}
                    }
                }
            )
            strategies["llm"] = llm_strategy
        
        return strategies
    
    async def extract_contacts(self, urls: Union[str, List[str]], 
                             strategy: str = "comprehensive") -> List[Dict]:
        """
        Extract contact information from one or more URLs.
        
        Args:
            urls: Single URL or list of URLs to extract contacts from
            strategy: Extraction strategy ('css', 'regex', 'llm', or 'comprehensive')
            
        Returns:
            List of dictionaries containing extracted contact information
        """
        if isinstance(urls, str):
            urls = [urls]
        
        results = []
        
        async with AsyncWebCrawler(verbose=True) as crawler:
            for url in urls:
                try:
                    print(f"Extracting contacts from: {url}")
                    
                    if strategy == "comprehensive":
                        # Use multiple strategies and combine results
                        contact_data = await self._comprehensive_extraction(crawler, url)
                    else:
                        # Use specific strategy
                        extraction_strategy = self.extraction_strategies.get(strategy)
                        if not extraction_strategy:
                            raise ValueError(f"Unknown strategy: {strategy}")
                        
                        result = await crawler.arun(
                            url=url,
                            extraction_strategy=extraction_strategy,
                            bypass_cache=True
                        )
                        
                        contact_data = self._process_extraction_result(result, url, strategy)
                    
                    results.append(contact_data)
                    
                except Exception as e:
                    print(f"Error extracting from {url}: {str(e)}")
                    results.append({
                        "url": url,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat(),
                        "contacts": {}
                    })
        
        return results
    
    async def _comprehensive_extraction(self, crawler: AsyncWebCrawler, url: str) -> Dict:
        """Perform comprehensive extraction using multiple strategies."""
        
        # First, get the basic crawl result
        basic_result = await crawler.arun(url=url, bypass_cache=True)
        
        # Apply CSS extraction
        css_result = await crawler.arun(
            url=url,
            extraction_strategy=self.extraction_strategies["css"],
            bypass_cache=True
        )
        
        # Apply regex extraction
        regex_result = await crawler.arun(
            url=url,
            extraction_strategy=self.extraction_strategies["regex"],
            bypass_cache=True
        )
        
        # Combine and process results
        combined_data = self._combine_extraction_results(
            basic_result, css_result, regex_result, url
        )
        
        # Apply LLM extraction if enabled
        if self.use_llm and "llm" in self.extraction_strategies:
            try:
                llm_result = await crawler.arun(
                    url=url,
                    extraction_strategy=self.extraction_strategies["llm"],
                    bypass_cache=True
                )
                combined_data = self._merge_llm_results(combined_data, llm_result)
            except Exception as e:
                print(f"LLM extraction failed for {url}: {str(e)}")
        
        return combined_data
    
    def _process_extraction_result(self, result: CrawlResult, url: str, strategy: str) -> Dict:
        """Process extraction result into standardized format."""

        extracted_content = {}

        # Handle extraction strategy results
        if hasattr(result, 'extracted_content') and result.extracted_content:
            try:
                extracted_content = json.loads(result.extracted_content)
            except json.JSONDecodeError:
                extracted_content = {"raw": result.extracted_content}

        # If no extracted content, fall back to manual extraction from cleaned_html
        if not extracted_content and hasattr(result, 'cleaned_html') and result.cleaned_html:
            extracted_content = self._manual_extraction(result.cleaned_html)

        return {
            "url": url,
            "timestamp": datetime.now().isoformat(),
            "strategy": strategy,
            "title": getattr(result, 'title', ''),
            "contacts": self._clean_and_structure_contacts(extracted_content),
            "metadata": {
                "success": result.success if hasattr(result, 'success') else True,
                "status_code": getattr(result, 'status_code', None),
                "content_length": len(result.cleaned_html) if hasattr(result, 'cleaned_html') else 0
            }
        }

    def _manual_extraction(self, html_content: str) -> Dict:
        """Manually extract contact information from HTML content using regex."""

        extracted = {
            "emails": [],
            "phones": [],
            "addresses": [],
            "social_links": [],
            "websites": []
        }

        # Extract emails
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, html_content, re.IGNORECASE)
        # Filter out image files and other false positives
        real_emails = [email for email in emails if not email.endswith(('.svg', '.png', '.jpg', '.jpeg', '.gif'))]
        extracted["emails"] = list(set(real_emails))

        # Extract phone numbers (multiple patterns)
        phone_patterns = [
            r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}',  # (************* or ************
            r'\+?1[-.\s]?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}',  # +****************
            r'\d{10}',  # 1234567890
        ]

        all_phones = []
        for pattern in phone_patterns:
            phones = re.findall(pattern, html_content)
            all_phones.extend(phones)

        # Clean and validate phone numbers
        cleaned_phones = []
        for phone in all_phones:
            # Remove non-digit characters for validation
            digits_only = re.sub(r'\D', '', phone)
            # Valid phone numbers should have 10 or 11 digits
            if 10 <= len(digits_only) <= 11:
                cleaned_phones.append(phone)

        extracted["phones"] = list(set(cleaned_phones))

        # Extract social media links
        social_patterns = {
            'facebook': r'https?://(?:www\.)?facebook\.com/[^\s<>"\']+',
            'twitter': r'https?://(?:www\.)?(?:twitter\.com|x\.com)/[^\s<>"\']+',
            'instagram': r'https?://(?:www\.)?instagram\.com/[^\s<>"\']+',
            'linkedin': r'https?://(?:www\.)?linkedin\.com/[^\s<>"\']+',
            'youtube': r'https?://(?:www\.)?youtube\.com/[^\s<>"\']+',
        }

        social_links = []
        for platform, pattern in social_patterns.items():
            links = re.findall(pattern, html_content, re.IGNORECASE)
            for link in links:
                social_links.append(link)

        extracted["social_links"] = list(set(social_links))

        # Extract addresses (basic pattern)
        address_pattern = r'\d+\s+[A-Za-z0-9\s,.-]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr|Court|Ct|Place|Pl)\s*,?\s*[A-Za-z\s]+,?\s*[A-Z]{2}\s*\d{5}(?:-\d{4})?'
        addresses = re.findall(address_pattern, html_content, re.IGNORECASE)
        extracted["addresses"] = list(set(addresses))

        return extracted
    
    def _combine_extraction_results(self, basic_result: CrawlResult, 
                                  css_result: CrawlResult, 
                                  regex_result: CrawlResult, 
                                  url: str) -> Dict:
        """Combine results from multiple extraction strategies."""
        
        # Process each result
        css_data = self._process_extraction_result(css_result, url, "css")
        regex_data = self._process_extraction_result(regex_result, url, "regex")
        
        # Merge contact information
        combined_contacts = {}
        
        # Merge emails (handle both string and dict formats)
        emails = set()
        css_emails = css_data.get("contacts", {}).get("emails", [])
        regex_emails = regex_data.get("contacts", {}).get("emails", [])

        for email_list in [css_emails, regex_emails]:
            for email in email_list:
                if isinstance(email, dict):
                    emails.add(email.get("email", ""))
                elif isinstance(email, str):
                    emails.add(email)

        combined_contacts["emails"] = [{"email": email, "confidence": 0.8} for email in emails if email]

        # Merge phones (handle both string and dict formats)
        phones = set()
        css_phones = css_data.get("contacts", {}).get("phones", [])
        regex_phones = regex_data.get("contacts", {}).get("phones", [])

        for phone_list in [css_phones, regex_phones]:
            for phone in phone_list:
                if isinstance(phone, dict):
                    phones.add(phone.get("phone", ""))
                elif isinstance(phone, str):
                    phones.add(phone)

        combined_contacts["phones"] = [{"phone": phone, "confidence": 0.8} for phone in phones if phone]

        # Merge other contact types (handle both string and dict formats)
        for contact_type in ["addresses", "social_links", "websites", "names_titles"]:
            items = set()
            css_items = css_data.get("contacts", {}).get(contact_type, [])
            regex_items = regex_data.get("contacts", {}).get(contact_type, [])

            for item_list in [css_items, regex_items]:
                for item in item_list:
                    if isinstance(item, dict):
                        # Extract the main value from dict
                        key = contact_type[:-1] if contact_type.endswith('s') else contact_type
                        items.add(item.get(key, str(item)))
                    elif isinstance(item, str):
                        items.add(item)

            # Format as structured data
            if contact_type == "social_links":
                combined_contacts["social_media"] = [{"url": item, "platform": self._identify_social_platform(item)} for item in items if item]
            else:
                key_name = contact_type[:-1] if contact_type.endswith('s') else contact_type
                combined_contacts[contact_type] = [{key_name: item, "confidence": 0.7} for item in items if item]
        
        return {
            "url": url,
            "timestamp": datetime.now().isoformat(),
            "strategy": "comprehensive",
            "title": getattr(basic_result, 'title', ''),
            "contacts": combined_contacts,
            "metadata": {
                "success": basic_result.success if hasattr(basic_result, 'success') else True,
                "status_code": getattr(basic_result, 'status_code', None),
                "strategies_used": ["css", "regex"]
            }
        }

    def _clean_and_structure_contacts(self, extracted_content: Dict) -> Dict:
        """Clean and structure extracted contact information."""

        structured_contacts = {
            "emails": [],
            "phones": [],
            "addresses": [],
            "social_media": [],
            "websites": [],
            "names_titles": [],
            "company_info": {}
        }

        # Process emails
        emails = extracted_content.get("emails", [])
        if isinstance(emails, str):
            emails = [emails]
        for email in emails:
            if email and self._is_valid_email(email):
                structured_contacts["emails"].append({
                    "email": email.strip(),
                    "confidence": 0.9
                })

        # Process phones
        phones = extracted_content.get("phones", [])
        if isinstance(phones, str):
            phones = [phones]
        for phone in phones:
            if phone:
                cleaned_phone = self._clean_phone_number(phone)
                if cleaned_phone:
                    structured_contacts["phones"].append({
                        "phone": cleaned_phone,
                        "original": phone,
                        "confidence": 0.8
                    })

        # Process addresses
        addresses = extracted_content.get("addresses", [])
        if isinstance(addresses, str):
            addresses = [addresses]
        for address in addresses:
            if address and len(address.strip()) > 10:  # Basic address validation
                structured_contacts["addresses"].append({
                    "address": address.strip(),
                    "confidence": 0.7
                })

        # Process social media links
        social_links = extracted_content.get("social_links", [])
        if isinstance(social_links, str):
            social_links = [social_links]
        for link in social_links:
            if link:
                platform = self._identify_social_platform(link)
                if platform:
                    structured_contacts["social_media"].append({
                        "platform": platform,
                        "url": link,
                        "handle": self._extract_social_handle(link, platform)
                    })

        # Process websites
        websites = extracted_content.get("websites", [])
        if isinstance(websites, str):
            websites = [websites]
        for website in websites:
            if website and self._is_valid_url(website):
                structured_contacts["websites"].append({
                    "url": website,
                    "domain": urlparse(website).netloc
                })

        return structured_contacts

    def _merge_llm_results(self, combined_data: Dict, llm_result: CrawlResult) -> Dict:
        """Merge LLM extraction results with existing data."""

        if not hasattr(llm_result, 'extracted_content') or not llm_result.extracted_content:
            return combined_data

        try:
            llm_content = json.loads(llm_result.extracted_content)

            # Merge LLM results with higher confidence scores
            for contact_type in ["emails", "phones", "addresses"]:
                if contact_type in llm_content:
                    existing_items = {item.get("email" if contact_type == "emails" else
                                               "phone" if contact_type == "phones" else "address", "").lower()
                                    for item in combined_data["contacts"].get(contact_type, [])}

                    for item in llm_content[contact_type]:
                        if isinstance(item, str):
                            item_key = item.lower()
                            if item_key not in existing_items:
                                contact_item = {
                                    contact_type[:-1]: item,  # Remove 's' from end
                                    "confidence": 0.95,  # Higher confidence for LLM
                                    "source": "llm"
                                }
                                combined_data["contacts"][contact_type].append(contact_item)

            # Update metadata
            combined_data["metadata"]["strategies_used"].append("llm")

        except json.JSONDecodeError as e:
            print(f"Failed to parse LLM results: {str(e)}")

        return combined_data

    def _is_valid_email(self, email: str) -> bool:
        """Validate email address format."""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email.strip()))

    def _clean_phone_number(self, phone: str) -> Optional[str]:
        """Clean and format phone number."""
        # Remove common phone number prefixes and clean
        cleaned = re.sub(r'[^\d+()-.\s]', '', phone)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()

        # Basic validation - should have at least 7 digits
        digits = re.findall(r'\d', cleaned)
        if len(digits) >= 7:
            return cleaned
        return None

    def _identify_social_platform(self, url: str) -> Optional[str]:
        """Identify social media platform from URL."""
        platforms = {
            'facebook.com': 'facebook',
            'twitter.com': 'twitter',
            'x.com': 'twitter',
            'linkedin.com': 'linkedin',
            'instagram.com': 'instagram',
            'youtube.com': 'youtube',
            'tiktok.com': 'tiktok',
            'github.com': 'github'
        }

        for domain, platform in platforms.items():
            if domain in url.lower():
                return platform
        return None

    def _extract_social_handle(self, url: str, platform: str) -> Optional[str]:
        """Extract social media handle from URL."""
        try:
            parsed = urlparse(url)
            path_parts = parsed.path.strip('/').split('/')
            if path_parts and path_parts[0]:
                return f"@{path_parts[0]}"
        except:
            pass
        return None

    def _is_valid_url(self, url: str) -> bool:
        """Validate URL format."""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False

    def export_to_json(self, contacts: List[Dict], filename: str) -> None:
        """Export contacts to JSON file."""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(contacts, f, indent=2, ensure_ascii=False)
        print(f"Contacts exported to {filename}")

    def export_to_csv(self, contacts: List[Dict], filename: str) -> None:
        """Export contacts to CSV file."""
        if not contacts:
            return

        # Flatten the contact data for CSV export
        flattened_data = []
        for contact_data in contacts:
            base_info = {
                'url': contact_data.get('url', ''),
                'timestamp': contact_data.get('timestamp', ''),
                'title': contact_data.get('title', ''),
                'strategy': contact_data.get('strategy', '')
            }

            contacts_info = contact_data.get('contacts', {})

            # Add emails
            emails = contacts_info.get('emails', [])
            for i, email_info in enumerate(emails):
                row = base_info.copy()
                row.update({
                    'contact_type': 'email',
                    'contact_value': email_info.get('email', ''),
                    'confidence': email_info.get('confidence', ''),
                    'index': i
                })
                flattened_data.append(row)

            # Add phones
            phones = contacts_info.get('phones', [])
            for i, phone_info in enumerate(phones):
                row = base_info.copy()
                row.update({
                    'contact_type': 'phone',
                    'contact_value': phone_info.get('phone', ''),
                    'confidence': phone_info.get('confidence', ''),
                    'index': i
                })
                flattened_data.append(row)

            # Add addresses
            addresses = contacts_info.get('addresses', [])
            for i, addr_info in enumerate(addresses):
                row = base_info.copy()
                row.update({
                    'contact_type': 'address',
                    'contact_value': addr_info.get('address', ''),
                    'confidence': addr_info.get('confidence', ''),
                    'index': i
                })
                flattened_data.append(row)

        if flattened_data:
            fieldnames = ['url', 'timestamp', 'title', 'strategy', 'contact_type',
                         'contact_value', 'confidence', 'index']

            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(flattened_data)

            print(f"Contacts exported to {filename}")


# Convenience function for quick extraction
async def extract_contacts_from_url(url: str, strategy: str = "comprehensive",
                                  use_llm: bool = False) -> Dict:
    """
    Quick function to extract contacts from a single URL.

    Args:
        url: URL to extract contacts from
        strategy: Extraction strategy to use
        use_llm: Whether to use LLM extraction

    Returns:
        Dictionary containing extracted contact information
    """
    extractor = ContactExtractor(use_llm=use_llm)
    results = await extractor.extract_contacts(url, strategy)
    return results[0] if results else {}
