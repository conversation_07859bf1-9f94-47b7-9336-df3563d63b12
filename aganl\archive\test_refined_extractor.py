"""
Test the refined contact extractor that returns the BEST single contact for each type
"""

import asyncio
import csv
from datetime import datetime
from typing import List
from refined_contact_extractor import RefinedContactExtractor


def load_coffee_shop_urls() -> List[str]:
    """Load URLs from test_data.csv"""
    urls = []
    try:
        with open("test_data.csv", 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                url = row.get('website_url', '').strip()
                if url and url.startswith(('http://', 'https://')):
                    urls.append(url)
        return urls
    except Exception as e:
        print(f"Error loading URLs: {e}")
        return []


async def test_single_refined_extraction():
    """Test refined extraction on a single coffee shop."""
    print("☕ REFINED CONTACT EXTRACTION - SINGLE TEST")
    print("Returns the BEST single contact for each type")
    print("=" * 60)
    
    urls = load_coffee_shop_urls()
    if not urls:
        print("No URLs found")
        return
    
    # Test with a coffee shop that should have good contact info
    test_url = "https://thejamescafe.com/"
    
    extractor = RefinedContactExtractor()
    results = await extractor.extract_best_contacts([test_url])
    
    if results and 'error' not in results[0]:
        result = results[0]
        print(f"\n✅ Successfully processed: {test_url}")
        
        # Export results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        extractor.export_to_json(results, f"refined_single_test_{timestamp}.json")
        extractor.export_to_csv(results, f"refined_single_test_{timestamp}.csv")
    else:
        print(f"❌ Failed to process {test_url}")


async def test_refined_batch_extraction():
    """Test refined extraction on multiple coffee shops."""
    print("☕ REFINED CONTACT EXTRACTION - BATCH TEST")
    print("Returns the BEST single contact for each type")
    print("=" * 60)
    
    urls = load_coffee_shop_urls()
    if not urls:
        print("No URLs found")
        return
    
    # Test first 5 URLs
    test_urls = urls[:5]
    print(f"Testing {len(test_urls)} coffee shops...")
    
    extractor = RefinedContactExtractor()
    results = await extractor.extract_best_contacts(test_urls)
    
    # Analyze results
    print(f"\n{'='*80}")
    print("REFINED EXTRACTION SUMMARY")
    print(f"{'='*80}")
    
    successful = 0
    with_business = 0
    with_email = 0
    with_phone = 0
    with_address = 0
    with_social = 0
    
    print(f"\n📋 DETAILED RESULTS:")
    
    for i, result in enumerate(results, 1):
        if 'error' in result:
            print(f"{i:2d}. ❌ {result['url']}: Error")
            continue
        
        successful += 1
        contacts = result.get('contacts', {})
        
        # Count what we found
        business = contacts.get('business_name')
        email = contacts.get('email')
        phone = contacts.get('phone')
        address = contacts.get('address')
        social = contacts.get('social_media')
        
        if business: with_business += 1
        if email: with_email += 1
        if phone: with_phone += 1
        if address: with_address += 1
        if social: with_social += 1
        
        # Display result
        business_name = business['business_name'][:30] + "..." if business and len(business['business_name']) > 30 else (business['business_name'] if business else "Not found")
        email_addr = email['email'] if email else "Not found"
        phone_num = phone['phone'] if phone else "Not found"
        address_text = address['address'][:40] + "..." if address and len(address['address']) > 40 else (address['address'] if address else "Not found")
        social_info = f"{social['platform']} ({social.get('handle', 'N/A')})" if social else "Not found"
        
        print(f"\n{i:2d}. 🏪 {business_name}")
        print(f"    🔗 {result['url']}")
        print(f"    📧 {email_addr}")
        print(f"    📞 {phone_num}")
        print(f"    📍 {address_text}")
        print(f"    🌐 {social_info}")
    
    # Summary statistics
    print(f"\n📊 SUMMARY STATISTICS:")
    print(f"   • Total processed: {len(test_urls)}")
    print(f"   • Successful: {successful}")
    print(f"   • With business name: {with_business}")
    print(f"   • With email: {with_email}")
    print(f"   • With phone: {with_phone}")
    print(f"   • With address: {with_address}")
    print(f"   • With social media: {with_social}")
    
    if successful > 0:
        print(f"\n📈 SUCCESS RATES:")
        print(f"   • Overall success: {(successful/len(test_urls)*100):.1f}%")
        print(f"   • Business name found: {(with_business/successful*100):.1f}%")
        print(f"   • Email found: {(with_email/successful*100):.1f}%")
        print(f"   • Phone found: {(with_phone/successful*100):.1f}%")
        print(f"   • Address found: {(with_address/successful*100):.1f}%")
        print(f"   • Social media found: {(with_social/successful*100):.1f}%")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    extractor.export_to_json(results, f"refined_batch_test_{timestamp}.json")
    extractor.export_to_csv(results, f"refined_batch_test_{timestamp}.csv")
    
    return results


async def test_comprehensive_refined_extraction():
    """Test refined extraction on all coffee shops."""
    print("☕ COMPREHENSIVE REFINED CONTACT EXTRACTION")
    print("Returns the BEST single contact for each type")
    print("=" * 60)
    
    urls = load_coffee_shop_urls()
    if not urls:
        print("No URLs found")
        return
    
    print(f"Processing all {len(urls)} coffee shops...")
    
    start_time = datetime.now()
    
    extractor = RefinedContactExtractor()
    results = await extractor.extract_best_contacts(urls)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # Comprehensive analysis
    print(f"\n{'='*80}")
    print("COMPREHENSIVE REFINED EXTRACTION ANALYSIS")
    print(f"{'='*80}")
    
    successful = 0
    complete_profiles = 0  # Has all 5 contact types
    partial_profiles = 0   # Has 3+ contact types
    
    contact_counts = {
        'business': 0, 'email': 0, 'phone': 0, 'address': 0, 'social': 0
    }
    
    high_confidence_contacts = []
    
    for result in results:
        if 'error' in result:
            continue
        
        successful += 1
        contacts = result.get('contacts', {})
        
        # Count contact types found
        found_count = 0
        contact_details = {
            'url': result['url'],
            'business': None, 'email': None, 'phone': None, 'address': None, 'social': None
        }
        
        for contact_type in ['business_name', 'email', 'phone', 'address', 'social_media']:
            contact = contacts.get(contact_type)
            if contact:
                found_count += 1
                short_type = contact_type.replace('_name', '').replace('_media', '')
                contact_counts[short_type] += 1
                
                # Store contact details
                if contact_type == 'business_name':
                    contact_details['business'] = contact['business_name']
                elif contact_type == 'email':
                    contact_details['email'] = contact['email']
                elif contact_type == 'phone':
                    contact_details['phone'] = contact['phone']
                elif contact_type == 'address':
                    contact_details['address'] = contact['address']
                elif contact_type == 'social_media':
                    contact_details['social'] = f"{contact['platform']} ({contact.get('handle', 'N/A')})"
                
                # Check for high confidence contacts
                if contact.get('confidence', 0) >= 0.8:
                    high_confidence_contacts.append({
                        'url': result['url'],
                        'type': contact_type,
                        'value': str(contact),
                        'confidence': contact.get('confidence', 0)
                    })
        
        if found_count >= 5:
            complete_profiles += 1
        elif found_count >= 3:
            partial_profiles += 1
    
    # Display comprehensive results
    print(f"\n📊 OVERALL STATISTICS:")
    print(f"   • Total websites: {len(urls)}")
    print(f"   • Successfully processed: {successful}")
    print(f"   • Complete profiles (5/5 contacts): {complete_profiles}")
    print(f"   • Partial profiles (3+ contacts): {partial_profiles}")
    print(f"   • Processing time: {duration:.2f} seconds ({duration/len(urls):.2f}s per site)")
    
    print(f"\n📞 CONTACT TYPE STATISTICS:")
    for contact_type, count in contact_counts.items():
        percentage = (count / successful * 100) if successful > 0 else 0
        print(f"   • {contact_type.title()}: {count}/{successful} ({percentage:.1f}%)")
    
    print(f"\n🏆 HIGH CONFIDENCE CONTACTS (≥80%):")
    print(f"   Found {len(high_confidence_contacts)} high-confidence contacts")
    
    # Export comprehensive results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    extractor.export_to_json(results, f"comprehensive_refined_{timestamp}.json")
    extractor.export_to_csv(results, f"comprehensive_refined_{timestamp}.csv")
    
    print(f"\n💾 RESULTS EXPORTED:")
    print(f"   • JSON: comprehensive_refined_{timestamp}.json")
    print(f"   • CSV: comprehensive_refined_{timestamp}.csv")
    
    return results


async def main():
    """Main test function."""
    print("🎯 REFINED CONTACT EXTRACTOR TESTS")
    print("Extracts the BEST single contact for each type")
    print("=" * 60)
    
    print("\nChoose test mode:")
    print("1. Single coffee shop test (detailed)")
    print("2. Batch test (5 coffee shops)")
    print("3. Comprehensive test (all coffee shops)")
    
    try:
        choice = input("\nEnter choice (1-3) or press Enter for option 1: ").strip()
        if not choice:
            choice = "1"
    except KeyboardInterrupt:
        print("\n\nTest cancelled by user.")
        return
    
    try:
        if choice == "1":
            await test_single_refined_extraction()
        elif choice == "2":
            await test_refined_batch_extraction()
        elif choice == "3":
            await test_comprehensive_refined_extraction()
        else:
            print("Invalid choice. Running single test.")
            await test_single_refined_extraction()
    
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
    
    print(f"\n🎉 Refined extraction test completed!")
    print("The system now returns exactly 1 of each contact type - the BEST one found!")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n☕ Test interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
