"""
Quick test of the Chunk UI
"""
import tkinter as tk
from chunk_ui import ChunkScraperUI
import sys

def test_ui():
    try:
        print("🧪 Testing Chunk Scraper UI...")
        
        # Create root window
        root = tk.Tk()
        
        # Initialize the UI
        app = ChunkScraperUI(root)
        
        print("✅ UI initialized successfully!")
        print("✅ All components loaded")
        print("✅ Chunk files detected")
        
        # Close immediately for testing
        root.after(1000, root.destroy)  # Close after 1 second
        
        # Start the UI briefly
        root.mainloop()
        
        print("✅ UI test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ UI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ui()
    if success:
        print("\n🎉 UI is ready to use!")
        print("Run: python chunk_ui.py")
    else:
        print("\n❌ UI has issues - check the errors above")
