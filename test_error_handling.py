"""
Test script to verify improved error handling works correctly.
"""

import asyncio
from aganl.perfect_contact_extractor import PerfectContactExtractor

async def test_error_handling():
    """Test the improved error handling with problematic URLs."""
    print("🧪 TESTING IMPROVED ERROR HANDLING")
    print("=" * 50)
    
    # Test URLs that are likely to cause different types of errors
    test_urls = [
        "https://www.haganaccounting.com/contact",  # DNS resolution error
        "http://www.alpern.com/about",              # Connection aborted error
        "https://nonexistentdomain12345.com",       # DNS not resolved
        "https://httpstat.us/500",                  # Server error (should work)
        "https://httpstat.us/timeout",              # Timeout error
    ]
    
    print(f"Testing {len(test_urls)} problematic URLs...")
    
    # Create extractor with conservative settings
    extractor = PerfectContactExtractor(batch_size=5, max_concurrent=2)
    
    # Process the URLs
    results = await extractor.extract_perfect(test_urls)
    
    print(f"\n📊 RESULTS SUMMARY:")
    print(f"Total URLs tested: {len(test_urls)}")
    
    successful = 0
    failed = 0
    error_types = {}
    
    for result in results:
        if 'error' in result:
            failed += 1
            error = result['error']
            print(f"❌ {result['url']}: {error}")
            
            # Categorize error types
            if 'DNS resolution failed' in error:
                error_types['DNS Issues'] = error_types.get('DNS Issues', 0) + 1
            elif 'Connection failed' in error or 'Connection aborted' in error:
                error_types['Connection Issues'] = error_types.get('Connection Issues', 0) + 1
            elif 'timeout' in error.lower():
                error_types['Timeouts'] = error_types.get('Timeouts', 0) + 1
            else:
                error_types['Other'] = error_types.get('Other', 0) + 1
        else:
            successful += 1
            print(f"✅ {result['url']}: Success")
    
    print(f"\n📈 STATISTICS:")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    
    if error_types:
        print(f"\n🔍 ERROR BREAKDOWN:")
        for error_type, count in error_types.items():
            print(f"   • {error_type}: {count}")
    
    print(f"\n✅ Error handling test completed!")
    print("The script should now handle network errors more gracefully.")

if __name__ == "__main__":
    asyncio.run(test_error_handling())
