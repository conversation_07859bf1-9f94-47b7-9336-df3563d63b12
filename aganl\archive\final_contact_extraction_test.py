"""
Final comprehensive test of the improved contact extractor
Tests all coffee shops and provides detailed analysis
"""

import asyncio
import csv
import json
from datetime import datetime
from typing import List, Dict
from improved_contact_extractor import ImprovedContactExtractor


def load_all_urls() -> List[str]:
    """Load all URLs from test_data.csv"""
    urls = []
    try:
        with open("test_data.csv", 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                url = row.get('website_url', '').strip()
                if url and url.startswith(('http://', 'https://')):
                    urls.append(url)
        return urls
    except Exception as e:
        print(f"Error loading URLs: {e}")
        return []


def analyze_results(results: List[Dict]) -> Dict:
    """Analyze extraction results and provide detailed statistics"""
    analysis = {
        'total_processed': len(results),
        'successful': 0,
        'with_contacts': 0,
        'with_emails': 0,
        'with_phones': 0,
        'with_addresses': 0,
        'with_social': 0,
        'total_emails': 0,
        'total_phones': 0,
        'total_addresses': 0,
        'total_social': 0,
        'businesses_found': [],
        'contact_details': []
    }
    
    for result in results:
        if 'error' in result:
            continue
            
        analysis['successful'] += 1
        contacts = result.get('contacts', {})
        
        # Count contact types
        emails = contacts.get('emails', [])
        phones = contacts.get('phones', [])
        addresses = contacts.get('addresses', [])
        social_media = contacts.get('social_media', [])
        
        if emails or phones or addresses or social_media:
            analysis['with_contacts'] += 1
        
        if emails:
            analysis['with_emails'] += 1
            analysis['total_emails'] += len(emails)
        
        if phones:
            analysis['with_phones'] += 1
            analysis['total_phones'] += len(phones)
        
        if addresses:
            analysis['with_addresses'] += 1
            analysis['total_addresses'] += len(addresses)
        
        if social_media:
            analysis['with_social'] += 1
            analysis['total_social'] += len(social_media)
        
        # Store business details
        business_name = contacts.get('business_name', 'Unknown')
        url = result.get('url', '')
        
        analysis['businesses_found'].append({
            'name': business_name,
            'url': url,
            'email_count': len(emails),
            'phone_count': len(phones),
            'address_count': len(addresses),
            'social_count': len(social_media),
            'total_contacts': len(emails) + len(phones) + len(addresses) + len(social_media)
        })
        
        # Store detailed contact info for businesses with contacts
        if emails or phones or addresses or social_media:
            contact_detail = {
                'business_name': business_name,
                'url': url,
                'emails': [e['email'] for e in emails],
                'phones': [p['phone'] for p in phones],
                'addresses': [a['address'] for a in addresses],
                'social_media': [f"{s['platform']}: {s.get('handle', s['url'])}" for s in social_media]
            }
            analysis['contact_details'].append(contact_detail)
    
    return analysis


def print_detailed_analysis(analysis: Dict):
    """Print detailed analysis of extraction results"""
    print(f"\n{'='*80}")
    print("COMPREHENSIVE CONTACT EXTRACTION ANALYSIS")
    print(f"{'='*80}")
    
    # Overall statistics
    print(f"\n📊 OVERALL STATISTICS:")
    print(f"   • Total websites processed: {analysis['total_processed']}")
    print(f"   • Successful extractions: {analysis['successful']}")
    print(f"   • Websites with contact info: {analysis['with_contacts']}")
    
    if analysis['successful'] > 0:
        success_rate = (analysis['successful'] / analysis['total_processed']) * 100
        contact_rate = (analysis['with_contacts'] / analysis['successful']) * 100
        print(f"   • Success rate: {success_rate:.1f}%")
        print(f"   • Contact discovery rate: {contact_rate:.1f}%")
    
    # Contact type breakdown
    print(f"\n📞 CONTACT TYPE BREAKDOWN:")
    print(f"   • Websites with emails: {analysis['with_emails']} (total: {analysis['total_emails']})")
    print(f"   • Websites with phones: {analysis['with_phones']} (total: {analysis['total_phones']})")
    print(f"   • Websites with addresses: {analysis['with_addresses']} (total: {analysis['total_addresses']})")
    print(f"   • Websites with social media: {analysis['with_social']} (total: {analysis['total_social']})")
    
    total_contact_items = (analysis['total_emails'] + analysis['total_phones'] + 
                          analysis['total_addresses'] + analysis['total_social'])
    print(f"   • Total contact items found: {total_contact_items}")
    
    # Top performers
    print(f"\n🏆 TOP PERFORMING WEBSITES:")
    sorted_businesses = sorted(analysis['businesses_found'], 
                              key=lambda x: x['total_contacts'], reverse=True)
    
    for i, business in enumerate(sorted_businesses[:10], 1):
        if business['total_contacts'] > 0:
            name = business['name'][:40] + "..." if len(business['name']) > 40 else business['name']
            print(f"   {i:2d}. {name}")
            print(f"       📧 {business['email_count']} emails, 📞 {business['phone_count']} phones, "
                  f"📍 {business['address_count']} addresses, 🌐 {business['social_count']} social")
            print(f"       🔗 {business['url']}")
    
    # Detailed contact information
    print(f"\n📋 DETAILED CONTACT INFORMATION:")
    for detail in analysis['contact_details']:
        print(f"\n🏪 {detail['business_name']}")
        print(f"   🔗 {detail['url']}")
        
        if detail['emails']:
            print(f"   📧 Emails: {', '.join(detail['emails'])}")
        
        if detail['phones']:
            print(f"   📞 Phones: {', '.join(detail['phones'])}")
        
        if detail['addresses']:
            for addr in detail['addresses']:
                addr_short = addr[:60] + "..." if len(addr) > 60 else addr
                print(f"   📍 Address: {addr_short}")
        
        if detail['social_media']:
            print(f"   🌐 Social: {', '.join(detail['social_media'])}")


async def run_comprehensive_test():
    """Run comprehensive test on all coffee shops"""
    print("☕ COMPREHENSIVE COFFEE SHOP CONTACT EXTRACTION")
    print("Using crawl4ai with improved validation and filtering")
    print("=" * 80)
    
    # Load URLs
    urls = load_all_urls()
    if not urls:
        print("❌ No URLs found in test_data.csv")
        return
    
    print(f"📋 Loaded {len(urls)} coffee shop URLs")
    print("🚀 Starting comprehensive extraction...")
    
    start_time = datetime.now()
    
    # Run extraction
    extractor = ImprovedContactExtractor()
    results = await extractor.extract_contacts(urls)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # Analyze results
    analysis = analyze_results(results)
    
    # Print detailed analysis
    print_detailed_analysis(analysis)
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    json_file = f"comprehensive_coffee_extraction_{timestamp}.json"
    csv_file = f"comprehensive_coffee_extraction_{timestamp}.csv"
    analysis_file = f"extraction_analysis_{timestamp}.json"
    
    extractor.export_to_json(results, json_file)
    extractor.export_to_csv(results, csv_file)
    
    # Export analysis
    with open(analysis_file, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 FILES EXPORTED:")
    print(f"   • Detailed results: {json_file}")
    print(f"   • CSV summary: {csv_file}")
    print(f"   • Analysis data: {analysis_file}")
    
    print(f"\n⏱️  Total execution time: {duration:.2f} seconds")
    print(f"⚡ Average time per website: {duration/len(urls):.2f} seconds")
    
    print(f"\n🎉 Comprehensive extraction completed!")
    print("The improved contact extractor successfully:")
    print("   ✅ Used crawl4ai's built-in RegexExtractionStrategy")
    print("   ✅ Used JsonCssExtractionStrategy for structured data")
    print("   ✅ Implemented robust validation to avoid false positives")
    print("   ✅ Filtered out coordinates and invalid phone numbers")
    print("   ✅ Extracted business names, emails, phones, addresses, and social media")
    print("   ✅ Provided detailed analysis and export capabilities")


async def main():
    """Main function"""
    try:
        await run_comprehensive_test()
    except KeyboardInterrupt:
        print("\n\n☕ Extraction interrupted by user. Partial results may be available.")
    except Exception as e:
        print(f"\n❌ Error during extraction: {str(e)}")
        print("Please check your internet connection and try again.")


if __name__ == "__main__":
    asyncio.run(main())
