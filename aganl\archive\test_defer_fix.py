"""
Test if the improved production extractor finds De<PERSON> Coffee's email
"""

import asyncio
from production_fast_extractor import ProductionFastExtractor


async def test_defer_coffee():
    """Test specifically for Defer Coffee email on contact page."""
    print("🔍 TESTING DEFER COFFEE EMAIL EXTRACTION")
    print("=" * 50)
    print("URL: https://www.defer.coffee/strip-district")
    print("Expected: Should find email on contact-us page")
    print("=" * 50)
    
    extractor = ProductionFastExtractor(batch_size=5, max_concurrent=2)
    results = await extractor.extract_production(['https://www.defer.coffee/strip-district'])
    
    result = results[0]
    
    if 'error' not in result:
        email = result.get('email')
        social = result.get('social_media')
        
        print("\n📊 DEFER COFFEE RESULTS:")
        
        if email:
            print(f"✅ Email: {email['email']} (confidence: {email['confidence']:.1%})")
        else:
            print("❌ Email: Not found")
        
        if social:
            print(f"✅ Social: {social['platform']} {social.get('handle', '')} (confidence: {social['confidence']:.1%})")
        else:
            print("❌ Social: Not found")
        
        print(f"\n🔗 URL processed: {result['url']}")
        print(f"⏰ Timestamp: {result['timestamp']}")
        
        if email:
            print(f"\n🎉 SUCCESS: Found Defer Coffee email!")
        else:
            print(f"\n⚠️  Still missing email - may need to check contact page manually")
    
    else:
        print(f"❌ ERROR: {result['error']}")
    
    return results


async def test_multiple_coffee_shops():
    """Test a few coffee shops including Defer to compare."""
    print("\n🔍 TESTING MULTIPLE COFFEE SHOPS")
    print("=" * 50)
    
    test_urls = [
        "https://www.defer.coffee/strip-district",  # Should find email on contact page
        "https://thejamescafe.com/",               # Should find email easily
        "https://convivecoffee.com/",              # Should find email
        "http://www.yinzcoffee.com/"               # Should find email
    ]
    
    print(f"Testing {len(test_urls)} coffee shops...")
    
    extractor = ProductionFastExtractor(batch_size=10, max_concurrent=3)
    results = await extractor.extract_production(test_urls)
    
    print(f"\n📊 COMPARISON RESULTS:")
    
    for i, result in enumerate(results, 1):
        if 'error' in result:
            print(f"{i}. ❌ {result['url']}: {result['error']}")
            continue
        
        url = result['url']
        email = result.get('email')
        social = result.get('social_media')
        
        domain = url.split('/')[2].replace('www.', '')
        
        print(f"\n{i}. 🏪 {domain}")
        
        if email:
            print(f"   📧 {email['email']} (confidence: {email['confidence']:.1%})")
        else:
            print(f"   📧 No email found")
        
        if social:
            print(f"   🌐 {social['platform']} {social.get('handle', '')} (confidence: {social['confidence']:.1%})")
        else:
            print(f"   🌐 No social found")
    
    # Summary
    successful = len([r for r in results if 'error' not in r])
    emails_found = len([r for r in results if 'error' not in r and r.get('email')])
    socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
    
    print(f"\n📈 SUMMARY:")
    print(f"   • URLs tested: {len(test_urls)}")
    print(f"   • Successful: {successful}")
    print(f"   • Emails found: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)" if successful > 0 else "")
    print(f"   • Social found: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)" if successful > 0 else "")
    
    return results


async def main():
    """Main test function."""
    print("🔧 TESTING IMPROVED PRODUCTION EXTRACTOR")
    print("Focus: Finding emails on contact pages (like Defer Coffee)")
    print("=" * 60)
    
    # Test Defer Coffee specifically
    await test_defer_coffee()
    
    # Test comparison with other coffee shops
    await test_multiple_coffee_shops()
    
    print(f"\n🎉 Testing completed!")
    print("The improved extractor now checks:")
    print("✅ Main page")
    print("✅ /contact page")
    print("✅ /contact-us page") 
    print("✅ /about page")
    print("This should catch emails that are only on contact pages!")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n🔧 Test interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
