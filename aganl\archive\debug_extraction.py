"""
Debug script to understand what's being extracted and improve our strategies.
"""

import asyncio
import json
import re
from crawl4ai import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JsonCssExtractionStrategy, RegexExtractionStrategy


async def debug_single_website(url: str):
    """Debug extraction from a single website to see what we're getting."""
    print(f"Debugging extraction from: {url}")
    print("=" * 60)
    
    async with AsyncWebCrawler(verbose=True) as crawler:
        # First, get basic content
        print("1. Getting basic content...")
        basic_result = await crawler.arun(url=url, bypass_cache=True)
        
        print(f"Success: {basic_result.success}")
        print(f"Status Code: {getattr(basic_result, 'status_code', 'N/A')}")
        print(f"Title: '{getattr(basic_result, 'title', 'N/A')}'")
        
        if hasattr(basic_result, 'cleaned_html'):
            content_length = len(basic_result.cleaned_html)
            print(f"Content Length: {content_length}")
            
            # Show first 500 characters of content
            preview = basic_result.cleaned_html[:500]
            print(f"Content Preview:\n{preview}...")
            
            # Look for common contact patterns manually
            print("\n2. Manual pattern search in content...")
            
            # Email patterns
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, basic_result.cleaned_html, re.IGNORECASE)
            print(f"Emails found: {emails}")
            
            # Phone patterns (simple)
            phone_pattern = r'(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})'
            phones = re.findall(phone_pattern, basic_result.cleaned_html)
            print(f"Phones found: {phones}")
            
            # Look for contact-related text
            contact_keywords = ['contact', 'phone', 'email', 'address', 'location']
            for keyword in contact_keywords:
                count = basic_result.cleaned_html.lower().count(keyword)
                print(f"'{keyword}' appears {count} times")
        
        # Test CSS extraction
        print("\n3. Testing CSS extraction...")
        css_strategy = JsonCssExtractionStrategy({
            "all_links": {
                "selector": "a",
                "attribute": "href"
            },
            "all_text": {
                "selector": "body",
                "attribute": "text"
            },
            "contact_links": {
                "selector": "a[href*='contact'], a[href*='mailto'], a[href*='tel']",
                "attribute": "href"
            }
        })
        
        css_result = await crawler.arun(
            url=url,
            extraction_strategy=css_strategy,
            bypass_cache=True
        )
        
        if hasattr(css_result, 'extracted_content'):
            try:
                css_data = json.loads(css_result.extracted_content)
                print(f"CSS extraction successful:")
                for key, value in css_data.items():
                    if isinstance(value, list):
                        print(f"  {key}: {len(value)} items")
                        if key == "contact_links" and value:
                            print(f"    Contact links: {value[:5]}")  # Show first 5
                    else:
                        print(f"  {key}: {type(value)}")
            except json.JSONDecodeError as e:
                print(f"CSS extraction failed to parse: {e}")
        
        # Test regex extraction
        print("\n4. Testing regex extraction...")
        regex_strategy = RegexExtractionStrategy(
            custom={
                "emails": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
                "phones": r'(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})',
                "contact_text": r'contact.*?(?=\n|\.|$)',
            }
        )
        
        regex_result = await crawler.arun(
            url=url,
            extraction_strategy=regex_strategy,
            bypass_cache=True
        )
        
        if hasattr(regex_result, 'extracted_content'):
            try:
                regex_data = json.loads(regex_result.extracted_content)
                print(f"Regex extraction successful:")
                for key, value in regex_data.items():
                    if isinstance(value, list):
                        print(f"  {key}: {len(value)} items - {value[:3]}")  # Show first 3
                    else:
                        print(f"  {key}: {value}")
            except json.JSONDecodeError as e:
                print(f"Regex extraction failed to parse: {e}")


async def test_improved_extraction(url: str):
    """Test with improved extraction strategies."""
    print(f"\n{'='*60}")
    print(f"IMPROVED EXTRACTION TEST: {url}")
    print(f"{'='*60}")
    
    async with AsyncWebCrawler(verbose=False) as crawler:
        # Improved CSS strategy with more selectors
        improved_css = JsonCssExtractionStrategy({
            "emails": {
                "selector": "a[href^='mailto:'], [data-email], .email, .contact-email, [href*='@']",
                "attribute": "href"
            },
            "phones": {
                "selector": "a[href^='tel:'], [data-phone], .phone, .contact-phone, .telephone",
                "attribute": "href"
            },
            "contact_info": {
                "selector": ".contact, .contact-info, .contact-section, #contact, footer",
                "attribute": "text"
            },
            "addresses": {
                "selector": ".address, .location, .contact-address, [itemtype*='PostalAddress'], .street-address",
                "attribute": "text"
            },
            "social_links": {
                "selector": "a[href*='facebook'], a[href*='twitter'], a[href*='instagram'], a[href*='linkedin']",
                "attribute": "href"
            }
        })
        
        result = await crawler.arun(
            url=url,
            extraction_strategy=improved_css,
            bypass_cache=True
        )
        
        if hasattr(result, 'extracted_content'):
            try:
                data = json.loads(result.extracted_content)
                print("Improved CSS extraction results:")
                
                for key, value in data.items():
                    if isinstance(value, list) and value:
                        print(f"\n{key.upper()}:")
                        for item in value[:5]:  # Show first 5 items
                            if key == "contact_info":
                                # Clean and truncate contact info text
                                clean_text = ' '.join(item.split())[:100]
                                print(f"  • {clean_text}...")
                            else:
                                print(f"  • {item}")
                    elif value:
                        print(f"{key}: {value}")
                
                return data
                
            except json.JSONDecodeError as e:
                print(f"Failed to parse extraction results: {e}")
        
        return {}


async def main():
    """Main debug function."""
    print("Contact Extraction Debug Tool")
    print("=" * 60)
    
    # Test with the first URL from your list
    test_urls = [
        "https://www.novariacoffee.com/",
        "https://www.bittyandbeauscoffee.com/location/pittsburgh/",
        "https://thejamescafe.com/"
    ]
    
    for url in test_urls[:2]:  # Test first 2 URLs
        try:
            await debug_single_website(url)
            await test_improved_extraction(url)
            print("\n" + "="*60 + "\n")
        except Exception as e:
            print(f"Error debugging {url}: {str(e)}")
    
    print("Debug complete!")


if __name__ == "__main__":
    asyncio.run(main())
