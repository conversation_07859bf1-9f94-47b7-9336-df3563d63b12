# Table of contents

* [Welcome to <PERSON>](README.md)

## Getting Started

* [Verify your 1st email](getting-started/quickstart.md)
* [Understanding "is\_reachable"](getting-started/is-reachable.md)

## Self-Hosting

* [SaaS vs Self-Host](self-hosting/saas-vs-self-host.md)
* [Install Reacher in 20min](self-hosting/install.md)
* [Scaling for Production](self-hosting/scaling-for-production/README.md)
  * [Manage scaling yourself](self-hosting/scaling-for-production/option-1-manage-scaling-yourself.md)
  * [Option 1: RabbitMQ-based Queue Architecture](self-hosting/scaling-for-production/option-2-rabbitmq-based-queue-architecture.md)
* [Licensing](self-hosting/licensing/README.md)
  * [Commercial License Trial](self-hosting/licensing/commercial-license-trial.md)
* [Proxies](self-hosting/proxies/README.md)
  * [Multiple Proxies](self-hosting/proxies/multiple-proxies.md)
* [Reacher Configuration](self-hosting/reacher-configuration-v0.10.md)
* [Debugging Reacher](self-hosting/debugging-reacher.md)

## Advanced

* [OpenAPI](advanced/openapi/README.md)
  * [/v0/check\_email](advanced/openapi/v0-check_email.md)
  * [/v1/check\_email](advanced/openapi/v1-check_email.md)
  * [/v1/bulk](advanced/openapi/v1-bulk.md)
* [Run your own Proxy](advanced/run-your-own-proxy.md)
* [Migrations](advanced/migrations/README.md)
  * [Reacher Configuration (v0.10)](advanced/migrations/reacher-configuration-v0.10.md)
  * [Migrating from 0.7 to 0.10](advanced/migrations/migrating-from-0.7-to-0.10-beta.md)
  * [Bulk Verification (v0.7)](advanced/migrations/bulk.md)
  * [Docker Environment Variables (v0.7)](advanced/migrations/docker-environment-variables.md)
