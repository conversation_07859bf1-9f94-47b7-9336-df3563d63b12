[package]
name = "reacher_backend"
version = "0.11.6"
edition = "2018"
license = "AGPL-3.0"
publish = false

[dependencies]
anyhow = "1.0"
async-smtp = { version = "0.9.1", features = ["runtime-tokio"] }
check-if-email-exists = { path = "../core", features = ["sentry"] }
config = "0.14"
csv = "1.3.0"
dotenv = "0.15.0"
futures = { version = "0.3.30" }
http = "1.2.0"
lapin = { version = "2.3.1" }
tokio-executor-trait = { version = "2.1.1" }
tokio-reactor-trait = { version = "1.1.0" }
openssl = { version = "0.10.69", features = ["vendored"] }
reqwest = { version = "0.12.15", default-features = false, features = [
    "json",
    "rustls-tls",
] }
sentry = { version = "0.36", default-features = false, features = [
    "reqwest",
    "rustls",
] }
sentry-anyhow = "0.32"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
sqlx = { version = "0.7", default-features = false, features = [
    "runtime-tokio-rustls",
    "postgres",
    "uuid",
    "chrono",
    "json",
    "migrate",
] }
sqlxmq = "0.5"
thiserror = "2.0"
tokio = { version = "1.40", features = ["macros"] }
tracing = "0.1.40"
tracing-subscriber = "0.3.18"
uuid = "1.10"
warp = "0.3"

[dev-dependencies]
serial_test = "3.2"
toml = "0.8"
