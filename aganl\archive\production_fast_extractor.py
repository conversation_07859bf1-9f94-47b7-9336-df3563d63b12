"""
Production-Ready Fast Email & Social Extractor
Stable, fast, and reliable for processing thousands of URLs.
"""

import asyncio
import json
import re
import csv
from datetime import datetime
from typing import Dict, List, Optional, Union
from urllib.parse import urljoin

from crawl4ai import Async<PERSON>eb<PERSON>rawler, CrawlerRunConfig
from crawl4ai import JsonCssExtractionStrategy


class ProductionFastExtractor:
    """
    Production-ready fast extractor for emails and social media.
    Optimized for stability and speed with large batches.
    """
    
    def __init__(self, batch_size: int = 50, max_concurrent: int = 6):
        """
        Initialize with batch processing for stability.
        
        Args:
            batch_size: Number of URLs to process per batch
            max_concurrent: Maximum concurrent requests per batch
        """
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
    
    async def extract_production(self, urls: Union[str, List[str]]) -> List[Dict]:
        """
        Production extraction with batch processing for stability.
        
        Args:
            urls: URLs to process
            
        Returns:
            List of results with emails and social media
        """
        if isinstance(urls, str):
            urls = [urls]
        
        print(f"🏭 PRODUCTION FAST EXTRACTION")
        print(f"📊 Processing {len(urls)} URLs in batches of {self.batch_size}")
        print(f"⚡ Max concurrent per batch: {self.max_concurrent}")
        
        all_results = []
        total_start = datetime.now()
        
        # Process URLs in batches for stability
        for i in range(0, len(urls), self.batch_size):
            batch = urls[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            total_batches = (len(urls) + self.batch_size - 1) // self.batch_size
            
            print(f"\n📦 Batch {batch_num}/{total_batches}: {len(batch)} URLs")
            
            batch_start = datetime.now()
            batch_results = await self._process_batch(batch)
            batch_duration = (datetime.now() - batch_start).total_seconds()
            
            all_results.extend(batch_results)
            
            print(f"   ✅ Completed in {batch_duration:.2f}s ({len(batch)/batch_duration:.2f} URLs/sec)")
            
            # Small delay between batches to be respectful
            if i + self.batch_size < len(urls):
                await asyncio.sleep(1)
        
        total_duration = (datetime.now() - total_start).total_seconds()
        
        print(f"\n🎉 PRODUCTION EXTRACTION COMPLETED!")
        print(f"   • Total URLs: {len(urls)}")
        print(f"   • Total time: {total_duration:.2f}s ({total_duration/60:.1f} min)")
        print(f"   • Overall rate: {len(urls)/total_duration:.2f} URLs/second")
        
        return all_results
    
    async def _process_batch(self, urls: List[str]) -> List[Dict]:
        """Process a batch of URLs with controlled concurrency."""
        
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_single(url: str) -> Dict:
            async with semaphore:
                return await self._extract_single_url(url)
        
        # Process batch concurrently
        tasks = [process_single(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append({
                    "url": urls[i],
                    "error": str(result),
                    "timestamp": datetime.now().isoformat()
                })
            else:
                final_results.append(result)
        
        return final_results
    
    async def _extract_single_url(self, url: str) -> Dict:
        """Extract from a single URL with optimized strategy."""

        try:
            # Check main page + contact page for better coverage
            pages_to_check = [url]

            # Add contact page variants
            contact_pages = ['/contact', '/contact-us', '/about']
            for contact_path in contact_pages:
                contact_url = urljoin(url, contact_path)
                if contact_url != url:
                    pages_to_check.append(contact_url)

            best_email = None
            best_social = None

            async with AsyncWebCrawler(verbose=False) as crawler:
                
                # Check each page for contact info
                for page_url in pages_to_check:
                    try:
                        # CSS extraction strategy
                        css_schema = {
                            "name": "Fast Contact",
                            "baseSelector": "body",
                            "fields": [
                                {
                                    "name": "emails",
                                    "selector": "a[href^='mailto:']",
                                    "type": "list",
                                    "fields": [{"name": "email", "type": "attribute", "attribute": "href"}]
                                },
                                {
                                    "name": "social",
                                    "selector": "a[href*='instagram.com'], a[href*='facebook.com'], a[href*='twitter.com']",
                                    "type": "list",
                                    "fields": [{"name": "url", "type": "attribute", "attribute": "href"}]
                                }
                            ]
                        }

                        strategy = JsonCssExtractionStrategy(css_schema, verbose=False)
                        config = CrawlerRunConfig(extraction_strategy=strategy)

                        # Extract with timeout
                        result = await asyncio.wait_for(
                            crawler.arun(url=page_url, config=config),
                            timeout=15.0  # Shorter timeout per page
                        )

                        # Process CSS results
                        if result.success and result.extracted_content:
                            try:
                                data = json.loads(result.extracted_content)
                                if data and len(data) > 0:
                                    css_data = data[0]

                                    # Extract emails
                                    if not best_email:
                                        emails = set()
                                        for email_item in css_data.get('emails', []):
                                            if isinstance(email_item, dict):
                                                email = email_item.get('email', '').replace('mailto:', '').strip().lower()
                                                if self._is_valid_email(email):
                                                    emails.add(email)

                                        if emails:
                                            best_email = self._select_best_email(list(emails))

                                    # Extract social links
                                    if not best_social:
                                        social_links = set()
                                        for social_item in css_data.get('social', []):
                                            if isinstance(social_item, dict):
                                                social_url = social_item.get('url', '').strip()
                                                if social_url and self._is_valid_social_url(social_url):
                                                    social_links.add(social_url)

                                        if social_links:
                                            best_social = self._select_best_social(list(social_links))

                            except json.JSONDecodeError:
                                pass

                        # Fallback: Quick regex extraction if CSS didn't work
                        if (not best_email or not best_social) and hasattr(result, 'cleaned_html'):
                            content = result.cleaned_html

                            # Quick email regex
                            if not best_email:
                                email_matches = re.findall(r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b', content)
                                valid_emails = [e.lower() for e in email_matches[:10] if self._is_valid_email(e)]
                                if valid_emails:
                                    best_email = self._select_best_email(valid_emails)

                            # Quick social regex
                            if not best_social:
                                social_pattern = r'https?://(?:www\.)?(?:instagram|facebook|twitter)\.com/[^\s<>"\']+'
                                social_matches = re.findall(social_pattern, content, re.IGNORECASE)
                                valid_socials = [s for s in social_matches[:5] if self._is_valid_social_url(s)]
                                if valid_socials:
                                    best_social = self._select_best_social(valid_socials)

                        # Early exit if we found both
                        if best_email and best_social:
                            break

                    except asyncio.TimeoutError:
                        continue  # Try next page
                    except Exception:
                        continue  # Try next page
                
                return {
                    "url": url,
                    "timestamp": datetime.now().isoformat(),
                    "email": best_email,
                    "social_media": best_social,
                    "success": True
                }
        
        except asyncio.TimeoutError:
            return {
                "url": url,
                "error": "timeout",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "url": url,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _is_valid_email(self, email: str) -> bool:
        """Fast email validation."""
        if not email or len(email) < 5 or len(email) > 50:
            return False
        
        # Quick blacklist
        if any(ext in email for ext in ['.svg', '.png', '.jpg', '.jpeg', '.gif', '.css', '.js']):
            return False
        
        # Basic format check
        return bool(re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email))
    
    def _is_valid_social_url(self, url: str) -> bool:
        """Validate social media URL."""
        if not url or len(url) < 10:
            return False
        
        # Must be from supported platforms
        platforms = ['instagram.com', 'facebook.com', 'twitter.com', 'x.com']
        if not any(platform in url.lower() for platform in platforms):
            return False
        
        # Avoid post/photo URLs
        avoid_patterns = ['/p/', '/posts/', '/photo/', '/status/', '/tweet/']
        if any(pattern in url.lower() for pattern in avoid_patterns):
            return False
        
        return True
    
    def _select_best_email(self, emails: List[str]) -> Optional[Dict]:
        """Select best email quickly."""
        if not emails:
            return None
        
        # Priority prefixes
        priority = ['contact', 'info', 'hello', 'admin', 'support']
        
        for prefix in priority:
            for email in emails:
                if email.startswith(f"{prefix}@"):
                    return {"email": email, "confidence": 0.95}
        
        # Return shortest professional email
        best = min(emails, key=lambda x: (len(x), x.count('.')))
        return {"email": best, "confidence": 0.85}
    
    def _select_best_social(self, social_links: List[str]) -> Optional[Dict]:
        """Select best social media link."""
        if not social_links:
            return None
        
        # Platform priority
        priority = ['instagram.com', 'facebook.com', 'twitter.com']
        
        for platform in priority:
            for link in social_links:
                if platform in link.lower():
                    platform_name = platform.split('.')[0]
                    handle = self._extract_handle(link)
                    return {
                        "platform": platform_name,
                        "url": link,
                        "handle": handle,
                        "confidence": 0.90
                    }
        
        return None
    
    def _extract_handle(self, url: str) -> Optional[str]:
        """Extract handle from social URL."""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            path_parts = [p for p in parsed.path.strip('/').split('/') if p]
            if path_parts:
                return f"@{path_parts[0]}"
        except:
            pass
        return None
    
    def export_to_csv(self, results: List[Dict], filename: str):
        """Export results to CSV."""
        if not results:
            return
        
        rows = []
        for result in results:
            if 'error' in result:
                continue
            
            email = result.get('email', {})
            social = result.get('social_media', {})
            
            row = {
                'url': result.get('url', ''),
                'email': email.get('email', '') if email else '',
                'email_confidence': email.get('confidence', '') if email else '',
                'social_platform': social.get('platform', '') if social else '',
                'social_handle': social.get('handle', '') if social else '',
                'social_url': social.get('url', '') if social else '',
                'social_confidence': social.get('confidence', '') if social else '',
                'timestamp': result.get('timestamp', '')
            }
            rows.append(row)
        
        if rows:
            fieldnames = list(rows[0].keys())
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(rows)
            print(f"📊 Results exported to {filename}")
    
    def print_summary(self, results: List[Dict]):
        """Print extraction summary."""
        total = len(results)
        successful = len([r for r in results if 'error' not in r])
        errors = total - successful
        
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        
        print(f"\n📊 EXTRACTION SUMMARY:")
        print(f"   • Total URLs: {total}")
        print(f"   • Successful: {successful}")
        print(f"   • Errors: {errors}")
        
        if successful > 0:
            print(f"   • Emails found: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)")
            print(f"   • Social media found: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)")
            print(f"   • Both found: {len([r for r in results if 'error' not in r and r.get('email') and r.get('social_media')])}/{successful}")


# Main function for production use
async def extract_emails_socials_production(urls: Union[str, List[str]], 
                                          batch_size: int = 50,
                                          max_concurrent: int = 6) -> List[Dict]:
    """
    Production function for extracting emails and social media at scale.
    
    Args:
        urls: URLs to process
        batch_size: URLs per batch (default: 50)
        max_concurrent: Concurrent requests per batch (default: 6)
        
    Returns:
        List of extraction results
    """
    extractor = ProductionFastExtractor(batch_size=batch_size, max_concurrent=max_concurrent)
    return await extractor.extract_production(urls)
