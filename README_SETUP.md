# Lead Generation Contact Extraction Setup

This setup integrates your existing web scraper with your lead datasets to extract contact information from website URLs.

## 📁 Project Structure

```
leadgen_cs/
├── aganl/                          # Web scraper folder (from other project)
│   ├── perfect_contact_extractor.py   # Main scraper engine
│   └── example.py                      # Original examples
├── practice_urls.csv               # Practice dataset (101 URLs)
├── pittsburgh_coffee_prospects_FINAL_20250914_164553.csv  # Main dataset (8004 records)
├── requirements.txt                # Updated with scraper dependencies
├── test_practice_urls.py          # NEW: Test script for practice URLs
├── process_urls.py                # NEW: Full integration script
└── README_SETUP.md                # This file
```

## 🚀 Quick Start

### Step 1: Install Dependencies

```bash
pip install -r requirements.txt
```

**Note**: The scraper uses `crawl4ai` which may take a few minutes to install as it downloads browser components.

### Step 2: Test with Practice URLs

Start with the practice dataset to verify everything works:

```bash
python test_practice_urls.py
```

This will test in stages:
- **Stage 1**: 5 URLs (basic functionality)
- **Stage 2**: 15 URLs (performance test)  
- **Stage 3**: ~100 URLs (full practice set)

### Step 3: Process Main Dataset

Once testing is successful, process your main dataset:

```bash
python process_urls.py
```

This will:
1. Test with practice URLs first
2. Process 20 URLs from your main dataset
3. Create an enhanced dataset with contact information

## 📊 What Gets Extracted

The scraper extracts:
- **📧 Email addresses** (with confidence scores)
- **📞 Phone numbers** (with confidence scores)
- **🌐 Social media** (platform, handle, URL)
- **📈 Processing metrics** (pages checked, efficiency)

## 📈 Performance Expectations

Based on the scraper's documentation:
- **Processing Rate**: ~0.5-1.0 URLs/second
- **Success Rate**: ~84% contact discovery rate
- **Efficiency**: Smart early stopping saves ~30% processing time

For your main dataset (8,004 URLs):
- **Estimated Time**: 2-4 hours for full processing
- **Recommended**: Start with small batches (20-50 URLs)

## 🔧 Configuration Options

### Conservative Settings (for testing)
```python
processor = LeadGenProcessor(batch_size=5, max_concurrent=2)
```

### Production Settings (for large datasets)
```python
processor = LeadGenProcessor(batch_size=25, max_concurrent=5)
```

### High-Speed Settings (if your system can handle it)
```python
processor = LeadGenProcessor(batch_size=50, max_concurrent=8)
```

## 📁 Output Files

### Test Results
- `practice_test_small_YYYYMMDD_HHMMSS.csv` - Small batch test results
- `practice_test_medium_YYYYMMDD_HHMMSS.csv` - Medium batch test results  
- `practice_test_full_YYYYMMDD_HHMMSS.csv` - Full practice set results

### Production Results
- `contact_extraction_YYYYMMDD_HHMMSS.csv` - Raw extraction results
- `pittsburgh_coffee_prospects_with_contacts_YYYYMMDD_HHMMSS.csv` - Enhanced main dataset

## 🎯 Enhanced Dataset Columns

Your main dataset will get these new columns:
- `extracted_email` - Email address found
- `extracted_email_confidence` - Confidence score (0-1)
- `extracted_phone` - Phone number found
- `extracted_phone_confidence` - Confidence score (0-1)
- `extracted_social_platform` - Social media platform
- `extracted_social_handle` - Social media handle
- `extracted_social_url` - Full social media URL
- `extraction_timestamp` - When extraction was performed
- `pages_checked` - Number of pages scraped
- `extraction_efficiency` - Processing efficiency metric

## 🛠️ Troubleshooting

### Common Issues

1. **Import Error**: Make sure you're in the project root directory
2. **crawl4ai Installation**: May take 5-10 minutes on first install
3. **Memory Issues**: Reduce `batch_size` and `max_concurrent` settings
4. **Timeout Issues**: Some websites may be slow - this is normal

### Performance Tuning

- **Too Slow**: Increase `batch_size` and `max_concurrent`
- **Too Many Errors**: Decrease `max_concurrent` 
- **Memory Issues**: Decrease `batch_size`

## 📋 Recommended Workflow

1. **Test First**: Run `test_practice_urls.py` to verify setup
2. **Small Batch**: Process 20-50 URLs from main dataset
3. **Review Results**: Check quality and performance
4. **Scale Up**: Gradually increase batch sizes
5. **Full Processing**: Process entire dataset when confident

## 🎉 Success Metrics

Good performance indicators:
- **Error Rate**: < 5%
- **Contact Discovery**: > 70%
- **Processing Rate**: > 0.3 URLs/second
- **No Memory Issues**: Stable processing

## 🔄 Resuming Processing

If processing is interrupted:
1. Check the last generated CSV file
2. Modify the script to skip already processed URLs
3. Continue from where you left off

The scripts create timestamped files so you won't lose previous work.

---

**Ready to start?** Run `python test_practice_urls.py` to begin! 🚀
