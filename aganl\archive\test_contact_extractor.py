"""
Test script for the Contact Extractor
Tests basic functionality and crawl4ai integration.
"""

import asyncio
import sys
from contact_extractor import ContactExtractor, extract_contacts_from_url


async def test_basic_functionality():
    """Test basic functionality without requiring external URLs."""
    print("Testing Contact Extractor basic functionality...")
    
    try:
        # Test initialization
        extractor = ContactExtractor()
        print("✓ ContactExtractor initialized successfully")
        
        # Test extraction strategies setup
        strategies = extractor.extraction_strategies
        expected_strategies = ["css", "regex"]
        
        for strategy in expected_strategies:
            if strategy in strategies:
                print(f"✓ {strategy} strategy configured")
            else:
                print(f"✗ {strategy} strategy missing")
        
        # Test utility methods
        test_email = "<EMAIL>"
        if extractor._is_valid_email(test_email):
            print("✓ Email validation working")
        else:
            print("✗ Email validation failed")
        
        test_phone = "******-123-4567"
        cleaned_phone = extractor._clean_phone_number(test_phone)
        if cleaned_phone:
            print(f"✓ Phone cleaning working: {cleaned_phone}")
        else:
            print("✗ Phone cleaning failed")
        
        test_url = "https://twitter.com/example"
        platform = extractor._identify_social_platform(test_url)
        if platform == "twitter":
            print("✓ Social platform identification working")
        else:
            print("✗ Social platform identification failed")
        
        print("Basic functionality tests completed!")
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {str(e)}")
        return False


async def test_crawl4ai_integration():
    """Test crawl4ai integration with a simple example."""
    print("\nTesting crawl4ai integration...")
    
    try:
        from crawl4ai import AsyncWebCrawler
        print("✓ crawl4ai imported successfully")
        
        # Test basic crawler functionality
        async with AsyncWebCrawler(verbose=False) as crawler:
            # Use a simple, reliable URL for testing
            test_url = "https://httpbin.org/html"
            
            try:
                result = await crawler.arun(url=test_url, bypass_cache=True)
                
                if hasattr(result, 'success') and result.success:
                    print("✓ Basic crawling successful")
                    
                    if hasattr(result, 'cleaned_html') and result.cleaned_html:
                        print(f"✓ Content extracted (length: {len(result.cleaned_html)})")
                    else:
                        print("⚠ No content extracted")
                    
                    return True
                else:
                    print("✗ Crawling failed")
                    return False
                    
            except Exception as e:
                print(f"✗ Crawling test failed: {str(e)}")
                return False
                
    except ImportError as e:
        print(f"✗ crawl4ai import failed: {str(e)}")
        return False
    except Exception as e:
        print(f"✗ crawl4ai integration test failed: {str(e)}")
        return False


async def test_extraction_strategies():
    """Test extraction strategies with mock data."""
    print("\nTesting extraction strategies...")
    
    try:
        extractor = ContactExtractor()
        
        # Test data cleaning and structuring
        mock_extracted_content = {
            "emails": ["<EMAIL>", "<EMAIL>"],
            "phones": ["******-123-4567", "************"],
            "addresses": ["123 Main St, Anytown, ST 12345"],
            "social_links": ["https://twitter.com/company", "https://facebook.com/company"]
        }
        
        structured = extractor._clean_and_structure_contacts(mock_extracted_content)
        
        # Verify structure
        expected_keys = ["emails", "phones", "addresses", "social_media", "websites", "names_titles", "company_info"]
        for key in expected_keys:
            if key in structured:
                print(f"✓ {key} structure present")
            else:
                print(f"✗ {key} structure missing")
        
        # Verify email processing
        if structured["emails"] and len(structured["emails"]) == 2:
            print("✓ Email processing working")
        else:
            print("✗ Email processing failed")
        
        # Verify phone processing
        if structured["phones"] and len(structured["phones"]) == 2:
            print("✓ Phone processing working")
        else:
            print("✗ Phone processing failed")
        
        # Verify social media processing
        if structured["social_media"] and len(structured["social_media"]) == 2:
            print("✓ Social media processing working")
        else:
            print("✗ Social media processing failed")
        
        print("Extraction strategy tests completed!")
        return True
        
    except Exception as e:
        print(f"✗ Extraction strategy test failed: {str(e)}")
        return False


async def test_export_functionality():
    """Test export functionality."""
    print("\nTesting export functionality...")
    
    try:
        extractor = ContactExtractor()
        
        # Create mock contact data
        mock_contacts = [{
            "url": "https://example.com",
            "timestamp": "2024-01-01T12:00:00Z",
            "strategy": "test",
            "title": "Test Page",
            "contacts": {
                "emails": [{"email": "<EMAIL>", "confidence": 0.9}],
                "phones": [{"phone": "******-123-4567", "confidence": 0.8}],
                "addresses": [{"address": "123 Test St", "confidence": 0.7}]
            }
        }]
        
        # Test JSON export
        try:
            extractor.export_to_json(mock_contacts, "test_contacts.json")
            print("✓ JSON export working")
        except Exception as e:
            print(f"✗ JSON export failed: {str(e)}")
        
        # Test CSV export
        try:
            extractor.export_to_csv(mock_contacts, "test_contacts.csv")
            print("✓ CSV export working")
        except Exception as e:
            print(f"✗ CSV export failed: {str(e)}")
        
        print("Export functionality tests completed!")
        return True
        
    except Exception as e:
        print(f"✗ Export functionality test failed: {str(e)}")
        return False


async def run_all_tests():
    """Run all tests and provide summary."""
    print("Contact Extractor Test Suite")
    print("=" * 40)
    
    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("crawl4ai Integration", test_crawl4ai_integration),
        ("Extraction Strategies", test_extraction_strategies),
        ("Export Functionality", test_export_functionality)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The Contact Extractor is ready to use.")
    else:
        print("⚠ Some tests failed. Please check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    # Run the test suite
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
