"""
Restart the email verification backend with improved configuration
This should help with the "unknown" classification issue
"""

import subprocess
import time
import os
import requests

def stop_current_backend():
    """Stop the current backend."""
    print("🛑 Stopping current backend...")
    try:
        subprocess.run(['docker', 'stop', 'email-verifier'], capture_output=True)
        subprocess.run(['docker', 'rm', 'email-verifier'], capture_output=True)
        print("✅ Current backend stopped")
    except Exception as e:
        print(f"⚠️  Error stopping backend: {e}")

def start_improved_backend():
    """Start backend with improved configuration."""
    print("🚀 Starting backend with improved SMTP settings...")
    print("This should help with corporate email detection!")
    
    try:
        # Get absolute path to config file
        config_path = os.path.abspath("improved_backend_config.toml")
        
        # Run Docker with custom config mounted
        cmd = [
            'docker', 'run', '-d',
            '--name', 'email-verifier-improved',
            '-p', '8080:8080',
            '-v', f'{config_path}:/app/backend_config.toml',
            '-e', 'RCH__HELLO_NAME=orchidwebsolution.com',
            '-e', 'RCH__FROM_EMAIL=<EMAIL>',
            '-e', 'RCH__SMTP_TIMEOUT=60',
            'reacherhq/backend:latest'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            container_id = result.stdout.strip()
            print(f"✅ Improved backend started! Container ID: {container_id[:12]}")
            return True
        else:
            print(f"❌ Failed to start improved backend: {result.stderr}")
            
            # Fallback: try with just environment variables
            print("🔄 Trying fallback method with environment variables only...")
            cmd_fallback = [
                'docker', 'run', '-d',
                '--name', 'email-verifier-improved',
                '-p', '8080:8080',
                '-e', 'RCH__HELLO_NAME=orchidwebsolution.com',
                '-e', 'RCH__FROM_EMAIL=<EMAIL>',
                '-e', 'RCH__SMTP_TIMEOUT=60',
                'reacherhq/backend:latest'
            ]
            
            result = subprocess.run(cmd_fallback, capture_output=True, text=True)
            if result.returncode == 0:
                container_id = result.stdout.strip()
                print(f"✅ Fallback backend started! Container ID: {container_id[:12]}")
                return True
            else:
                print(f"❌ Fallback also failed: {result.stderr}")
                return False
                
    except Exception as e:
        print(f"❌ Error starting improved backend: {e}")
        return False

def wait_for_backend(max_wait=60):
    """Wait for the backend to be ready."""
    print("⏳ Waiting for improved backend to be ready...")
    
    for i in range(max_wait):
        try:
            response = requests.get("http://localhost:8080/v0/check_email", timeout=5)
            if response.status_code == 405:  # Method not allowed is expected for GET
                print("✅ Improved backend is ready!")
                return True
        except requests.exceptions.RequestException:
            pass
        
        if i % 10 == 0 and i > 0:
            print(f"   Still waiting... ({i}s)")
        
        time.sleep(1)
    
    print("❌ Backend did not start within 60 seconds")
    return False

def main():
    """Main function to restart with improved config."""
    print("🔧 RESTARTING WITH IMPROVED EMAIL VERIFICATION CONFIG")
    print("=" * 60)
    print("This should fix the 'unknown' classification issue!")
    print("Key improvements:")
    print("- Better HELLO name (orchidwebsolution.com vs localhost)")
    print("- Better FROM email (<EMAIL> vs hello@localhost)")
    print("- Longer SMTP timeout (60s vs default)")
    print("- Optimized verification methods per provider")
    print()
    
    # Stop current backend
    stop_current_backend()
    
    # Start improved backend
    if not start_improved_backend():
        print("❌ Failed to start improved backend")
        return
    
    # Wait for it to be ready
    if not wait_for_backend():
        print("❌ Improved backend failed to start properly")
        return
    
    print("\n🎉 IMPROVED BACKEND IS READY!")
    print("=" * 40)
    print("Now try running:")
    print("1. python debug_email_verifier.py  # See detailed responses")
    print("2. python fast_email_verifier.py   # Run full test")
    print()
    print("The improved config should give better results for corporate emails!")

if __name__ == "__main__":
    main()
