"""
Debug Email Verification - Let's see what's really happening
This will show detailed responses to understand why emails are coming back as "unknown"
"""

import asyncio
import aiohttp
import json
from datetime import datetime

class DebugEmailVerifier:
    def __init__(self, backend_url: str = "http://localhost:8080"):
        self.backend_url = backend_url
        self.session = None
        
    async def __aenter__(self):
        connector = aiohttp.TCPConnector(limit=10)
        timeout = aiohttp.ClientTimeout(total=60)
        self.session = aiohttp.ClientSession(connector=connector, timeout=timeout)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def debug_single_email(self, email: str) -> dict:
        """Debug a single email with full response details."""
        print(f"\n🔍 DEBUGGING: {email}")
        print("-" * 50)
        
        try:
            payload = {"to_email": email.strip()}
            
            async with self.session.post(
                f"{self.backend_url}/v0/check_email",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    # Print key details
                    print(f"📧 Email: {result.get('input', 'N/A')}")
                    print(f"🎯 Reachable: {result.get('is_reachable', 'N/A')}")
                    
                    # Syntax check
                    syntax = result.get('syntax', {})
                    print(f"✏️  Syntax Valid: {syntax.get('is_valid_syntax', 'N/A')}")
                    print(f"🌐 Domain: {syntax.get('domain', 'N/A')}")
                    
                    # MX Records
                    mx = result.get('mx', {})
                    print(f"📬 Accepts Mail: {mx.get('accepts_mail', 'N/A')}")
                    mx_records = mx.get('records', [])
                    print(f"📮 MX Records: {len(mx_records)} found")
                    if mx_records:
                        print(f"   First MX: {mx_records[0]}")
                    
                    # SMTP Details
                    smtp = result.get('smtp', {})
                    print(f"🔌 Can Connect SMTP: {smtp.get('can_connect_smtp', 'N/A')}")
                    print(f"📨 Is Deliverable: {smtp.get('is_deliverable', 'N/A')}")
                    print(f"🚫 Is Disabled: {smtp.get('is_disabled', 'N/A')}")
                    print(f"📥 Is Catch All: {smtp.get('is_catch_all', 'N/A')}")
                    print(f"📪 Full Inbox: {smtp.get('has_full_inbox', 'N/A')}")
                    
                    # Misc info
                    misc = result.get('misc', {})
                    print(f"🗑️  Is Disposable: {misc.get('is_disposable', 'N/A')}")
                    print(f"👔 Is Role Account: {misc.get('is_role_account', 'N/A')}")
                    
                    # Show full JSON for analysis
                    print(f"\n📋 FULL RESPONSE:")
                    print(json.dumps(result, indent=2))
                    
                    return result
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP Error {response.status}: {error_text}")
                    return {"error": f"HTTP {response.status}", "details": error_text}
                    
        except Exception as e:
            print(f"❌ Exception: {e}")
            return {"error": "Exception", "details": str(e)}

async def main():
    """Debug specific emails to understand the 'unknown' issue."""
    print("🔍 EMAIL VERIFICATION DEBUG MODE")
    print("=" * 50)
    print("Let's see exactly what's happening with these emails...")
    
    # Test a mix of emails - some that should work, some that shouldn't
    test_emails = [
        "<EMAIL>",  # Your coffee company
        "<EMAIL>",        # Should be valid
        "<EMAIL>",        # Should be valid  
        "<EMAIL>",          # Should be valid
        "<EMAIL>",  # Should be invalid
        "<EMAIL>",            # Might be disabled
        "<EMAIL>"         # Should be valid
    ]
    
    async with DebugEmailVerifier() as verifier:
        for email in test_emails:
            result = await verifier.debug_single_email(email)
            
            # Wait a bit between requests to avoid overwhelming
            await asyncio.sleep(2)
            
            print("\n" + "="*70)
    
    print("\n🎯 ANALYSIS:")
    print("Look for patterns in the responses above:")
    print("- Are MX records being found?")
    print("- Is SMTP connection working?") 
    print("- What's the exact reason for 'unknown' status?")
    print("- Do we need to configure better HELLO/FROM settings?")

if __name__ == "__main__":
    asyncio.run(main())
