---
cover: .gitbook/assets/docs_cover.jpg
coverY: 0
layout:
  cover:
    visible: true
    size: full
  title:
    visible: true
  description:
    visible: false
  tableOfContents:
    visible: true
  outline:
    visible: true
  pagination:
    visible: true
---

# Welcome to <PERSON> is an open-source email verification API.

You can use <PERSON> to ensure the deliverability of your emails, clean your email lists, and prevent bounces. The API supports both individual email checks and bulk verification processes.

### Jump right in

<table data-view="cards"><thead><tr><th></th><th></th><th data-hidden data-card-cover data-type="files"></th><th data-hidden></th><th data-hidden data-card-target data-type="content-ref"></th></tr></thead><tbody><tr><td><strong>Getting Started</strong></td><td>Verify your 1st email</td><td><a href=".gitbook/assets/undraw_Mail_sent_re_0ofv.png">undraw_Mail_sent_re_0ofv.png</a></td><td></td><td><a href="getting-started/quickstart.md">quickstart.md</a></td></tr><tr><td><strong>Self-Hosting</strong></td><td>Install Reacher in 20 min</td><td><a href=".gitbook/assets/undraw_Server_re_twwj.png">undraw_Server_re_twwj.png</a></td><td></td><td><a href="self-hosting/install.md">install.md</a></td></tr><tr><td><strong>Proxies</strong></td><td>Verify emails using a proxy</td><td><a href=".gitbook/assets/undraw_online_transactions_02ka.png">undraw_online_transactions_02ka.png</a></td><td></td><td><a href="self-hosting/proxies/">proxies</a></td></tr></tbody></table>
