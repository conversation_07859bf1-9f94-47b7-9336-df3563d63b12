"""
Full Dataset Contact + Business Extractor
Production script to process all 8,004 Pittsburgh prospects overnight.
"""

import asyncio
import pandas as pd
import re
import json
import aiohttp
from datetime import datetime
from aganl.perfect_contact_extractor import PerfectContactExtractor


class FullDatasetExtractor(PerfectContactExtractor):
    """Production extractor for processing the complete dataset overnight."""
    
    def __init__(self, batch_size: int = 20, max_concurrent: int = 30):
        """Initialize with optimized settings for full dataset processing."""
        super().__init__(batch_size, max_concurrent)
        self.start_time = datetime.now()  # Initialize with current time
        self.processed_count = 0
        self.total_count = 0
    
    async def process_full_dataset(self, input_file: str, output_file: str):
        """Process the complete Pittsburgh prospects dataset."""
        print("🚀 FULL DATASET EXTRACTION - PITTSBURGH PROSPECTS")
        print("=" * 70)
        print("Processing complete dataset overnight - you can leave your PC!")
        
        # Load the full dataset
        try:
            df = pd.read_csv(input_file)
            urls = df['website'].dropna().tolist()
            self.total_count = len(urls)
            print(f"✅ Loaded {self.total_count} URLs from {input_file}")
        except Exception as e:
            print(f"❌ Error loading dataset: {e}")
            return
        
        print(f"\n🎯 EXTRACTION SETTINGS:")
        print(f"   • Batch size: {self.batch_size}")
        print(f"   • Max concurrent: {self.max_concurrent}")
        print(f"   • Expected time: ~{self.total_count / (0.8 * 3600):.1f} hours")
        print(f"   • Output file: {output_file}")
        
        self.start_time = datetime.now()
        print(f"\n⏰ Started at: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("💤 Safe to leave PC - script will run overnight!")
        
        # Process all URLs
        results = await self.extract_production_data(urls)

        # Merge with original data and export
        self._merge_and_export(df, results, output_file)

        # Final summary with error statistics
        self._print_final_summary(results)
    
    async def extract_production_data(self, urls: list) -> list:
        """Extract production-ready contact + business data with progress tracking and retry logic."""
        print(f"\n🔥 Starting extraction for {len(urls)} URLs...")

        # Use the proven perfect extraction method
        results = await self.extract_perfect(urls)

        # Count and retry failed URLs (network errors only)
        failed_urls = []
        network_error_keywords = [
            "ERR_NAME_NOT_RESOLVED", "ERR_CONNECTION_REFUSED", "ERR_CONNECTION_CLOSED",
            "ERR_ABORTED", "ERR_CONNECTION_TIMED_OUT", "Navigation failed", "Network error"
        ]

        for result in results:
            if 'error' in result:
                error_msg = result['error']
                # Only retry network-related errors, not permanent issues
                if any(keyword in error_msg for keyword in network_error_keywords):
                    failed_urls.append(result['url'])

        # Retry failed URLs once with longer timeout
        if failed_urls:
            print(f"🔄 Retrying {len(failed_urls)} failed URLs with extended timeout...")
            retry_extractor = FullDatasetExtractor(batch_size=10, max_concurrent=15)  # Slower, more conservative
            retry_results = await retry_extractor.extract_perfect(failed_urls)

            # Replace failed results with retry results
            retry_dict = {r['url']: r for r in retry_results}
            for i, result in enumerate(results):
                if result.get('url') in retry_dict:
                    results[i] = retry_dict[result['url']]

        # Clean and enhance each result with progress tracking
        print(f"🔧 Cleaning and validating {len(results)} results...")

        production_results = []
        for i, result in enumerate(results):
            if 'error' not in result:
                cleaned_result = await self._clean_and_validate_result(result)
                production_results.append(cleaned_result)
            else:
                production_results.append(result)

            # Progress updates every 100 results
            if (i + 1) % 100 == 0:
                elapsed = (datetime.now() - self.start_time).total_seconds()
                rate = (i + 1) / elapsed
                remaining = (len(results) - i - 1) / rate if rate > 0 else 0
                print(f"   ✅ Processed {i + 1}/{len(results)} results ({rate:.2f}/sec, {remaining/3600:.1f}h remaining)")

        return production_results
    
    async def _clean_and_validate_result(self, result: dict) -> dict:
        """Clean and validate a single result."""
        url = result.get('url', '')
        
        # Clean email data
        email_data = self._clean_email_data(result.get('email'))
        
        # Clean phone data
        phone_data = self._clean_phone_data(result.get('phone'))
        
        # Clean social media data
        social_media = self._clean_social_media_data(result.get('social_media'))
        
        # Get business info and contact form detection
        business_info = await self._get_business_info(url)
        contact_form_detected = business_info.pop('contact_form_detected', False)
        
        # Create clean production result
        production_result = {
            'url': url,
            'timestamp': result.get('timestamp', datetime.now().isoformat()),
            # VALIDATED CONTACT DATA
            'email': email_data['email'],
            'email_confidence': email_data['confidence'],
            'email_status': email_data['status'],
            'phone': phone_data['phone'],
            'phone_confidence': phone_data['confidence'],
            'phone_status': phone_data['status'],
            'social_media': social_media,
            'contact_form_detected': contact_form_detected,
            'pages_checked': result.get('pages_checked', 0),
            # BUSINESS INTELLIGENCE
            **business_info
        }
        
        return production_result
    
    def _clean_email_data(self, email_raw) -> dict:
        """Clean and validate email data."""
        email = ''
        confidence = 0
        status = 'not_found'
        
        # Extract email from various formats
        if isinstance(email_raw, dict):
            email = email_raw.get('email', '')
            confidence = email_raw.get('confidence', 0)
        elif isinstance(email_raw, str) and email_raw and email_raw != 'None':
            email = email_raw
            confidence = 0.8
        
        # Validate email
        if email and self._is_valid_email(email) and self._is_business_email(email):
            status = 'found'
        else:
            email = ''
            confidence = 0
            status = 'not_found'
        
        return {
            'email': email,
            'confidence': confidence,
            'status': status
        }
    
    def _clean_phone_data(self, phone_raw) -> dict:
        """Clean and validate phone data."""
        phone = ''
        confidence = 0
        status = 'not_found'
        
        # Extract phone from various formats
        if isinstance(phone_raw, dict):
            phone = phone_raw.get('phone', '')
            confidence = phone_raw.get('confidence', 0)
        elif isinstance(phone_raw, str) and phone_raw and phone_raw != 'None':
            phone = phone_raw
            confidence = 0.8
        
        # Clean and validate phone
        if phone:
            clean_phone = self._format_phone_number(phone)
            if clean_phone and self._is_valid_phone(clean_phone):
                phone = clean_phone
                status = 'found'
            else:
                phone = ''
                confidence = 0
                status = 'not_found'
        
        return {
            'phone': phone,
            'confidence': confidence,
            'status': status
        }
    
    def _clean_social_media_data(self, social_raw) -> str:
        """Clean social media data."""
        if not social_raw:
            return ''
        
        if isinstance(social_raw, dict):
            platform = social_raw.get('platform', '')
            url = social_raw.get('url', '')
            if platform and url:
                return f"{platform}: {url}"
            return url
        
        return str(social_raw) if social_raw != 'None' else ''
    
    async def _get_business_info(self, url: str) -> dict:
        """Get business information from multiple formats (HTML, JSON, APIs)."""
        try:
            # Try different extraction methods in order of preference

            # 1. Try API endpoints first (fastest and most structured)
            api_result = await self._try_api_extraction(url)
            if api_result:
                return api_result

            # 2. Try JSON-LD structured data
            json_result = await self._try_json_extraction(url)
            if json_result:
                return json_result

            # 3. Fall back to HTML extraction (most reliable)
            html_result = await self._try_html_extraction(url)
            if html_result:
                return html_result

        except Exception:
            # Silent failure for production - don't spam logs
            pass

        return self._empty_business_info()

    async def _try_api_extraction(self, url: str) -> dict | None:
        """Try to extract from common API endpoints."""
        try:
            # Common API patterns for business info
            api_endpoints = [
                f"{url.rstrip('/')}/api/contact",
                f"{url.rstrip('/')}/api/business",
                f"{url.rstrip('/')}/api/company",
                f"{url.rstrip('/')}/wp-json/wp/v2/pages",  # WordPress API
            ]

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                for endpoint in api_endpoints:
                    try:
                        async with session.get(endpoint) as response:
                            if response.status == 200:
                                data = await response.json()
                                return self._parse_api_data(data, url)
                    except:
                        continue

        except Exception:
            pass

        return None

    async def _try_json_extraction(self, url: str) -> dict | None:
        """Try to extract from JSON-LD structured data."""
        try:
            from crawl4ai import AsyncWebCrawler
            async with AsyncWebCrawler() as crawler:
                from crawl4ai import CrawlerRunConfig
                config = CrawlerRunConfig()
                result = await crawler.arun(url=url, config=config)

                if result.success and result.cleaned_html:
                    # Look for JSON-LD structured data
                    json_ld_matches = re.findall(
                        r'<script[^>]*type=["\']application/ld\+json["\'][^>]*>(.*?)</script>',
                        result.cleaned_html,
                        re.DOTALL | re.IGNORECASE
                    )

                    for json_str in json_ld_matches:
                        try:
                            data = json.loads(json_str.strip())
                            parsed = self._parse_json_ld(data, url)
                            if parsed:
                                return parsed
                        except:
                            continue

        except Exception:
            pass

        return None

    async def _try_html_extraction(self, url: str) -> dict | None:
        """Try HTML extraction (fallback method)."""
        try:
            from crawl4ai import AsyncWebCrawler
            async with AsyncWebCrawler() as crawler:
                from crawl4ai import CrawlerRunConfig
                config = CrawlerRunConfig()
                result = await crawler.arun(url=url, config=config)

                if result.success and result.cleaned_html:
                    return self._extract_business_from_html(result.cleaned_html)

        except Exception:
            pass

        return None

    def _parse_api_data(self, data: dict, _url: str) -> dict | None:
        """Parse business info from API response."""
        try:
            business_info = self._empty_business_info()

            # Common API field mappings
            field_mappings = {
                'company_name': ['name', 'company', 'business_name', 'title', 'organization'],
                'about_text': ['description', 'about', 'summary', 'bio', 'content'],
                'services_text': ['services', 'products', 'offerings', 'specialties'],
                'page_title': ['title', 'name', 'heading'],
                'meta_description': ['description', 'summary', 'excerpt']
            }

            for field, possible_keys in field_mappings.items():
                for key in possible_keys:
                    if key in data and data[key]:
                        business_info[field] = str(data[key])[:400]
                        break

            # Generate business summary
            business_info['business_summary'] = self._generate_business_summary(business_info)
            business_info['contact_form_detected'] = False  # APIs usually don't have forms

            return business_info

        except Exception:
            return None

    def _parse_json_ld(self, data: dict, _url: str) -> dict | None:
        """Parse business info from JSON-LD structured data."""
        try:
            business_info = self._empty_business_info()

            # Handle different JSON-LD types
            if isinstance(data, list):
                data = data[0] if data else {}

            # Organization/LocalBusiness schema
            if data.get('@type') in ['Organization', 'LocalBusiness', 'Corporation', 'Company']:
                business_info['company_name'] = data.get('name', '')
                business_info['about_text'] = data.get('description', '')[:400]
                business_info['page_title'] = data.get('name', '')
                business_info['meta_description'] = data.get('description', '')[:200]

                # Services from JSON-LD
                services = data.get('hasOfferCatalog', {}).get('itemListElement', [])
                if services:
                    service_names = [item.get('name', '') for item in services if item.get('name')]
                    business_info['services_text'] = ' | '.join(service_names[:5])[:300]

            # WebSite schema
            elif data.get('@type') == 'WebSite':
                business_info['page_title'] = data.get('name', '')
                business_info['meta_description'] = data.get('description', '')[:200]

            # Generate business summary
            business_info['business_summary'] = self._generate_business_summary(business_info)
            business_info['contact_form_detected'] = False

            return business_info if any(business_info.values()) else None

        except Exception:
            return None

    def _extract_business_from_html(self, html_content: str) -> dict:
        """Extract business information from HTML."""
        business_info = {}
        
        # Page title
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
        business_info['page_title'] = title_match.group(1).strip() if title_match else ''
        
        # Meta description
        meta_match = re.search(r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']', html_content, re.IGNORECASE)
        business_info['meta_description'] = meta_match.group(1).strip() if meta_match else ''
        
        # Company name
        business_info['company_name'] = self._extract_company_name(html_content)
        
        # About text
        business_info['about_text'] = self._extract_about_text(html_content)
        
        # Services text
        business_info['services_text'] = self._extract_services_text(html_content)
        
        # Key headings
        business_info['key_headings'] = self._extract_headings(html_content)
        
        # Business summary
        business_info['business_summary'] = self._generate_business_summary(business_info)
        
        # Contact form detection
        business_info['contact_form_detected'] = self._detect_contact_form(html_content)
        
        return business_info
    
    def _extract_company_name(self, html_content: str) -> str:
        """Extract company name."""
        patterns = [
            r'<h1[^>]*>([^<]+)</h1>',
            r'<title[^>]*>([^<|\\-]+)',
            r'class=["\'][^"\']*(?:company|brand|business)[^"\']*["\'][^>]*>([^<]+)',
            r'<img[^>]*alt=["\']([^"\']*(?:logo|company|brand)[^"\']*)["\']',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                candidate = match.strip()
                if (len(candidate) > 2 and len(candidate) < 100 and 
                    not any(word in candidate.lower() for word in ['home', 'welcome', 'contact', 'about', 'menu'])):
                    return candidate
        
        return ''
    
    def _extract_about_text(self, html_content: str) -> str:
        """Extract about text."""
        patterns = [
            r'<[^>]*(?:class|id)=["\'][^"\']*about[^"\']*["\'][^>]*>([^<]+)',
            r'(?i)about\s+us[^<]*<[^>]*>([^<]+)',
            r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']',
        ]
        
        about_texts = []
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                text = re.sub(r'\s+', ' ', match.strip())
                if len(text) > 20 and len(text) < 500:
                    about_texts.append(text)
        
        return ' '.join(about_texts[:2])[:400] if about_texts else ''
    
    def _extract_services_text(self, html_content: str) -> str:
        """Extract services text."""
        patterns = [
            r'<[^>]*(?:class|id)=["\'][^"\']*service[^"\']*["\'][^>]*>([^<]+)',
            r'(?i)(?:our\s+)?(?:services|products)[^<]*<[^>]*>([^<]+)',
        ]
        
        service_texts = []
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                text = re.sub(r'\s+', ' ', match.strip())
                if len(text) > 10 and len(text) < 300:
                    service_texts.append(text)
        
        return ' '.join(service_texts[:2])[:300] if service_texts else ''
    
    def _extract_headings(self, html_content: str) -> str:
        """Extract key headings."""
        heading_matches = re.findall(r'<h[1-3][^>]*>([^<]+)</h[1-3]>', html_content, re.IGNORECASE)
        headings = []
        for heading in heading_matches:
            clean_heading = re.sub(r'\s+', ' ', heading.strip())
            if len(clean_heading) > 2 and len(clean_heading) < 80:
                headings.append(clean_heading)
        
        return ' | '.join(headings[:6])
    
    def _generate_business_summary(self, business_info: dict) -> str:
        """Generate business summary."""
        parts = []
        
        if business_info.get('company_name'):
            parts.append(business_info['company_name'])
        
        if business_info.get('meta_description'):
            parts.append(business_info['meta_description'][:100])
        elif business_info.get('about_text'):
            parts.append(business_info['about_text'][:100])
        
        return ' | '.join(parts)
    
    def _detect_contact_form(self, html_content: str) -> bool:
        """Detect contact forms."""
        patterns = [
            r'<form[^>]*(?:contact|email|inquiry)[^>]*>',
            r'<input[^>]*type=["\']email["\']',
            r'<textarea[^>]*(?:message|inquiry)[^>]*>',
        ]
        
        for pattern in patterns:
            if re.search(pattern, html_content, re.IGNORECASE):
                return True
        return False
    
    def _empty_business_info(self) -> dict:
        """Return empty business info."""
        return {
            'page_title': '',
            'meta_description': '',
            'company_name': '',
            'about_text': '',
            'services_text': '',
            'key_headings': '',
            'business_summary': '',
            'contact_form_detected': False
        }
    
    def _is_valid_email(self, email: str) -> bool:
        """Validate email format."""
        if not email or len(email) < 5:
            return False
        pattern = r'^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$'
        return bool(re.match(pattern, email))
    
    def _is_business_email(self, email: str) -> bool:
        """Check if email is business email."""
        excluded_domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com']
        domain = email.split('@')[1].lower() if '@' in email else ''
        return domain not in excluded_domains
    
    def _format_phone_number(self, phone: str) -> str:
        """Format phone number."""
        digits = re.sub(r'[^\d+]', '', phone)
        
        if digits.startswith('1') and len(digits) == 11:
            digits = digits[1:]
        
        if len(digits) == 10:
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif digits.startswith('+'):
            return digits
        
        return phone
    
    def _is_valid_phone(self, phone: str) -> bool:
        """Validate phone number."""
        if not phone:
            return False
        digits_only = re.sub(r'[^\d]', '', phone)
        return len(digits_only) >= 10 and len(digits_only) <= 15
    
    def _merge_and_export(self, original_df: pd.DataFrame, results: list, output_file: str):
        """Merge extraction results with original data and export companies with emails OR phone numbers."""
        print(f"\n📊 Processing extraction results...")

        # Count results by type
        email_results = []
        phone_results = []
        failed_results = []
        no_contact_results = []

        for result in results:
            if 'error' in result:
                failed_results.append(result)
            else:
                # Check if we found a valid email
                email = result.get('email', '')
                if isinstance(email, dict):
                    email = email.get('email', '')

                # Check if we found a valid phone
                phone = result.get('phone', '')
                if isinstance(phone, dict):
                    phone = phone.get('phone', '')

                has_email = email and email.strip() and email != 'None'
                has_phone = phone and phone.strip() and phone != 'None'

                if has_email:
                    email_results.append(result)
                elif has_phone:
                    phone_results.append(result)
                else:
                    no_contact_results.append(result)

        print(f"   📧 Companies WITH emails: {len(email_results)} (will be saved to email CSV)")
        print(f"   📞 Companies WITH phones (no email): {len(phone_results)} (will be saved to phone CSV)")
        print(f"   ❌ Failed extractions: {len(failed_results)} (will be excluded)")
        print(f"   📭 No contact info found: {len(no_contact_results)} (will be excluded)")
        print(f"   🎯 Companies with email OR phone will be saved to separate files")

        # Helper function to create flattened result dictionary
        def create_flattened_result(result):
            """Helper function to create flattened result dictionary."""
            # Extract email properly
            email_data = result.get('email', '')
            if isinstance(email_data, dict):
                email = email_data.get('email', '')
                email_confidence = email_data.get('confidence', '')
            else:
                email = email_data
                email_confidence = result.get('email_confidence', '')

            # Extract phone properly
            phone_data = result.get('phone', '')
            if isinstance(phone_data, dict):
                phone = phone_data.get('phone', '')
                phone_confidence = phone_data.get('confidence', '')
            else:
                phone = phone_data
                phone_confidence = result.get('phone_confidence', '')

            return {
                'url': result.get('url', ''),
                'timestamp': result.get('timestamp', ''),
                # CONTACT DATA
                'email': email,
                'email_confidence': email_confidence,
                'email_status': result.get('email_status', 'found'),
                'phone': phone,
                'phone_confidence': phone_confidence,
                'phone_status': result.get('phone_status', ''),
                'social_media': result.get('social_media', ''),
                'contact_form_detected': result.get('contact_form_detected', False),
                'pages_checked': result.get('pages_checked', ''),
                # BUSINESS DATA
                'page_title': result.get('page_title', ''),
                'meta_description': result.get('meta_description', ''),
                'company_name': result.get('company_name', ''),
                'about_text': result.get('about_text', ''),
                'services_text': result.get('services_text', ''),
                'key_headings': result.get('key_headings', ''),
                'business_summary': result.get('business_summary', '')
            }

        # Create email dataset
        email_flattened = [create_flattened_result(result) for result in email_results]

        # Create phone dataset
        phone_flattened = [create_flattened_result(result) for result in phone_results]

        if len(email_flattened) == 0 and len(phone_flattened) == 0:
            print(f"⚠️  No companies with email addresses or phone numbers found!")
            print(f"   Consider adjusting extraction settings or checking more URLs")
            return

        # Create separate output files
        base_filename = output_file.replace('.csv', '')
        email_filename = f"{base_filename}_emails.csv"
        phone_filename = f"{base_filename}_phones.csv"

        # Process email dataset
        if len(email_flattened) > 0:
            print(f"📊 Merging email dataset with original data...")
            email_df = pd.DataFrame(email_flattened)
            email_merged_df = original_df.merge(email_df, left_on='website', right_on='url', how='inner')

            # Remove duplicate url column
            if 'url' in email_merged_df.columns:
                email_merged_df = email_merged_df.drop('url', axis=1)

            print(f"   📈 Email dataset contains {len(email_merged_df)} companies with email addresses")

            # Export email results
            email_merged_df.to_csv(email_filename, index=False)
            print(f"💾 Email results exported to: {email_filename}")
            print(f"   📧 Ready for email outreach campaigns!")

        # Process phone dataset
        if len(phone_flattened) > 0:
            print(f"📊 Merging phone dataset with original data...")
            phone_df = pd.DataFrame(phone_flattened)
            phone_merged_df = original_df.merge(phone_df, left_on='website', right_on='url', how='inner')

            # Remove duplicate url column
            if 'url' in phone_merged_df.columns:
                phone_merged_df = phone_merged_df.drop('url', axis=1)

            print(f"   📈 Phone dataset contains {len(phone_merged_df)} companies with phone numbers")

            # Export phone results
            phone_merged_df.to_csv(phone_filename, index=False)
            print(f"💾 Phone results exported to: {phone_filename}")
            print(f"   📞 Ready for phone outreach campaigns!")

        print(f"\n🎯 SUMMARY:")
        print(f"   📧 Companies with emails: {len(email_flattened)} → {email_filename}")
        print(f"   📞 Companies with phones only: {len(phone_flattened)} → {phone_filename}")
        print(f"   🚫 Companies excluded (no contact info): {len(no_contact_results) + len(failed_results)}")
    
    def _print_final_summary(self, results: list | None = None):
        """Print final extraction summary with error statistics."""
        end_time = datetime.now()
        duration = end_time - self.start_time

        print(f"\n🎉 FULL DATASET EXTRACTION COMPLETE!")
        print("=" * 70)
        print(f"⏰ Started: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ Finished: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  Total time: {duration}")
        print(f"📊 Total URLs processed: {self.total_count}")
        print(f"⚡ Average rate: {self.total_count / duration.total_seconds():.2f} URLs/second")

        # Error statistics if results provided
        if results:
            successful = len([r for r in results if 'error' not in r])
            failed = len([r for r in results if 'error' in r])
            success_rate = (successful / len(results)) * 100 if results else 0

            print(f"\n📈 SUCCESS STATISTICS:")
            print(f"   ✅ Successful: {successful} ({success_rate:.1f}%)")
            print(f"   ❌ Failed: {failed} ({100-success_rate:.1f}%)")

            # Count error types
            if failed > 0:
                error_types = {}
                for r in results:
                    if 'error' in r:
                        error = r['error']
                        if 'DNS resolution failed' in error:
                            error_types['DNS Issues'] = error_types.get('DNS Issues', 0) + 1
                        elif 'Connection failed' in error or 'Connection aborted' in error:
                            error_types['Connection Issues'] = error_types.get('Connection Issues', 0) + 1
                        elif 'timeout' in error.lower():
                            error_types['Timeouts'] = error_types.get('Timeouts', 0) + 1
                        elif 'SSL' in error:
                            error_types['SSL Issues'] = error_types.get('SSL Issues', 0) + 1
                        else:
                            error_types['Other'] = error_types.get('Other', 0) + 1

                print(f"\n🔍 ERROR BREAKDOWN:")
                for error_type, count in error_types.items():
                    print(f"   • {error_type}: {count}")

        # Calculate actual extraction rates focusing on email extraction
        if results:
            total_processed = len(results)
            failed_results = [r for r in results if 'error' in r]
            successful_results = [r for r in results if 'error' not in r]

            # Count companies with emails (our target)
            companies_with_emails = []
            for r in successful_results:
                email = r.get('email', '')
                if isinstance(email, dict):
                    email = email.get('email', '')
                if email and email.strip() and email != 'None':
                    companies_with_emails.append(r)

            if companies_with_emails:
                companies_found = len([r for r in companies_with_emails if r.get('company_name')])

                # Count companies with emails OR phones
                companies_with_phones = []
                for r in successful_results:
                    phone = r.get('phone', '')
                    if isinstance(phone, dict):
                        phone = phone.get('phone', '')
                    if phone and phone.strip() and phone != 'None':
                        companies_with_phones.append(r)

                # Count companies with emails only (for phone-only count)
                phone_only_companies = []
                for r in companies_with_phones:
                    email = r.get('email', '')
                    if isinstance(email, dict):
                        email = email.get('email', '')
                    has_email = email and email.strip() and email != 'None'
                    if not has_email:
                        phone_only_companies.append(r)

                print(f"\n🎯 CONTACT-FOCUSED EXTRACTION RESULTS:")
                print(f"   📊 Total URLs processed: {total_processed}")
                print(f"   ❌ Failed/unreachable: {len(failed_results)}")
                print(f"   ✅ Successfully scraped: {len(successful_results)}")
                print(f"   📧 Companies WITH emails: {len(companies_with_emails)} ({len(companies_with_emails)/total_processed*100:.1f}% of total)")
                print(f"   📞 Companies WITH phones: {len(companies_with_phones)} ({len(companies_with_phones)/total_processed*100:.1f}% of total)")
                print(f"   📞 Phone-only companies: {len(phone_only_companies)} ({len(phone_only_companies)/total_processed*100:.1f}% of total)")
                print(f"   🏢 Company names extracted: {companies_found} ({companies_found/len(companies_with_emails)*100:.1f}% of email companies)")
                print(f"\n💾 Final datasets:")
                print(f"   📧 Email dataset: {len(companies_with_emails)} companies")
                print(f"   📞 Phone-only dataset: {len(phone_only_companies)} companies")
                print(f"   🚫 Excluded (no contact info): {total_processed - len(companies_with_emails) - len(phone_only_companies)} companies")

        print(f"\n✅ Your Pittsburgh prospects databases are ready!")
        print(f"📧 Email dataset: Perfect for email outreach campaigns!")
        print(f"📞 Phone dataset: Perfect for cold calling campaigns!")
        print(f"🎯 Two targeted datasets for maximum outreach effectiveness!")


async def main():
    """Main function to process the full dataset."""
    # Configuration
    INPUT_FILE = "pittsburgh_coffee_prospects_FINAL_20250914_164553.csv"
    OUTPUT_FILE = f"pittsburgh_prospects_with_contacts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    # Create extractor with optimized settings
    extractor = FullDatasetExtractor(batch_size=20, max_concurrent=30)
    
    # Process the full dataset
    await extractor.process_full_dataset(INPUT_FILE, OUTPUT_FILE)


if __name__ == "__main__":
    asyncio.run(main())
