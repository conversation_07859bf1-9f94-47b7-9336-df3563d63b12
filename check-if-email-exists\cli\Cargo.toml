[package]
name = "check-if-email-exists-cli"
version = "0.11.6"
default-run = "check_if_email_exists"
edition = "2018"
description = "Check if an email address exists without sending any email."
authors = ["Amaury <<EMAIL>>"]
license = "AGPL-3.0"
publish = false

[[bin]]
name = "check_if_email_exists"
path = "src/main.rs"

[dependencies]
anyhow = "1.0"
check-if-email-exists = { path = "../core" }
clap = { version = "3.2", features = ["derive", "env"] }
once_cell = "1.21"
openssl = { version = "0.10", features = ["vendored"] }
serde = "1.0"
serde_json = "1.0"
tracing-subscriber = "0.3.18"

[dependencies.tokio]
version = "1.40.0"
features = ["macros", "net", "io-util", "rt-multi-thread"]

[profile.release]
lto = true
