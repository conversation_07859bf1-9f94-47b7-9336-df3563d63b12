"""
Intelligent Contact Extractor with Smart Page Discovery
Discovers available pages first, then prioritizes the most promising ones for extraction.
"""

import asyncio
import json
import re
import csv
import aiohttp
from datetime import datetime
from typing import Dict, List, Optional, Union, Tuple
from urllib.parse import urljoin, urlparse

from crawl4ai import As<PERSON><PERSON><PERSON><PERSON>raw<PERSON>, CrawlerRunConfig
from crawl4ai import JsonCssExtractionStrategy


class IntelligentContactExtractor:
    """
    Intelligent extractor that discovers available pages and prioritizes the best ones.
    Much faster and more accurate than checking all possible pages.
    """
    
    def __init__(self, batch_size: int = 50, max_concurrent: int = 6):
        """Initialize with intelligent page discovery."""
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        
        # Prioritized contact page patterns (most likely to have contact info first)
        self.contact_patterns = [
            ('/contact', 10),      # Highest priority
            ('/contact-us', 10),
            ('/contactus', 9),
            ('/about', 7),         # Medium priority
            ('/about-us', 7),
            ('/location', 6),      # Lower priority
            ('/locations', 6),
            ('/hours', 4),
            ('/info', 4)
        ]
    
    async def extract_intelligent(self, urls: Union[str, List[str]]) -> List[Dict]:
        """
        Intelligent extraction with smart page discovery.
        
        Args:
            urls: URLs to process
            
        Returns:
            List of results with emails and social media
        """
        if isinstance(urls, str):
            urls = [urls]
        
        print(f"🧠 INTELLIGENT CONTACT EXTRACTION")
        print(f"📊 Processing {len(urls)} URLs with smart page discovery")
        print(f"⚡ Max concurrent per batch: {self.max_concurrent}")
        
        all_results = []
        total_start = datetime.now()
        
        # Process URLs in batches
        for i in range(0, len(urls), self.batch_size):
            batch = urls[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            total_batches = (len(urls) + self.batch_size - 1) // self.batch_size
            
            print(f"\n📦 Batch {batch_num}/{total_batches}: {len(batch)} URLs")
            
            batch_start = datetime.now()
            batch_results = await self._process_batch_intelligent(batch)
            batch_duration = (datetime.now() - batch_start).total_seconds()
            
            all_results.extend(batch_results)
            
            print(f"   ✅ Completed in {batch_duration:.2f}s ({len(batch)/batch_duration:.2f} URLs/sec)")
            
            # Small delay between batches
            if i + self.batch_size < len(urls):
                await asyncio.sleep(1)
        
        total_duration = (datetime.now() - total_start).total_seconds()
        
        print(f"\n🎉 INTELLIGENT EXTRACTION COMPLETED!")
        print(f"   • Total URLs: {len(urls)}")
        print(f"   • Total time: {total_duration:.2f}s ({total_duration/60:.1f} min)")
        print(f"   • Overall rate: {len(urls)/total_duration:.2f} URLs/second")
        
        return all_results
    
    async def _process_batch_intelligent(self, urls: List[str]) -> List[Dict]:
        """Process a batch with intelligent page discovery."""
        
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_single(url: str) -> Dict:
            async with semaphore:
                return await self._extract_single_intelligent(url)
        
        tasks = [process_single(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append({
                    "url": urls[i],
                    "error": str(result),
                    "timestamp": datetime.now().isoformat()
                })
            else:
                final_results.append(result)
        
        return final_results
    
    async def _extract_single_intelligent(self, url: str) -> Dict:
        """Extract from single URL with intelligent page discovery."""
        
        try:
            # Step 1: Discover available pages quickly
            available_pages = await self._discover_available_pages(url)
            
            # Step 2: Score and prioritize pages
            prioritized_pages = self._prioritize_pages(available_pages)
            
            # Step 3: Extract from best pages (max 4 for better coverage)
            best_pages = prioritized_pages[:4]

            print(f"   🔍 {urlparse(url).netloc}: Found {len(available_pages)} pages, will check top {len(best_pages)}")
            for page_url, score in best_pages:
                print(f"     📄 Will check: {page_url} (score: {score})")

            best_email = None
            best_social = None
            pages_checked = 0

            async with AsyncWebCrawler(verbose=False) as crawler:
                for page_url, score in best_pages:
                    try:
                        print(f"     🔍 Checking page: {page_url}")

                        # Extract from this page
                        result = await asyncio.wait_for(
                            self._extract_from_page(crawler, page_url),
                            timeout=15.0
                        )

                        pages_checked += 1

                        # Update best contacts
                        if result.get('email') and not best_email:
                            best_email = result['email']
                            print(f"     📧 Found email on {page_url}: {best_email['email']}")

                        if result.get('social') and not best_social:
                            best_social = result['social']
                            print(f"     🌐 Found social on {page_url}: {best_social['platform']}")

                        # Continue checking all high-priority pages instead of early exit
                        # This ensures we find emails on contact pages

                    except asyncio.TimeoutError:
                        print(f"     ⏰ Timeout on {page_url}")
                        continue
                    except Exception as e:
                        print(f"     ❌ Error on {page_url}: {str(e)}")
                        continue
            
            return {
                "url": url,
                "timestamp": datetime.now().isoformat(),
                "email": best_email,
                "social_media": best_social,
                "pages_discovered": len(available_pages),
                "pages_checked": pages_checked,
                "success": True
            }
        
        except Exception as e:
            return {
                "url": url,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _discover_available_pages(self, base_url: str) -> List[Tuple[str, int]]:
        """Quickly discover which contact pages actually exist."""

        available_pages = [(base_url, 5)]  # Always include main page with base score

        # Quick HTTP HEAD requests to check if pages exist
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=8)) as session:
            tasks = []

            for pattern, priority in self.contact_patterns:
                contact_url = urljoin(base_url, pattern)
                if contact_url != base_url:
                    tasks.append(self._check_page_exists(session, contact_url, priority))

            # Check all pages concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            for result in results:
                if isinstance(result, tuple) and result[0]:  # (exists, url, priority)
                    available_pages.append((result[1], result[2]))
                    print(f"     ✅ Found contact page: {result[1]} (priority: {result[2]})")

        print(f"     📄 Total pages discovered: {len(available_pages)}")
        return available_pages
    
    async def _check_page_exists(self, session: aiohttp.ClientSession, url: str, priority: int) -> Tuple[bool, str, int]:
        """Check if a page exists using HEAD request, fallback to GET if needed."""
        try:
            # Try HEAD request first
            async with session.head(url, allow_redirects=True, ssl=False) as response:
                if response.status < 400:
                    print(f"     ✅ HEAD {url}: {response.status}")
                    return (True, url, priority)
                elif response.status == 403:
                    # 403 might mean HEAD is blocked, try GET request
                    print(f"     🔄 HEAD {url}: 403, trying GET...")
                    async with session.get(url, allow_redirects=True, ssl=False) as get_response:
                        exists = get_response.status < 400
                        print(f"     🔍 GET {url}: {get_response.status} ({'✅' if exists else '❌'})")
                        return (exists, url, priority)
                else:
                    print(f"     ❌ HEAD {url}: {response.status}")
                    return (False, url, priority)
        except Exception as e:
            print(f"     ❌ {url}: Error - {str(e)}")
            return (False, url, priority)
    
    def _prioritize_pages(self, available_pages: List[Tuple[str, int]]) -> List[Tuple[str, int]]:
        """Prioritize pages based on likelihood of having contact info."""
        
        scored_pages = []
        
        for page_url, base_priority in available_pages:
            score = base_priority
            
            # Boost score based on URL patterns
            url_lower = page_url.lower()
            
            if 'contact' in url_lower:
                score += 5
            if 'about' in url_lower:
                score += 3
            if 'location' in url_lower:
                score += 2
            
            # Penalize very long URLs (likely not main contact pages)
            if len(page_url) > 100:
                score -= 2
            
            scored_pages.append((page_url, score))
        
        # Sort by score (highest first)
        scored_pages.sort(key=lambda x: x[1], reverse=True)
        
        return scored_pages
    
    async def _extract_from_page(self, crawler: AsyncWebCrawler, url: str) -> Dict:
        """Extract contacts from a specific page."""
        
        # CSS extraction strategy
        css_schema = {
            "name": "Smart Contact",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "emails",
                    "selector": "a[href^='mailto:'], [data-email]",
                    "type": "list",
                    "fields": [{"name": "email", "type": "attribute", "attribute": "href"}]
                },
                {
                    "name": "social",
                    "selector": "a[href*='instagram.com'], a[href*='facebook.com'], a[href*='twitter.com']",
                    "type": "list",
                    "fields": [{"name": "url", "type": "attribute", "attribute": "href"}]
                }
            ]
        }
        
        strategy = JsonCssExtractionStrategy(css_schema, verbose=False)
        config = CrawlerRunConfig(extraction_strategy=strategy)
        
        result = await crawler.arun(url=url, config=config)
        
        best_email = None
        best_social = None
        
        # Process CSS results
        if result.success and result.extracted_content:
            try:
                data = json.loads(result.extracted_content)
                if data and len(data) > 0:
                    css_data = data[0]
                    
                    # Extract emails
                    emails = set()
                    for email_item in css_data.get('emails', []):
                        if isinstance(email_item, dict):
                            email = email_item.get('email', '').replace('mailto:', '').strip().lower()
                            if self._is_valid_email(email):
                                emails.add(email)
                    
                    if emails:
                        best_email = self._select_best_email(list(emails))
                    
                    # Extract social links
                    social_links = set()
                    for social_item in css_data.get('social', []):
                        if isinstance(social_item, dict):
                            social_url = social_item.get('url', '').strip()
                            if social_url and self._is_valid_social_url(social_url):
                                social_links.add(social_url)
                    
                    if social_links:
                        best_social = self._select_best_social(list(social_links))
            
            except json.JSONDecodeError:
                pass
        
        # Fallback: Regex extraction if CSS didn't work
        if (not best_email or not best_social) and hasattr(result, 'cleaned_html'):
            content = result.cleaned_html
            
            # Quick email regex
            if not best_email:
                email_matches = re.findall(r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b', content)
                valid_emails = [e.lower() for e in email_matches[:10] if self._is_valid_email(e)]
                if valid_emails:
                    best_email = self._select_best_email(valid_emails)
            
            # Quick social regex
            if not best_social:
                social_pattern = r'https?://(?:www\.)?(?:instagram|facebook|twitter)\.com/[^\s<>"\']+' 
                social_matches = re.findall(social_pattern, content, re.IGNORECASE)
                valid_socials = [s for s in social_matches[:5] if self._is_valid_social_url(s)]
                if valid_socials:
                    best_social = self._select_best_social(valid_socials)
        
        return {
            'email': best_email,
            'social': best_social
        }
    
    def _is_valid_email(self, email: str) -> bool:
        """Fast email validation."""
        if not email or len(email) < 5 or len(email) > 50:
            return False
        
        # Quick blacklist
        if any(ext in email for ext in ['.svg', '.png', '.jpg', '.jpeg', '.gif', '.css', '.js']):
            return False
        
        return bool(re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email))
    
    def _is_valid_social_url(self, url: str) -> bool:
        """Validate social media URL."""
        if not url or len(url) < 10:
            return False
        
        platforms = ['instagram.com', 'facebook.com', 'twitter.com', 'x.com']
        if not any(platform in url.lower() for platform in platforms):
            return False
        
        # Avoid post/photo URLs
        avoid_patterns = ['/p/', '/posts/', '/photo/', '/status/', '/tweet/']
        if any(pattern in url.lower() for pattern in avoid_patterns):
            return False
        
        return True
    
    def _select_best_email(self, emails: List[str]) -> Optional[Dict]:
        """Select best email."""
        if not emails:
            return None
        
        priority = ['contact', 'info', 'hello', 'admin', 'support']
        
        for prefix in priority:
            for email in emails:
                if email.startswith(f"{prefix}@"):
                    return {"email": email, "confidence": 0.95}
        
        best = min(emails, key=lambda x: (len(x), x.count('.')))
        return {"email": best, "confidence": 0.85}
    
    def _select_best_social(self, social_links: List[str]) -> Optional[Dict]:
        """Select best social media link."""
        if not social_links:
            return None
        
        priority = ['instagram.com', 'facebook.com', 'twitter.com']
        
        for platform in priority:
            for link in social_links:
                if platform in link.lower():
                    platform_name = platform.split('.')[0]
                    handle = self._extract_handle(link)
                    return {
                        "platform": platform_name,
                        "url": link,
                        "handle": handle,
                        "confidence": 0.90
                    }
        
        return None
    
    def _extract_handle(self, url: str) -> Optional[str]:
        """Extract handle from social URL."""
        try:
            parsed = urlparse(url)
            path_parts = [p for p in parsed.path.strip('/').split('/') if p]
            if path_parts:
                return f"@{path_parts[0]}"
        except:
            pass
        return None
    
    def export_to_csv(self, results: List[Dict], filename: str):
        """Export results to CSV."""
        if not results:
            return
        
        rows = []
        for result in results:
            if 'error' in result:
                continue
            
            email = result.get('email', {})
            social = result.get('social_media', {})
            
            row = {
                'url': result.get('url', ''),
                'email': email.get('email', '') if email else '',
                'email_confidence': email.get('confidence', '') if email else '',
                'social_platform': social.get('platform', '') if social else '',
                'social_handle': social.get('handle', '') if social else '',
                'social_url': social.get('url', '') if social else '',
                'social_confidence': social.get('confidence', '') if social else '',
                'pages_discovered': result.get('pages_discovered', 0),
                'pages_checked': result.get('pages_checked', 0),
                'timestamp': result.get('timestamp', '')
            }
            rows.append(row)
        
        if rows:
            fieldnames = list(rows[0].keys())
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(rows)
            print(f"📊 Results exported to {filename}")
    
    def print_summary(self, results: List[Dict]):
        """Print extraction summary."""
        total = len(results)
        successful = len([r for r in results if 'error' not in r])
        errors = total - successful
        
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        
        total_pages_discovered = sum(r.get('pages_discovered', 0) for r in results if 'error' not in r)
        total_pages_checked = sum(r.get('pages_checked', 0) for r in results if 'error' not in r)
        
        avg_discovered = total_pages_discovered / successful if successful > 0 else 0
        avg_checked = total_pages_checked / successful if successful > 0 else 0
        
        print(f"\n📊 INTELLIGENT EXTRACTION SUMMARY:")
        print(f"   • Total URLs: {total}")
        print(f"   • Successful: {successful}")
        print(f"   • Errors: {errors}")
        
        if successful > 0:
            print(f"   • Emails found: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)")
            print(f"   • Social media found: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)")
            print(f"   • Average pages discovered: {avg_discovered:.1f}")
            print(f"   • Average pages checked: {avg_checked:.1f}")
            print(f"   • Efficiency: {avg_checked/avg_discovered*100:.1f}% of discovered pages checked" if avg_discovered > 0 else "")


# Main function for intelligent extraction
async def extract_contacts_intelligent(urls: Union[str, List[str]], 
                                     batch_size: int = 50,
                                     max_concurrent: int = 6) -> List[Dict]:
    """
    Intelligent contact extraction with smart page discovery.
    
    Args:
        urls: URLs to process
        batch_size: URLs per batch
        max_concurrent: Concurrent requests per batch
        
    Returns:
        List of extraction results
    """
    extractor = IntelligentContactExtractor(batch_size=batch_size, max_concurrent=max_concurrent)
    return await extractor.extract_intelligent(urls)
