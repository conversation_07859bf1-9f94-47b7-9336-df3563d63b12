"""
Test the ultra-fast email and social media extractor
"""

import asyncio
import csv
from datetime import datetime
from typing import List
from fast_email_social_extractor import FastEmailSocialExtractor


def load_test_urls() -> List[str]:
    """Load URLs from test_data.csv"""
    urls = []
    try:
        with open("test_data.csv", 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                url = row.get('website_url', '').strip()
                if url and url.startswith(('http://', 'https://')):
                    urls.append(url)
        return urls
    except Exception as e:
        print(f"Error loading URLs: {e}")
        return []


async def test_speed_demo():
    """Quick speed test with a few URLs."""
    print("⚡ ULTRA-FAST EMAIL & SOCIAL EXTRACTOR - SPEED DEMO")
    print("=" * 60)
    print("Focus: ONLY emails and social media")
    print("Strategy: Maximum speed, minimal pages checked")
    print("=" * 60)
    
    # Test with 5 URLs for quick demo
    test_urls = [
        "https://thejamescafe.com/",
        "https://convivecoffee.com/",
        "https://www.defer.coffee/strip-district",
        "https://www.novariacoffee.com/",
        "https://redstartroasters.com/"
    ]
    
    print(f"🚀 Testing {len(test_urls)} URLs...")
    
    start_time = datetime.now()
    
    extractor = FastEmailSocialExtractor(max_concurrent=10)
    results = await extractor.extract_fast(test_urls)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print(f"\n⚡ SPEED RESULTS:")
    print(f"   • Processing time: {duration:.2f} seconds")
    print(f"   • Rate: {len(test_urls)/duration:.2f} URLs/second")
    print(f"   • Average per URL: {duration/len(test_urls):.2f} seconds")
    
    print(f"\n📧 EXTRACTION RESULTS:")
    
    for i, result in enumerate(results, 1):
        if 'error' in result:
            print(f"{i}. ❌ {result['url']}: Error")
            continue
        
        url = result['url']
        email = result.get('email')
        social = result.get('social_media')
        pages = result.get('pages_processed', 0)
        
        domain = url.split('/')[2].replace('www.', '')
        
        print(f"\n{i}. 🏪 {domain} ({pages} pages)")
        
        if email:
            print(f"   📧 {email['email']} (confidence: {email['confidence']:.1%})")
        else:
            print(f"   📧 No email found")
        
        if social:
            print(f"   🌐 {social['platform'].title()}: {social.get('handle', social['url'])}")
        else:
            print(f"   🌐 No social media found")
    
    # Summary
    extractor.print_summary(results)
    
    # Export
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    extractor.export_to_csv(results, f"fast_demo_{timestamp}.csv")
    
    return results


async def test_full_speed():
    """Test with all URLs for full speed measurement."""
    print("\n🚀 FULL SPEED TEST - ALL URLS")
    print("=" * 60)
    
    urls = load_test_urls()
    if not urls:
        print("No URLs found")
        return
    
    print(f"🎯 Processing ALL {len(urls)} URLs at maximum speed...")
    print(f"⚡ High concurrency enabled")
    
    start_time = datetime.now()
    
    # Maximum speed settings
    extractor = FastEmailSocialExtractor(max_concurrent=20)  # High concurrency
    results = await extractor.extract_fast(urls)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print(f"\n🎉 FULL SPEED TEST COMPLETED!")
    print(f"   • Total URLs: {len(urls)}")
    print(f"   • Processing time: {duration:.2f} seconds ({duration/60:.1f} minutes)")
    print(f"   • Processing rate: {len(urls)/duration:.2f} URLs/second")
    print(f"   • Average per URL: {duration/len(urls):.2f} seconds")
    
    # Detailed analysis
    extractor.print_summary(results)
    
    # Show some successful extractions
    successful_results = [r for r in results if 'error' not in r and (r.get('email') or r.get('social_media'))]
    
    print(f"\n🏆 SAMPLE SUCCESSFUL EXTRACTIONS:")
    for result in successful_results[:10]:  # Show first 10
        url = result['url']
        email = result.get('email')
        social = result.get('social_media')
        
        domain = url.split('/')[2].replace('www.', '')
        
        email_text = email['email'] if email else "None"
        social_text = f"{social['platform']} {social.get('handle', '')}" if social else "None"
        
        print(f"   • {domain}: 📧 {email_text} | 🌐 {social_text}")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    extractor.export_to_csv(results, f"fast_full_speed_{timestamp}.csv")
    
    # Scaling projections
    print(f"\n📈 SCALING PROJECTIONS:")
    rate = len(urls) / duration
    print(f"   • 100 URLs: ~{100/rate:.1f} seconds")
    print(f"   • 1,000 URLs: ~{1000/rate/60:.1f} minutes")
    print(f"   • 10,000 URLs: ~{10000/rate/60:.1f} minutes")
    
    return results


async def test_accuracy_check():
    """Quick accuracy check on known good URLs."""
    print("\n🎯 ACCURACY CHECK")
    print("=" * 60)
    
    # URLs we know should have good contact info
    known_good_urls = [
        "https://thejamescafe.com/",  # <NAME_EMAIL>
        "https://convivecoffee.com/",  # <NAME_EMAIL>
        "http://www.yinzcoffee.com/",  # <NAME_EMAIL>
        "http://www.grimwizard.com/",  # <NAME_EMAIL>
    ]
    
    print(f"🔍 Checking {len(known_good_urls)} URLs with known contact info...")
    
    extractor = FastEmailSocialExtractor(max_concurrent=5)
    results = await extractor.extract_fast(known_good_urls)
    
    print(f"\n✅ ACCURACY RESULTS:")
    
    for i, result in enumerate(results, 1):
        if 'error' in result:
            print(f"{i}. ❌ ERROR: {result['url']}")
            continue
        
        url = result['url']
        email = result.get('email')
        social = result.get('social_media')
        pages = result.get('pages_processed', 0)
        
        domain = url.split('/')[2].replace('www.', '')
        
        print(f"\n{i}. 🏪 {domain}")
        print(f"   🔗 {url}")
        print(f"   📄 Pages processed: {pages}")
        
        if email:
            print(f"   📧 Email: {email['email']} (confidence: {email['confidence']:.1%})")
        else:
            print(f"   📧 Email: ❌ Not found")
        
        if social:
            print(f"   🌐 Social: {social['platform'].title()} {social.get('handle', '')} (confidence: {social['confidence']:.1%})")
        else:
            print(f"   🌐 Social: ❌ Not found")
    
    # Quick summary
    successful = len([r for r in results if 'error' not in r])
    emails_found = len([r for r in results if 'error' not in r and r.get('email')])
    socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
    
    print(f"\n📊 ACCURACY SUMMARY:")
    print(f"   • URLs tested: {len(known_good_urls)}")
    print(f"   • Successful: {successful}")
    print(f"   • Emails found: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)" if successful > 0 else "")
    print(f"   • Social found: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)" if successful > 0 else "")
    
    return results


async def main():
    """Main test function."""
    print("⚡ ULTRA-FAST EMAIL & SOCIAL EXTRACTOR TESTS")
    print("Optimized for maximum speed and throughput")
    print("=" * 60)
    
    print("\nChoose test mode:")
    print("1. Speed demo (5 URLs)")
    print("2. Full speed test (all URLs)")
    print("3. Accuracy check (4 known URLs)")
    print("4. All tests")
    
    try:
        choice = input("\nEnter choice (1-4) or press Enter for option 1: ").strip()
        if not choice:
            choice = "1"
    except KeyboardInterrupt:
        print("\n\nTest cancelled.")
        return
    
    try:
        if choice == "1":
            await test_speed_demo()
        elif choice == "2":
            await test_full_speed()
        elif choice == "3":
            await test_accuracy_check()
        elif choice == "4":
            await test_speed_demo()
            await test_full_speed()
            await test_accuracy_check()
        else:
            print("Invalid choice. Running speed demo.")
            await test_speed_demo()
    
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🎉 Ultra-fast extraction testing completed!")
    print("\nFinal optimizations:")
    print("✅ Only extracts emails and social media (no phones/addresses)")
    print("✅ Maximum 2 pages per URL (main + contact)")
    print("✅ 10-second timeout per page")
    print("✅ High concurrency (15-20 simultaneous requests)")
    print("✅ Early exit when both email and social found")
    print("✅ Streamlined validation and processing")
    print("✅ Ready for thousands of URLs!")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚡ Fast extraction test interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
