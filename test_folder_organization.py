"""
Test the organized folder structure
"""

import tkinter as tk
from chunk_ui import ChunkScraper<PERSON>
from pathlib import Path

def test_folder_organization():
    print('🧪 Testing organized folder structure...')
    
    try:
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        app = ChunkScraperUI(root)
        
        # Test folder creation
        test_folder = app.create_chunk_folder('test_chunk_001_of_006.csv')
        
        print(f'✅ Folder created: {test_folder}')
        print(f'✅ Mini-chunks folder: {test_folder / "mini_chunks"}')
        print(f'✅ Combined results folder: {test_folder / "combined_results"}')
        
        # Check if folders exist
        if (test_folder / 'mini_chunks').exists():
            print('✅ Mini-chunks subfolder exists')
        if (test_folder / 'combined_results').exists():
            print('✅ Combined results subfolder exists')
        
        # Test the folder structure
        print('\n📁 Created folder structure:')
        print(f'chunk_ui_results/')
        print(f'└── test_chunk_001_of_006/')
        print(f'    ├── mini_chunks/')
        print(f'    └── combined_results/')
        
        root.destroy()
        print('\n✅ Folder organization test passed!')
        return True
        
    except Exception as e:
        print(f'❌ Test failed: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_folder_organization()
    if success:
        print('\n🎉 Organized folder structure is working!')
    else:
        print('\n❌ Folder organization needs fixing')
