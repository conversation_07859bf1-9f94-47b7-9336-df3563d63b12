name: deploy_backend

on:
  push:
    tags:
      - "v*.*.*"

jobs:
  get-tag:
    runs-on: ubuntu-latest
    outputs:
      GITHUB_TAG: ${{ steps.vars.outputs.GITHUB_TAG }}
    steps:
      - uses: actions/checkout@master
      - name: Set GITHUB_TAG arg
        id: vars
        run: echo ::set-output name=GITHUB_TAG::${GITHUB_REF:10} # Remove /refs/head/

  docker-publish:
    runs-on: ubuntu-latest
    needs: get-tag
    steps:
      - uses: actions/checkout@master
      - name: Print version
        run: echo "Publishing reacherhq/backend:${{ needs.get-tag.outputs.GITHUB_TAG }}"
      - name: Publish to Docker Hub
        uses: elgohr/Publish-Docker-Github-Action@v5
        with:
          name: reacherhq/backend
          dockerfile: backend/Dockerfile
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
          tags: "${{ needs.get-tag.outputs.GITHUB_TAG }},beta"
