"""
Smart Contact Extractor - Identifies contact pages first, then extracts from the best sources
Designed for high-volume processing of thousands of URLs efficiently.
"""

import asyncio
import json
import re
import csv
from datetime import datetime
from typing import Dict, List, Optional, Union, Tuple
from urllib.parse import urljoin, urlparse
import aiohttp

from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai import JsonCssExtractionStrategy, RegexExtractionStrategy


class SmartContactExtractor:
    """
    Smart contact extractor that:
    1. Identifies which pages likely have contact info
    2. Focuses extraction on those high-value pages
    3. Processes thousands of URLs efficiently
    """
    
    def __init__(self, max_concurrent: int = 10):
        """Initialize with concurrency control."""
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        # Prioritized contact page patterns (most likely to have contact info first)
        self.contact_page_patterns = [
            '/contact', '/contact-us', '/contactus',  # High priority
            '/about', '/about-us',                    # Medium priority
            '/location', '/locations',                # Medium priority
            '/hours', '/info', '/visit'               # Lower priority
        ]
    
    async def extract_contacts_smart(self, urls: Union[str, List[str]]) -> List[Dict]:
        """
        Smart extraction process:
        1. For each URL, find the best contact pages
        2. Extract from those pages specifically
        3. Return the best single contact of each type
        """
        if isinstance(urls, str):
            urls = [urls]
        
        print(f"🎯 Smart Contact Extraction - Processing {len(urls)} URLs")
        print(f"⚡ Max concurrent requests: {self.max_concurrent}")
        
        results = []
        
        # Process URLs with concurrency control
        tasks = [self._process_single_url(url) for url in urls]
        completed_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(completed_results):
            if isinstance(result, Exception):
                results.append({
                    "url": urls[i],
                    "error": str(result),
                    "timestamp": datetime.now().isoformat()
                })
            else:
                results.append(result)
        
        return results
    
    async def _process_single_url(self, url: str) -> Dict:
        """Process a single URL with smart page discovery."""
        async with self.semaphore:  # Limit concurrency
            try:
                print(f"🔍 Analyzing: {url}")
                
                # Step 1: Discover potential contact pages
                contact_pages = await self._discover_contact_pages(url)
                
                # Step 2: Extract from the best pages
                best_contacts = await self._extract_from_best_pages(url, contact_pages)
                
                return {
                    "url": url,
                    "timestamp": datetime.now().isoformat(),
                    "contacts": best_contacts,
                    "metadata": {
                        "pages_checked": len(contact_pages),
                        "contact_pages_found": [p for p in contact_pages if p['has_contact_info']],
                        "extraction_strategy": "smart_multi_page"
                    }
                }
                
            except Exception as e:
                print(f"❌ Error processing {url}: {str(e)}")
                return {
                    "url": url,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
    
    async def _discover_contact_pages(self, base_url: str) -> List[Dict]:
        """Discover which pages likely contain contact information."""
        
        # Generate potential contact page URLs
        potential_urls = [base_url]  # Always include main page
        
        for pattern in self.contact_page_patterns:
            potential_url = urljoin(base_url, pattern)
            potential_urls.append(potential_url)
        
        contact_pages = []
        
        async with AsyncWebCrawler(verbose=False) as crawler:
            # Check each potential URL
            for url in potential_urls:
                try:
                    result = await crawler.arun(url=url, bypass_cache=True)
                    
                    if result.success and hasattr(result, 'cleaned_html'):
                        # Analyze if this page likely has contact info
                        contact_score = self._score_contact_page(result.cleaned_html, url)
                        
                        contact_pages.append({
                            'url': url,
                            'score': contact_score,
                            'has_contact_info': contact_score > 3,
                            'content': result.cleaned_html,
                            'title': getattr(result, 'title', '')
                        })
                        
                        print(f"   📄 {url}: score {contact_score}")
                    
                except Exception as e:
                    # Page doesn't exist or failed to load
                    continue
        
        # Sort by contact score (highest first)
        contact_pages.sort(key=lambda x: x['score'], reverse=True)
        return contact_pages
    
    def _score_contact_page(self, content: str, url: str) -> int:
        """Score how likely a page is to contain contact information."""
        score = 0
        content_lower = content.lower()
        
        # URL-based scoring
        if any(pattern in url.lower() for pattern in ['/contact', '/about', '/location']):
            score += 5
        
        # Content-based scoring
        contact_keywords = [
            ('contact us', 3), ('get in touch', 3), ('reach us', 3),
            ('phone', 2), ('email', 2), ('address', 2),
            ('call us', 2), ('visit us', 2), ('find us', 2),
            ('hours', 1), ('location', 1), ('directions', 1)
        ]
        
        for keyword, points in contact_keywords:
            if keyword in content_lower:
                score += points
        
        # Look for contact patterns
        if re.search(r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b', content):
            score += 4  # Has email
        
        if re.search(r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}', content):
            score += 4  # Has phone
        
        if re.search(r'\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd)', content):
            score += 3  # Has address
        
        return score
    
    async def _extract_from_best_pages(self, base_url: str, contact_pages: List[Dict]) -> Dict:
        """Extract contact information from the highest-scoring pages with early stopping."""

        # Take top pages with contact info, but limit to avoid over-processing
        best_pages = [p for p in contact_pages if p['has_contact_info']][:5]

        if not best_pages:
            # Fallback to main page if no contact pages found
            best_pages = contact_pages[:1] if contact_pages else []

        all_contacts = {
            'emails': set(),
            'phones': set(),
            'addresses': set(),
            'social_media': set()
        }

        # Track what we've found for early stopping
        found_email = False
        found_phone = False
        found_address = False
        found_social = False
        
        # Extract from each high-value page with early stopping
        async with AsyncWebCrawler(verbose=False) as crawler:
            for i, page in enumerate(best_pages):
                try:
                    # Enhanced extraction for this specific page
                    page_contacts = await self._extract_from_page(crawler, page)

                    # Merge results
                    if page_contacts.get('emails'):
                        all_contacts['emails'].update(page_contacts['emails'])
                        found_email = True
                    if page_contacts.get('phones'):
                        all_contacts['phones'].update(page_contacts['phones'])
                        found_phone = True
                    if page_contacts.get('addresses'):
                        all_contacts['addresses'].update(page_contacts['addresses'])
                        found_address = True
                    if page_contacts.get('social_media'):
                        all_contacts['social_media'].update(page_contacts['social_media'])
                        found_social = True

                    # Early stopping: if we found good contact info and processed at least 2 pages, stop
                    if i >= 1 and found_email and found_phone and found_social:
                        print(f"   ⚡ Early stop: Found complete contact info after {i+1} pages")
                        break

                except Exception as e:
                    print(f"   ⚠️ Failed to extract from {page['url']}: {str(e)}")
                    continue
        
        # Select the best single contact of each type
        return {
            'email': self._select_best_email(list(all_contacts['emails'])),
            'phone': self._select_best_phone(list(all_contacts['phones'])),
            'address': self._select_best_address(list(all_contacts['addresses'])),
            'social_media': self._select_best_social(list(all_contacts['social_media']))
        }
    
    async def _extract_from_page(self, crawler: AsyncWebCrawler, page: Dict) -> Dict:
        """Extract contacts from a specific page using enhanced strategies."""
        
        url = page['url']
        content = page['content']
        
        contacts = {
            'emails': set(),
            'phones': set(),
            'addresses': set(),
            'social_media': set()
        }
        
        # Strategy 1: Enhanced CSS extraction
        css_schema = {
            "name": "Enhanced Contact Extraction",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "emails",
                    "selector": "a[href^='mailto:'], .email, .contact-email, [data-email]",
                    "type": "list",
                    "fields": [{"name": "email", "type": "attribute", "attribute": "href"}]
                },
                {
                    "name": "phones", 
                    "selector": "a[href^='tel:'], .phone, .contact-phone, .telephone",
                    "type": "list",
                    "fields": [{"name": "phone", "type": "attribute", "attribute": "href"}]
                },
                {
                    "name": "addresses",
                    "selector": ".address, .location, [itemtype*='PostalAddress'], .street-address, .contact-address",
                    "type": "list",
                    "fields": [{"name": "address", "type": "text"}]
                },
                {
                    "name": "social",
                    "selector": "a[href*='facebook.com'], a[href*='instagram.com'], a[href*='twitter.com'], a[href*='linkedin.com']",
                    "type": "list", 
                    "fields": [{"name": "url", "type": "attribute", "attribute": "href"}]
                }
            ]
        }
        
        try:
            strategy = JsonCssExtractionStrategy(css_schema, verbose=False)
            config = CrawlerRunConfig(extraction_strategy=strategy)
            result = await crawler.arun(url=url, config=config)
            
            if result.success and result.extracted_content:
                data = json.loads(result.extracted_content)
                if data:
                    css_data = data[0]
                    
                    # Process CSS results
                    for email_item in css_data.get('emails', []):
                        if isinstance(email_item, dict):
                            email = email_item.get('email', '').replace('mailto:', '')
                            if self._is_valid_email(email):
                                contacts['emails'].add(email.lower())
                    
                    for phone_item in css_data.get('phones', []):
                        if isinstance(phone_item, dict):
                            phone = phone_item.get('phone', '').replace('tel:', '')
                            if self._is_valid_phone(phone):
                                contacts['phones'].add(self._normalize_phone(phone))
                    
                    for addr_item in css_data.get('addresses', []):
                        if isinstance(addr_item, dict):
                            address = addr_item.get('address', '').strip()
                            if self._is_valid_address(address):
                                contacts['addresses'].add(address)
                    
                    for social_item in css_data.get('social', []):
                        if isinstance(social_item, dict):
                            social_url = social_item.get('url', '')
                            if social_url:
                                contacts['social_media'].add(social_url)
        
        except Exception as e:
            print(f"   CSS extraction failed: {str(e)}")
        
        # Strategy 2: Regex extraction from content
        try:
            # Email regex
            email_pattern = r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b'
            emails = re.findall(email_pattern, content)
            for email in emails:
                if self._is_valid_email(email):
                    contacts['emails'].add(email.lower())
            
            # Enhanced phone regex with better patterns
            phone_patterns = [
                r'(?:phone|tel|telephone|call)[\s:]*(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})',  # After phone keywords
                r'(\d{3}[-.\s]\d{3}[-.\s]\d{4})',  # ************
                r'(\(\d{3}\)\s?\d{3}[-.\s]\d{4})',  # (*************
                r'(\+?1[-.\s]?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})',  # +1 formats
                r'(\d{3}\.\d{3}\.\d{4})',  # ************
            ]

            for pattern in phone_patterns:
                phones = re.findall(pattern, content, re.IGNORECASE)
                for phone in phones:
                    clean_phone = phone.strip()
                    if self._is_valid_phone(clean_phone):
                        normalized = self._normalize_phone(clean_phone)
                        if normalized:
                            contacts['phones'].add(normalized)
            
            # Social media regex
            social_patterns = [
                r'https?://(?:www\.)?(?:facebook|instagram|twitter|linkedin)\.com/[^\s<>"\']+',
            ]
            for pattern in social_patterns:
                social_links = re.findall(pattern, content)
                for link in social_links:
                    contacts['social_media'].add(link)
        
        except Exception as e:
            print(f"   Regex extraction failed: {str(e)}")
        
        return {k: list(v) for k, v in contacts.items()}
    
    def _normalize_phone(self, phone: str) -> Optional[str]:
        """Normalize phone number to consistent format with better validation."""
        if not phone:
            return None

        # Remove all non-digits
        digits = re.sub(r'\D', '', phone)

        # Skip if too few or too many digits
        if len(digits) < 7 or len(digits) > 15:
            return None

        # Skip obvious false positives (coordinates, etc.)
        if '.' in phone and len(digits) < 10:
            return None

        # Handle US numbers
        if len(digits) == 10:
            # Validate US area code (first digit 2-9)
            if digits[0] in '23456789':
                return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == '1':
            # US number with country code
            if digits[1] in '23456789':
                return f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        elif 7 <= len(digits) <= 10:
            # Other formats, return as-is but cleaned
            return phone.strip()

        return None
    
    def _is_valid_email(self, email: str) -> bool:
        """Validate email address."""
        if not email or len(email) < 5:
            return False
        
        # Filter out image files and false positives
        if re.search(r'\.(svg|png|jpg|jpeg|gif|webp|ico|css|js)$', email, re.IGNORECASE):
            return False
        
        # Basic email validation
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    def _is_valid_phone(self, phone: str) -> bool:
        """Enhanced phone number validation."""
        if not phone:
            return False

        # Remove whitespace
        phone = phone.strip()

        # Check for obvious false positives
        false_positive_patterns = [
            r'^\d{1,2}\.\d+$',  # Coordinates like 40.123456
            r'^\d+\.\d{6,}$',   # Long decimals
            r'^\d{13,}$',       # Very long numbers (likely IDs)
            r'^[0-9]{1,6}$',    # Too short
            r'^\d{4}-\d{2}-\d{2}$',  # Dates
            r'^\d{1,3}:\d{2}$',      # Times
        ]

        for pattern in false_positive_patterns:
            if re.match(pattern, phone):
                return False

        digits = re.sub(r'\D', '', phone)

        # Must have reasonable number of digits
        if not (7 <= len(digits) <= 15):
            return False

        # For US numbers, first digit should be 2-9
        if len(digits) in [10, 11]:
            first_digit = digits[0] if len(digits) == 10 else digits[1]
            if first_digit not in '23456789':
                return False

        return True
    
    def _is_valid_address(self, address: str) -> bool:
        """Validate address."""
        if not address or len(address.strip()) < 15:
            return False
        
        # Must contain address keywords and numbers
        address_keywords = ['street', 'st', 'avenue', 'ave', 'road', 'rd', 'boulevard', 'blvd', 'lane', 'ln', 'drive', 'dr']
        has_keyword = any(keyword in address.lower() for keyword in address_keywords)
        has_numbers = bool(re.search(r'\d+', address))
        
        return has_keyword and has_numbers
    
    def _select_best_email(self, emails: List[str]) -> Optional[Dict]:
        """Select the best email from candidates."""
        if not emails:
            return None
        
        # Prefer common business email patterns
        preferred_prefixes = ['contact', 'info', 'hello', 'support', 'admin']
        
        for prefix in preferred_prefixes:
            for email in emails:
                if email.startswith(f"{prefix}@"):
                    return {"email": email, "confidence": 0.95}
        
        # Return the shortest, most professional looking email
        best_email = min(emails, key=lambda x: (len(x), x.count('.')))
        return {"email": best_email, "confidence": 0.85}
    
    def _select_best_phone(self, phones: List[str]) -> Optional[Dict]:
        """Select the best phone from candidates."""
        if not phones:
            return None
        
        # Prefer properly formatted phones
        for phone in phones:
            if re.match(r'^\(\d{3}\) \d{3}-\d{4}$', phone):
                return {"phone": phone, "confidence": 0.95}
        
        return {"phone": phones[0], "confidence": 0.85}
    
    def _select_best_address(self, addresses: List[str]) -> Optional[Dict]:
        """Select the best address from candidates."""
        if not addresses:
            return None
        
        # Prefer addresses with zip codes
        for address in addresses:
            if re.search(r'\d{5}(-\d{4})?', address):
                return {"address": address.strip(), "confidence": 0.90}
        
        # Return the most complete looking address
        best_address = max(addresses, key=len)
        return {"address": best_address.strip(), "confidence": 0.75}
    
    def _select_best_social(self, social_links: List[str]) -> Optional[Dict]:
        """Select the best social media link."""
        if not social_links:
            return None
        
        # Prefer Instagram, then Facebook, then others
        platform_priority = ['instagram.com', 'facebook.com', 'twitter.com', 'linkedin.com']
        
        for platform in platform_priority:
            for link in social_links:
                if platform in link.lower():
                    platform_name = platform.split('.')[0]
                    handle = self._extract_social_handle(link)
                    return {
                        "platform": platform_name,
                        "url": link,
                        "handle": handle,
                        "confidence": 0.90
                    }
        
        return None
    
    def _extract_social_handle(self, url: str) -> Optional[str]:
        """Extract social media handle from URL."""
        try:
            parsed = urlparse(url)
            path_parts = [p for p in parsed.path.strip('/').split('/') if p]
            if path_parts and not path_parts[0] in ['p', 'posts', 'status', 'tweet']:
                return f"@{path_parts[0]}"
        except:
            pass
        return None
    
    def export_to_csv(self, results: List[Dict], filename: str):
        """Export results to CSV format."""
        if not results:
            return
        
        flattened_data = []
        for result in results:
            if 'error' in result:
                continue
            
            contacts = result.get('contacts', {})
            metadata = result.get('metadata', {})
            
            row = {
                'url': result.get('url', ''),
                'timestamp': result.get('timestamp', ''),
                'email': contacts.get('email', {}).get('email', '') if contacts.get('email') else '',
                'email_confidence': contacts.get('email', {}).get('confidence', '') if contacts.get('email') else '',
                'phone': contacts.get('phone', {}).get('phone', '') if contacts.get('phone') else '',
                'phone_confidence': contacts.get('phone', {}).get('confidence', '') if contacts.get('phone') else '',
                'address': contacts.get('address', {}).get('address', '') if contacts.get('address') else '',
                'address_confidence': contacts.get('address', {}).get('confidence', '') if contacts.get('address') else '',
                'social_platform': contacts.get('social_media', {}).get('platform', '') if contacts.get('social_media') else '',
                'social_handle': contacts.get('social_media', {}).get('handle', '') if contacts.get('social_media') else '',
                'social_url': contacts.get('social_media', {}).get('url', '') if contacts.get('social_media') else '',
                'pages_checked': metadata.get('pages_checked', 0),
                'contact_pages_found': len(metadata.get('contact_pages_found', []))
            }
            flattened_data.append(row)
        
        if flattened_data:
            fieldnames = list(flattened_data[0].keys())
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(flattened_data)
            print(f"📊 Results exported to {filename}")


# Convenience function for bulk processing
async def process_urls_smart(urls: Union[str, List[str]], max_concurrent: int = 10) -> List[Dict]:
    """
    Process URLs with smart contact page discovery.
    
    Args:
        urls: URLs to process
        max_concurrent: Maximum concurrent requests
        
    Returns:
        List of extraction results
    """
    extractor = SmartContactExtractor(max_concurrent=max_concurrent)
    return await extractor.extract_contacts_smart(urls)
