"""
Ultra-Fast Email & Social Media Extractor
Optimized for processing thousands of URLs at maximum speed.
Returns only the best email and social media link per URL.
"""

import asyncio
import json
import re
import csv
from datetime import datetime
from typing import Dict, List, Optional, Union
from urllib.parse import urljoin, urlparse

from crawl4ai import Async<PERSON>ebCrawler, CrawlerRunConfig
from crawl4ai import JsonCssExtractionStrategy, RegexExtractionStrategy


class FastEmailSocialExtractor:
    """
    Ultra-fast extractor focused only on emails and social media.
    Optimized for maximum speed and throughput.
    """
    
    def __init__(self, max_concurrent: int = 8):
        """Initialize with optimized concurrency for stability."""
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
        # Only check the most likely contact pages for speed
        self.priority_pages = ['/contact', '/about']
    
    async def extract_fast(self, urls: Union[str, List[str]]) -> List[Dict]:
        """
        Ultra-fast extraction of emails and social media only.
        
        Args:
            urls: URLs to process
            
        Returns:
            List of results with best email and social media per URL
        """
        if isinstance(urls, str):
            urls = [urls]
        
        print(f"⚡ FAST EMAIL & SOCIAL EXTRACTION")
        print(f"🎯 Processing {len(urls)} URLs (max concurrent: {self.max_concurrent})")
        
        start_time = datetime.now()
        
        # Process all URLs concurrently
        tasks = [self._process_url_fast(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append({
                    "url": urls[i],
                    "error": str(result),
                    "timestamp": datetime.now().isoformat()
                })
            else:
                final_results.append(result)
        
        duration = (datetime.now() - start_time).total_seconds()
        
        print(f"⚡ Completed {len(urls)} URLs in {duration:.2f}s ({len(urls)/duration:.2f} URLs/sec)")
        
        return final_results
    
    async def _process_url_fast(self, url: str) -> Dict:
        """Process single URL with maximum speed optimization."""
        async with self.semaphore:
            try:
                # Strategy: Check main page + contact page only (max 2 pages)
                pages_to_check = [url]
                
                # Add contact page if it exists (quick check)
                contact_url = urljoin(url, '/contact')
                if contact_url != url:
                    pages_to_check.append(contact_url)
                
                best_email = None
                best_social = None
                pages_processed = 0
                
                async with AsyncWebCrawler(verbose=False) as crawler:
                    for page_url in pages_to_check:
                        try:
                            # Quick extraction with timeout
                            result = await asyncio.wait_for(
                                self._extract_from_page_fast(crawler, page_url),
                                timeout=15.0  # 15 second timeout per page
                            )
                            
                            pages_processed += 1
                            
                            # Update best contacts
                            if result.get('email') and not best_email:
                                best_email = result['email']
                            
                            if result.get('social') and not best_social:
                                best_social = result['social']
                            
                            # Early exit if we found both
                            if best_email and best_social:
                                break
                                
                        except asyncio.TimeoutError:
                            print(f"   ⏰ Timeout on {page_url}")
                            continue
                        except Exception:
                            continue
                
                return {
                    "url": url,
                    "timestamp": datetime.now().isoformat(),
                    "email": best_email,
                    "social_media": best_social,
                    "pages_processed": pages_processed
                }
                
            except Exception as e:
                return {
                    "url": url,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
    
    async def _extract_from_page_fast(self, crawler: AsyncWebCrawler, url: str) -> Dict:
        """Ultra-fast extraction from a single page."""
        
        # Single strategy: CSS + Regex combined for speed
        css_schema = {
            "name": "Fast Email Social",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "emails",
                    "selector": "a[href^='mailto:'], [data-email]",
                    "type": "list",
                    "fields": [{"name": "email", "type": "attribute", "attribute": "href"}]
                },
                {
                    "name": "social",
                    "selector": "a[href*='instagram.com'], a[href*='facebook.com'], a[href*='twitter.com']",
                    "type": "list",
                    "fields": [{"name": "url", "type": "attribute", "attribute": "href"}]
                }
            ]
        }
        
        strategy = JsonCssExtractionStrategy(css_schema, verbose=False)
        config = CrawlerRunConfig(extraction_strategy=strategy)
        
        result = await crawler.arun(url=url, config=config)
        
        emails = set()
        social_links = set()
        
        # Process CSS results
        if result.success and result.extracted_content:
            try:
                data = json.loads(result.extracted_content)
                if data and len(data) > 0:
                    css_data = data[0]
                    
                    # Extract emails
                    for email_item in css_data.get('emails', []):
                        if isinstance(email_item, dict):
                            email = email_item.get('email', '').replace('mailto:', '').strip().lower()
                            if self._is_valid_email(email):
                                emails.add(email)
                    
                    # Extract social links
                    for social_item in css_data.get('social', []):
                        if isinstance(social_item, dict):
                            social_url = social_item.get('url', '').strip()
                            if social_url:
                                social_links.add(social_url)
            except:
                pass
        
        # Fallback: Regex extraction from content if CSS didn't find much
        if (not emails or not social_links) and hasattr(result, 'cleaned_html'):
            content = result.cleaned_html
            
            # Quick email regex
            if not emails:
                email_matches = re.findall(r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b', content)
                for email in email_matches[:5]:  # Limit to first 5 for speed
                    if self._is_valid_email(email):
                        emails.add(email.lower())
            
            # Quick social regex
            if not social_links:
                social_patterns = [
                    r'https?://(?:www\.)?instagram\.com/[^\s<>"\']+',
                    r'https?://(?:www\.)?facebook\.com/[^\s<>"\']+',
                    r'https?://(?:www\.)?twitter\.com/[^\s<>"\']+',
                ]
                for pattern in social_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches[:3]:  # Limit for speed
                        social_links.add(match)
        
        # Select best email and social
        best_email = self._select_best_email(list(emails)) if emails else None
        best_social = self._select_best_social(list(social_links)) if social_links else None
        
        return {
            'email': best_email,
            'social': best_social
        }
    
    def _is_valid_email(self, email: str) -> bool:
        """Fast email validation."""
        if not email or len(email) < 5 or len(email) > 50:
            return False
        
        # Quick blacklist check
        if any(ext in email for ext in ['.svg', '.png', '.jpg', '.jpeg', '.gif', '.css', '.js']):
            return False
        
        # Fast regex check
        return bool(re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email))
    
    def _select_best_email(self, emails: List[str]) -> Optional[Dict]:
        """Select the best email quickly."""
        if not emails:
            return None
        
        # Priority order for business emails
        priority_prefixes = ['contact', 'info', 'hello', 'admin', 'support']
        
        # Check for priority emails first
        for prefix in priority_prefixes:
            for email in emails:
                if email.startswith(f"{prefix}@"):
                    return {"email": email, "confidence": 0.95}
        
        # Return shortest professional-looking email
        best = min(emails, key=lambda x: (len(x), x.count('.')))
        return {"email": best, "confidence": 0.85}
    
    def _select_best_social(self, social_links: List[str]) -> Optional[Dict]:
        """Select the best social media link quickly."""
        if not social_links:
            return None
        
        # Platform priority: Instagram > Facebook > Twitter
        platform_priority = ['instagram.com', 'facebook.com', 'twitter.com']
        
        for platform in platform_priority:
            for link in social_links:
                if platform in link.lower():
                    # Avoid post/photo URLs, prefer profile URLs
                    if not any(x in link.lower() for x in ['/p/', '/posts/', '/photo/', '/status/']):
                        platform_name = platform.split('.')[0]
                        handle = self._extract_handle(link)
                        return {
                            "platform": platform_name,
                            "url": link,
                            "handle": handle,
                            "confidence": 0.90
                        }
        
        # Fallback to first link
        link = social_links[0]
        platform = self._identify_platform(link)
        return {
            "platform": platform or "unknown",
            "url": link,
            "handle": self._extract_handle(link),
            "confidence": 0.75
        }
    
    def _identify_platform(self, url: str) -> Optional[str]:
        """Quickly identify social platform."""
        platforms = {
            'instagram.com': 'instagram',
            'facebook.com': 'facebook', 
            'twitter.com': 'twitter',
            'x.com': 'twitter'
        }
        
        for domain, platform in platforms.items():
            if domain in url.lower():
                return platform
        return None
    
    def _extract_handle(self, url: str) -> Optional[str]:
        """Extract social media handle."""
        try:
            parsed = urlparse(url)
            path_parts = [p for p in parsed.path.strip('/').split('/') if p]
            if path_parts and path_parts[0]:
                return f"@{path_parts[0]}"
        except:
            pass
        return None
    
    def export_to_csv(self, results: List[Dict], filename: str):
        """Export results to CSV - optimized format."""
        if not results:
            return
        
        rows = []
        for result in results:
            if 'error' in result:
                continue
            
            email = result.get('email', {})
            social = result.get('social_media', {})
            
            row = {
                'url': result.get('url', ''),
                'email': email.get('email', '') if email else '',
                'email_confidence': email.get('confidence', '') if email else '',
                'social_platform': social.get('platform', '') if social else '',
                'social_handle': social.get('handle', '') if social else '',
                'social_url': social.get('url', '') if social else '',
                'social_confidence': social.get('confidence', '') if social else '',
                'pages_processed': result.get('pages_processed', 0),
                'timestamp': result.get('timestamp', '')
            }
            rows.append(row)
        
        if rows:
            fieldnames = list(rows[0].keys())
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(rows)
            print(f"📊 Results exported to {filename}")
    
    def print_summary(self, results: List[Dict]):
        """Print performance summary."""
        successful = len([r for r in results if 'error' not in r])
        errors = len([r for r in results if 'error' in r])
        
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        
        total_pages = sum(r.get('pages_processed', 0) for r in results if 'error' not in r)
        avg_pages = total_pages / successful if successful > 0 else 0
        
        print(f"\n📊 EXTRACTION SUMMARY:")
        print(f"   • URLs processed: {len(results)}")
        print(f"   • Successful: {successful}")
        print(f"   • Errors: {errors}")
        print(f"   • Emails found: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)" if successful > 0 else "")
        print(f"   • Social media found: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)" if successful > 0 else "")
        print(f"   • Average pages per URL: {avg_pages:.1f}")


# Convenience function for batch processing
async def extract_emails_and_socials_fast(urls: Union[str, List[str]], 
                                         max_concurrent: int = 15) -> List[Dict]:
    """
    Ultra-fast extraction of emails and social media.
    
    Args:
        urls: URLs to process
        max_concurrent: Maximum concurrent requests (default: 15)
        
    Returns:
        List of results with best email and social per URL
    """
    extractor = FastEmailSocialExtractor(max_concurrent=max_concurrent)
    return await extractor.extract_fast(urls)
