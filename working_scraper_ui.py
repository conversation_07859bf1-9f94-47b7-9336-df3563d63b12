"""
Working Scraper UI - Fixed version with proper pause, statistics, and chunk processing
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
import json
import os
import time
import asyncio
from datetime import datetime
from pathlib import Path

class WorkingScraperUI:
    """Working Tkinter UI for the pausable contact scraper."""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🔍 Lead Contact Scraper - Working Version")
        self.root.geometry("900x700")
        self.root.configure(bg='#f0f0f0')
        
        # State variables
        self.chunks_dir = None
        self.results_dir = None
        self.is_running = False
        self.scraper_thread = None
        self.should_stop = False
        
        # Progress tracking
        self.total_chunks = 0
        self.completed_chunks = 0
        self.current_chunk = 0
        self.total_urls = 0
        self.emails_found = 0
        self.phones_found = 0
        
        # Create UI
        self.create_widgets()
        self.load_available_sessions()
        
        # Start monitoring
        self.start_monitoring()
    
    def create_widgets(self):
        """Create the UI widgets."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="🔍 Lead Contact Scraper - Working Version", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Session selection frame
        session_frame = ttk.Frame(main_frame)
        session_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(session_frame, text="Chunks Directory:").pack(side=tk.LEFT)
        self.chunks_var = tk.StringVar()
        self.chunks_combo = ttk.Combobox(session_frame, textvariable=self.chunks_var, 
                                        state="readonly", width=50)
        self.chunks_combo.pack(side=tk.LEFT, padx=(10, 5), fill=tk.X, expand=True)
        self.chunks_combo.bind('<<ComboboxSelected>>', self.on_session_selected)
        
        ttk.Button(session_frame, text="Browse", command=self.browse_chunks_dir).pack(side=tk.RIGHT)
        
        # Control buttons frame
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=20)
        
        self.start_btn = ttk.Button(control_frame, text="▶️ Start Scraping", 
                                   command=self.start_scraping)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.pause_btn = ttk.Button(control_frame, text="⏸️ Pause", 
                                   command=self.pause_scraping, state=tk.DISABLED)
        self.pause_btn.pack(side=tk.LEFT, padx=5)
        
        self.resume_btn = ttk.Button(control_frame, text="▶️ Resume", 
                                    command=self.resume_scraping, state=tk.DISABLED)
        self.resume_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(control_frame, text="⏹️ Stop", 
                                  command=self.stop_scraping, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        # Progress frame
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding="10")
        progress_frame.pack(fill=tk.X, pady=10)
        
        # Overall progress
        ttk.Label(progress_frame, text="Overall Progress:").pack(anchor=tk.W)
        self.overall_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.overall_progress.pack(fill=tk.X, pady=5)
        self.overall_label = ttk.Label(progress_frame, text="0/0 chunks completed")
        self.overall_label.pack(anchor=tk.W)
        
        # Current chunk progress
        ttk.Label(progress_frame, text="Current Chunk:").pack(anchor=tk.W, pady=(10, 0))
        self.chunk_progress = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.chunk_progress.pack(fill=tk.X, pady=5)
        self.chunk_label = ttk.Label(progress_frame, text="Ready")
        self.chunk_label.pack(anchor=tk.W)
        
        # Statistics frame
        stats_frame = ttk.Frame(progress_frame)
        stats_frame.pack(fill=tk.X, pady=10)
        
        self.urls_label = ttk.Label(stats_frame, text="URLs Processed: 0")
        self.urls_label.pack(side=tk.LEFT)
        
        self.emails_label = ttk.Label(stats_frame, text="📧 Emails: 0")
        self.emails_label.pack(side=tk.LEFT, padx=(20, 0))
        
        self.phones_label = ttk.Label(stats_frame, text="📞 Phones: 0")
        self.phones_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # Log frame
        log_frame = ttk.LabelFrame(main_frame, text="Scraping Output", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD, 
                                                 font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready - Select chunks directory to begin")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, pady=(10, 0))
    
    def log_message(self, message):
        """Add message to log with timestamp."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def load_available_sessions(self):
        """Load available chunk directories."""
        chunk_dirs = [d for d in os.listdir('.') if d.endswith('_chunks') and os.path.isdir(d)]
        self.chunks_combo['values'] = chunk_dirs
        
        if chunk_dirs:
            self.chunks_combo.set(chunk_dirs[0])
            self.on_session_selected()
    
    def browse_chunks_dir(self):
        """Browse for chunks directory."""
        directory = filedialog.askdirectory(title="Select Chunks Directory")
        if directory:
            self.chunks_var.set(directory)
            self.on_session_selected()
    
    def on_session_selected(self, event=None):
        """Handle session selection."""
        chunks_dir = self.chunks_var.get()
        if not chunks_dir or not os.path.exists(chunks_dir):
            return
        
        self.chunks_dir = chunks_dir
        self.results_dir = f"{chunks_dir}_results"
        
        # Load manifest to get chunk info
        try:
            manifest_path = os.path.join(chunks_dir, 'chunk_manifest.json')
            with open(manifest_path, 'r') as f:
                manifest = json.load(f)
            
            self.total_chunks = manifest['num_chunks']
            total_records = manifest['total_records']
            
            self.log_message(f"📂 Selected: {chunks_dir}")
            self.log_message(f"📊 Total chunks: {self.total_chunks}, Total URLs: {total_records:,}")
            
            # Update progress
            self.overall_progress['maximum'] = self.total_chunks
            self.overall_label.config(text=f"0/{self.total_chunks} chunks completed")
            
            # Load existing progress if available
            self.load_progress()
            
            self.status_var.set(f"Ready - {self.total_chunks} chunks, {total_records:,} URLs")
            
        except Exception as e:
            self.log_message(f"❌ Error loading session: {e}")
            self.status_var.set("Error loading session")
    
    def load_progress(self):
        """Load existing progress."""
        if not self.results_dir:
            return
        
        progress_file = os.path.join(self.results_dir, 'scraping_progress.json')
        if os.path.exists(progress_file):
            try:
                with open(progress_file, 'r') as f:
                    progress = json.load(f)
                
                self.completed_chunks = len(progress.get('completed_chunks', []))
                self.total_urls = progress.get('total_processed', 0)
                self.emails_found = progress.get('total_with_emails', 0)
                self.phones_found = progress.get('total_with_phones', 0)
                
                # Update UI
                self.update_progress_display()
                
                # Check if paused
                pause_file = os.path.join(self.results_dir, 'PAUSE_SCRAPING')
                if os.path.exists(pause_file):
                    self.status_var.set("⏸️ Paused - Click Resume to continue")
                    self.resume_btn.config(state=tk.NORMAL)
                    self.pause_btn.config(state=tk.DISABLED)
                
                self.log_message(f"📈 Loaded progress: {self.completed_chunks} chunks completed")
                
            except Exception as e:
                self.log_message(f"❌ Error loading progress: {e}")
    
    def update_progress_display(self):
        """Update the progress display."""
        self.overall_progress['value'] = self.completed_chunks
        self.overall_label.config(text=f"{self.completed_chunks}/{self.total_chunks} chunks completed")
        self.urls_label.config(text=f"URLs Processed: {self.total_urls:,}")
        self.emails_label.config(text=f"📧 Emails: {self.emails_found:,}")
        self.phones_label.config(text=f"📞 Phones: {self.phones_found:,}")
    
    def start_scraping(self):
        """Start the scraping process."""
        if not self.chunks_dir:
            messagebox.showerror("Error", "Please select a chunks directory first")
            return

        if not os.path.exists(self.chunks_dir):
            messagebox.showerror("Error", f"Chunks directory not found: {self.chunks_dir}")
            return

        # Ensure results_dir is set
        if not self.results_dir:
            self.results_dir = f"{self.chunks_dir}_results"

        # Check if manifest exists
        manifest_path = os.path.join(self.chunks_dir, 'chunk_manifest.json')
        if not os.path.exists(manifest_path):
            messagebox.showerror("Error", f"Chunk manifest not found: {manifest_path}")
            return

        # Remove any existing pause file
        pause_file = os.path.join(self.results_dir, 'PAUSE_SCRAPING')
        if os.path.exists(pause_file):
            os.remove(pause_file)
        
        self.is_running = True
        self.should_stop = False
        self.start_btn.config(state=tk.DISABLED)
        self.pause_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.NORMAL)
        self.resume_btn.config(state=tk.DISABLED)
        
        self.status_var.set("🚀 Starting scraper...")
        self.log_message("🚀 Starting contact extraction...")
        self.log_message(f"📂 Processing {self.total_chunks} chunks with {self.total_urls:,} URLs")
        self.log_message("💤 Perfect for overnight processing - will save progress automatically")
        self.chunk_progress.start(10)

        # Start scraper in thread
        self.scraper_thread = threading.Thread(target=self.run_scraper, daemon=True)
        self.scraper_thread.start()
    
    def run_scraper(self):
        """Run the scraper with proper error handling for overnight processing."""
        try:
            # Import here to avoid startup issues
            from pausable_scraper import PausableScraper

            # Ensure directories are set
            if not self.chunks_dir or not self.results_dir:
                raise ValueError("Chunks or results directory not properly set")

            # Log start time for overnight tracking
            start_time = datetime.now()
            self.root.after(0, lambda: self.log_message(f"🕐 Started at: {start_time.strftime('%Y-%m-%d %H:%M:%S')}"))
            self.root.after(0, lambda: self.log_message("💤 Running overnight - progress will be saved automatically"))

            # Create scraper
            scraper = PausableScraper(self.chunks_dir, self.results_dir)

            # Run scraper
            asyncio.run(scraper.run())

            # Log completion time
            end_time = datetime.now()
            duration = end_time - start_time
            self.root.after(0, lambda: self.log_message(f"🕐 Completed at: {end_time.strftime('%Y-%m-%d %H:%M:%S')}"))
            self.root.after(0, lambda: self.log_message(f"⏱️ Total time: {duration}"))

            # Completed successfully
            self.root.after(0, self.scraping_completed)

        except Exception as e:
            error_msg = str(e)
            error_time = datetime.now()
            self.root.after(0, lambda: self.log_message(f"❌ Error at {error_time.strftime('%H:%M:%S')}: {error_msg}"))
            self.root.after(0, lambda: self.scraping_error(error_msg))
    
    def scraping_completed(self):
        """Handle scraping completion."""
        self.is_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.pause_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.DISABLED)
        self.resume_btn.config(state=tk.DISABLED)
        
        self.chunk_progress.stop()
        self.chunk_label.config(text="✅ Completed")
        self.status_var.set("🎉 Scraping completed!")
        self.log_message("🎉 Contact extraction completed!")
        
        # Load final progress
        self.load_progress()
        
        # Ask if user wants to combine results
        if messagebox.askyesno("Complete", "Scraping completed! Would you like to combine results now?"):
            self.combine_results()
    
    def scraping_error(self, error_msg):
        """Handle scraping error."""
        self.is_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.pause_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.DISABLED)
        
        self.chunk_progress.stop()
        self.status_var.set("❌ Error occurred")
        self.log_message(f"❌ Error: {error_msg}")
        
        messagebox.showerror("Scraping Error", f"An error occurred:\n{error_msg}")
    
    def pause_scraping(self):
        """Pause the scraping process."""
        if not self.results_dir:
            return
        
        # Create pause file
        os.makedirs(self.results_dir, exist_ok=True)
        pause_file = os.path.join(self.results_dir, 'PAUSE_SCRAPING')
        with open(pause_file, 'w') as f:
            f.write(f"Scraping paused at: {datetime.now().isoformat()}\n")
        
        self.pause_btn.config(state=tk.DISABLED)
        self.resume_btn.config(state=tk.NORMAL)
        self.status_var.set("⏸️ Pausing after current chunk...")
        self.log_message("⏸️ Pause requested - will pause after current chunk completes")
    
    def resume_scraping(self):
        """Resume the scraping process."""
        if not self.results_dir:
            return
        
        # Remove pause file
        pause_file = os.path.join(self.results_dir, 'PAUSE_SCRAPING')
        if os.path.exists(pause_file):
            os.remove(pause_file)
        
        self.resume_btn.config(state=tk.DISABLED)
        self.status_var.set("▶️ Resuming...")
        self.log_message("▶️ Pause file removed - resuming scraping")
        
        # Start scraping again
        self.start_scraping()
    
    def stop_scraping(self):
        """Stop the scraping process."""
        if messagebox.askyesno("Confirm", "Are you sure you want to stop scraping?\nProgress will be saved."):
            self.should_stop = True
            self.pause_scraping()  # This will cause graceful stop
            
            self.is_running = False
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            self.chunk_progress.stop()
            self.log_message("⏹️ Scraping stopped by user")
    
    def combine_results(self):
        """Combine results using the results combiner."""
        if not self.results_dir or not os.path.exists(self.results_dir):
            messagebox.showerror("Error", "No results directory found")
            return
        
        try:
            from results_combiner import ResultsCombiner
            
            self.log_message("🔗 Combining results...")
            self.status_var.set("🔗 Combining results...")
            
            combiner = ResultsCombiner(self.results_dir)
            summary = combiner.combine_results()
            
            if summary:
                self.log_message("✅ Results combined successfully!")
                self.status_var.set("✅ Results combined!")
                
                # Show summary
                emails = summary['contacts_found']['emails']
                phones_only = summary['segmentation']['companies_with_phones_only']
                
                messagebox.showinfo("Results Combined", 
                    f"Results combined successfully!\n\n"
                    f"📧 Companies with emails: {emails:,}\n"
                    f"📞 Companies with phones only: {phones_only:,}\n\n"
                    f"Files created in current directory.")
            
        except Exception as e:
            self.log_message(f"❌ Error combining results: {e}")
            messagebox.showerror("Error", f"Error combining results:\n{e}")
    
    def start_monitoring(self):
        """Start monitoring progress in background."""
        def monitor():
            while True:
                try:
                    if self.is_running and self.results_dir and os.path.exists(self.results_dir):
                        # Load progress and update UI
                        progress_file = os.path.join(self.results_dir, 'scraping_progress.json')
                        if os.path.exists(progress_file):
                            with open(progress_file, 'r') as f:
                                progress = json.load(f)
                            
                            # Update progress variables
                            old_completed = self.completed_chunks
                            self.completed_chunks = len(progress.get('completed_chunks', []))
                            self.total_urls = progress.get('total_processed', 0)
                            self.emails_found = progress.get('total_with_emails', 0)
                            self.phones_found = progress.get('total_with_phones', 0)
                            self.current_chunk = progress.get('current_chunk', 0)
                            
                            # Update UI on main thread
                            self.root.after(0, self.update_progress_display)
                            
                            # Log chunk completion
                            if self.completed_chunks > old_completed:
                                self.root.after(0, lambda: self.log_message(
                                    f"✅ Chunk {self.completed_chunks} completed"))
                            
                            # Update chunk status
                            if self.current_chunk:
                                self.root.after(0, lambda: self.chunk_label.config(
                                    text=f"Processing chunk {self.current_chunk}"))
                    
                    time.sleep(2)  # Update every 2 seconds
                    
                except Exception as e:
                    print(f"Monitor error: {e}")
                    time.sleep(5)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()

def main():
    """Main function to run the UI."""
    print("🚀 Starting Working Lead Contact Scraper UI...")
    
    root = tk.Tk()
    
    # Configure ttk styles
    style = ttk.Style()
    style.theme_use('clam')
    
    try:
        app = WorkingScraperUI(root)
        root.mainloop()
    except KeyboardInterrupt:
        print("Application interrupted")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        messagebox.showerror("Application Error", f"An error occurred:\n{e}")

if __name__ == "__main__":
    main()
