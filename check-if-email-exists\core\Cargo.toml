[package]
name = "check-if-email-exists"
version = "0.11.6"
authors = ["<PERSON><PERSON>ry <<EMAIL>>"]
categories = ["email"]
description = "Check if an email address exists without sending any email"
documentation = "http://docs.rs/check-if-email-exists"
edition = "2018"
keywords = ["email", "smtp", "mx", "verification"]
license = "AGPL-3.0"
publish = true
readme = "../README.md"
repository = "https://github.com/reacherhq/check-if-email-exists"

[dependencies]
anyhow = "1.0"
async-recursion = "1.0.5"
async-smtp = { version = "0.9.1", features = ["runtime-tokio"] }
chrono = { version = "0.4.31", features = ["serde"] }
config = "0.14"
derive_builder = "0.20"
fast-socks5 = "0.9"
fantoccini = { version = "0.21.2", default-features = false, features = [
    "rustls-tls",
] }
futures = { version = "0.3.30" }
hickory-proto = "0.24.0"
hickory-resolver = "0.24.0"
levenshtein = "1.0.5"
log = "0.4.26"
mailchecker = "6.0.11"
md5 = "0.7.0"
once_cell = "1.21.1"
rand = { version = "0.8.5", features = ["small_rng"] }
regex = "1.11.1"
reqwest = { version = "0.12.15", default-features = false, features = [
    "json",
    "rustls-tls",
] }
rustls = { version = "0.23", features = ["ring"] }
sentry = { version = "0.36", default-features = false, features = [
    "reqwest",
    "rustls",
], optional = true }
serde = { version = "1.0.214", features = ["derive"] }
serde_json = "1.0.133"
thiserror = "2.0"
tokio = { version = "1.40.0", features = ["rt-multi-thread", "macros"] }
tracing = "0.1.40"
