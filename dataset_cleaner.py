"""
Dataset Cleaner - Remove invalid URLs and prepare clean dataset
"""

import pandas as pd
import re
from urllib.parse import urlparse
from typing import List, Tuple
import os

class DatasetCleaner:
    """Clean dataset by removing invalid URLs and preparing for scraping."""
    
    def __init__(self):
        self.valid_url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    def is_valid_url(self, url: str) -> bool:
        """Check if URL is valid and accessible."""
        if not url or pd.isna(url) or url.strip() == '':
            return False
        
        url = url.strip()
        
        # Basic URL format validation
        if not self.valid_url_pattern.match(url):
            return False
        
        # Parse URL to check components
        try:
            parsed = urlparse(url)
            if not parsed.netloc:
                return False
            
            # Avoid obviously invalid domains
            invalid_domains = [
                'example.com', 'test.com', 'localhost', '127.0.0.1',
                'domain.com', 'website.com', 'yoursite.com'
            ]
            
            if any(invalid in parsed.netloc.lower() for invalid in invalid_domains):
                return False
            
            return True
            
        except Exception:
            return False
    
    def clean_dataset(self, input_file: str, output_file: str = None) -> Tuple[str, dict]:
        """
        Clean dataset by removing invalid URLs.
        
        Args:
            input_file: Path to input CSV file
            output_file: Path to output CSV file (optional)
            
        Returns:
            Tuple of (output_file_path, statistics)
        """
        print(f"🧹 CLEANING DATASET: {input_file}")
        print("=" * 60)
        
        # Read the dataset
        print("📖 Reading dataset...")
        df = pd.read_csv(input_file)
        
        print(f"   • Total records: {len(df):,}")
        print(f"   • Columns: {list(df.columns)}")
        
        # Check if website column exists
        if 'website' not in df.columns:
            raise ValueError("Dataset must have a 'website' column")
        
        # Analyze URLs
        print("\n🔍 Analyzing URLs...")
        total_records = len(df)
        empty_urls = df['website'].isna().sum() + (df['website'] == '').sum()
        non_empty_urls = total_records - empty_urls
        
        print(f"   • Records with empty URLs: {empty_urls:,}")
        print(f"   • Records with non-empty URLs: {non_empty_urls:,}")
        
        # Validate URLs
        print("\n✅ Validating URLs...")
        valid_mask = df['website'].apply(self.is_valid_url)
        valid_count = valid_mask.sum()
        invalid_count = non_empty_urls - valid_count
        
        print(f"   • Valid URLs: {valid_count:,}")
        print(f"   • Invalid URLs: {invalid_count:,}")
        
        # Filter to valid URLs only
        clean_df = df[valid_mask].copy()
        
        # Generate output filename if not provided
        if output_file is None:
            base_name = os.path.splitext(input_file)[0]
            output_file = f"{base_name}_CLEAN.csv"
        
        # Save clean dataset
        print(f"\n💾 Saving clean dataset to: {output_file}")
        clean_df.to_csv(output_file, index=False)
        
        # Statistics
        stats = {
            'original_records': total_records,
            'empty_urls': empty_urls,
            'invalid_urls': invalid_count,
            'valid_urls': valid_count,
            'clean_records': len(clean_df),
            'removal_rate': ((total_records - len(clean_df)) / total_records) * 100
        }
        
        print("\n📊 CLEANING SUMMARY:")
        print(f"   • Original records: {stats['original_records']:,}")
        print(f"   • Clean records: {stats['clean_records']:,}")
        print(f"   • Removed: {stats['original_records'] - stats['clean_records']:,} ({stats['removal_rate']:.1f}%)")
        print(f"   • Clean dataset saved: {output_file}")
        
        return output_file, stats

def main():
    """Main function to clean the dataset."""
    # Default input file
    input_file = "pittsburgh_coffee_prospects_FINAL_20250914_164553.csv"
    
    # Check if file exists
    if not os.path.exists(input_file):
        print(f"❌ Error: File '{input_file}' not found!")
        print("Please make sure the dataset file is in the current directory.")
        return
    
    # Clean the dataset
    cleaner = DatasetCleaner()
    try:
        output_file, stats = cleaner.clean_dataset(input_file)
        print(f"\n✅ SUCCESS! Clean dataset ready: {output_file}")
        
    except Exception as e:
        print(f"❌ Error cleaning dataset: {str(e)}")

if __name__ == "__main__":
    main()
