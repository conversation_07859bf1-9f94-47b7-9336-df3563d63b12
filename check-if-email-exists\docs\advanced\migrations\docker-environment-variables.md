# Docker Environment Variables (v0.7)

{% hint style="info" %}
This page only applies to Reacher version 0.7. For the `beta` v0.10 version, see [reacher-configuration-v0.10.md](../../self-hosting/reacher-configuration-v0.10.md "mention").
{% endhint %}

<PERSON>'s software is available on [Docker Hub](https://hub.docker.com/r/reacherhq/backend/tags). You can get started using the default parameters:

```bash
docker run -p 8080:8080 reacherhq/backend:latest
```

Below are the environment variables used to configure the HTTP server. To pass them to the Docker container, use the `-e {ENV_VAR}={VALUE}` flag.

<table><thead><tr><th width="220">Env Var</th><th width="107">Required?</th><th width="264">Description</th><th>Default</th></tr></thead><tbody><tr><td><code>RUST_LOG</code></td><td>No</td><td>One of <code>trace,debug,warn,error,info</code>. <code>RUST_LOG=debug</code> is very handful for debugging purposes.</td><td><code>info</code></td></tr><tr><td><code>RCH_HTTP_HOST</code></td><td>No</td><td>The host name to bind the HTTP server to.</td><td><code>0.0.0.0</code></td></tr><tr><td><code>PORT</code></td><td>No</td><td>The port to bind the HTTP server to, often populated by the cloud provider.</td><td><code>8080</code></td></tr><tr><td><code>RCH_SENTRY_DSN</code></td><td>No</td><td>If set, bug reports will be sent to this <a href="https://sentry.io">Sentry</a> DSN.</td><td>not defined</td></tr><tr><td><code>RCH_HEADER_SECRET</code></td><td>No</td><td>If set, then all HTTP requests must have the <code>x-reacher-secret</code> header set to this value. This is used to protect the backend against public unwanted HTTP requests.</td><td>undefined</td></tr><tr><td><code>RCH_FROM_EMAIL</code></td><td>No</td><td>Email to use in the <code>&#x3C;MAIL FROM:></code> SMTP step. Can be overwritten by each API request's <code>from_email</code> field.</td><td><a href="mailto:<EMAIL>"><EMAIL></a></td></tr><tr><td><code>RCH_HELLO_NAME</code></td><td>No</td><td>Name to use in the <code>&#x3C;EHLO></code> SMTP step. Can be overwritten by each API request's <code>hello_name</code> field.</td><td>gmail.com</td></tr><tr><td><code>RCH_SMTP_TIMEOUT</code></td><td>No</td><td>Timeout for each SMTP connection.</td><td>45s</td></tr><tr><td><code>RCH_WEBDRIVER_ADDR</code></td><td>No</td><td>Set to a running WebDriver process endpoint (e.g. <code>http://localhost:9515</code>) to use a headless navigator to password recovery pages to check Yahoo and Hotmail/Outlook addresses. We recommend <code>chromedriver</code> as it allows parallel requests.</td><td><code>http://localhost:9515</code></td></tr><tr><td><strong>For Bulk Verification:</strong></td><td></td><td></td><td></td></tr><tr><td><code>RCH_ENABLE_BULK</code></td><td>No</td><td>If set to <code>1</code>, then bulk verification endpoints will be added to the backend.</td><td>0</td></tr><tr><td><code>DATABASE_URL</code></td><td>Yes if <code>RCH_ENABLE_BULK==1</code></td><td>[Bulk] Database connection string for storing results and task queue</td><td>not defined</td></tr><tr><td><code>RCH_DATABASE_MAX_CONNECTIONS</code></td><td>No</td><td>[Bulk] Connections created for the database pool</td><td>5</td></tr><tr><td><code>RCH_MINIMUM_TASK_CONCURRENCY</code></td><td>No</td><td>[Bulk] Minimum number of concurrent running tasks below which more tasks are fetched</td><td>10</td></tr><tr><td><code>RCH_MAXIMUM_CONCURRENT_TASK_FETCH</code></td><td>No</td><td>[Bulk] Maximum number of tasks fetched at once</td><td>20</td></tr></tbody></table>
