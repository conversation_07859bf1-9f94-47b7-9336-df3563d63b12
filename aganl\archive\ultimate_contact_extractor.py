"""
Ultimate Contact Extractor - The Perfect Balance
Discovers available pages, prioritizes intelligently, stops early when successful.
"""

import asyncio
import json
import re
import csv
import aiohttp
from datetime import datetime
from typing import Dict, List, Optional, Union, Tuple
from urllib.parse import urljoin, urlparse

from crawl4ai import Async<PERSON>eb<PERSON>rawler, CrawlerRunConfig
from crawl4ai import JsonCssExtractionStrategy


class UltimateContactExtractor:
    """
    The ultimate contact extractor that combines all best practices:
    - Smart page discovery with robust fallbacks
    - Intelligent prioritization
    - Early stopping when both contacts found
    - Guaranteed coverage for critical pages
    """
    
    def __init__(self, batch_size: int = 50, max_concurrent: int = 6):
        """Initialize the ultimate extractor."""
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        
        # Page patterns with priority scores (higher = more important)
        self.page_patterns = [
            ('', 10),              # Main page - highest priority
            ('/contact-us', 9),    # Primary contact page
            ('/contact', 9),       # Alternative contact page
            ('/about', 7),         # About page often has contact info
            ('/about-us', 6),      # Alternative about page
            ('/location', 5),      # Location pages sometimes have contact
            ('/locations', 5),     # Multiple locations
            ('/info', 4),          # Info pages
            ('/hours', 3),         # Hours pages sometimes have contact
        ]
        
        # Fallback pages if discovery fails completely
        self.fallback_pages = ['', '/contact-us', '/contact', '/about']
    
    async def extract_ultimate(self, urls: Union[str, List[str]]) -> List[Dict]:
        """
        Ultimate extraction with smart discovery and early stopping.
        
        Args:
            urls: URLs to process
            
        Returns:
            List of results with emails and social media
        """
        if isinstance(urls, str):
            urls = [urls]
        
        print(f"🚀 ULTIMATE CONTACT EXTRACTION")
        print(f"📊 Processing {len(urls)} URLs with smart discovery + early stopping")
        print(f"⚡ Max concurrent per batch: {self.max_concurrent}")
        
        all_results = []
        total_start = datetime.now()
        
        # Process URLs in batches
        for i in range(0, len(urls), self.batch_size):
            batch = urls[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            total_batches = (len(urls) + self.batch_size - 1) // self.batch_size
            
            print(f"\n📦 Batch {batch_num}/{total_batches}: {len(batch)} URLs")
            
            batch_start = datetime.now()
            batch_results = await self._process_batch_ultimate(batch)
            batch_duration = (datetime.now() - batch_start).total_seconds()
            
            all_results.extend(batch_results)
            
            print(f"   ✅ Completed in {batch_duration:.2f}s ({len(batch)/batch_duration:.2f} URLs/sec)")
            
            # Small delay between batches
            if i + self.batch_size < len(urls):
                await asyncio.sleep(1)
        
        total_duration = (datetime.now() - total_start).total_seconds()
        
        print(f"\n🎉 ULTIMATE EXTRACTION COMPLETED!")
        print(f"   • Total URLs: {len(urls)}")
        print(f"   • Total time: {total_duration:.2f}s ({total_duration/60:.1f} min)")
        print(f"   • Overall rate: {len(urls)/total_duration:.2f} URLs/second")
        
        return all_results
    
    async def _process_batch_ultimate(self, urls: List[str]) -> List[Dict]:
        """Process a batch with ultimate intelligence."""
        
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_single(url: str) -> Dict:
            async with semaphore:
                return await self._extract_single_ultimate(url)
        
        tasks = [process_single(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append({
                    "url": urls[i],
                    "error": str(result),
                    "timestamp": datetime.now().isoformat()
                })
            else:
                final_results.append(result)
        
        return final_results
    
    async def _extract_single_ultimate(self, url: str) -> Dict:
        """Extract from single URL with ultimate intelligence."""
        
        try:
            domain = urlparse(url).netloc.replace('www.', '')
            
            # Step 1: Discover available pages with robust fallbacks
            available_pages = await self._discover_pages_robust(url)
            
            # Step 2: Prioritize pages intelligently
            prioritized_pages = self._prioritize_pages_smart(available_pages)
            
            print(f"   🧠 {domain}: Discovered {len(available_pages)} pages, prioritized {len(prioritized_pages)} to check")
            
            best_email = None
            best_social = None
            pages_checked = 0
            
            async with AsyncWebCrawler(verbose=False) as crawler:
                for page_url, priority in prioritized_pages:
                    try:
                        print(f"     🔍 Checking: {page_url} (priority: {priority})")
                        
                        # Extract from this page
                        result = await asyncio.wait_for(
                            self._extract_from_page_ultimate(crawler, page_url),
                            timeout=15.0
                        )
                        
                        pages_checked += 1
                        
                        # Update best contacts
                        if result.get('email') and not best_email:
                            best_email = result['email']
                            print(f"     📧 Found email: {best_email['email']}")
                        
                        if result.get('social') and not best_social:
                            best_social = result['social']
                            print(f"     🌐 Found social: {best_social['platform']}")
                        
                        # Smart early stopping: stop when we have both OR after checking key pages
                        if best_email and best_social:
                            print(f"     ⚡ Early stop: Found both contacts after {pages_checked} pages")
                            break
                        elif pages_checked >= 2 and (best_email or best_social):
                            # If we have one contact type and checked main + contact page, that's often enough
                            remaining_high_priority = [p for p in prioritized_pages[pages_checked:] if p[1] >= 8]
                            if not remaining_high_priority:
                                print(f"     ⚡ Smart stop: Have contact info, no more high-priority pages")
                                break
                    
                    except asyncio.TimeoutError:
                        print(f"     ⏰ Timeout: {page_url}")
                        continue
                    except Exception as e:
                        print(f"     ❌ Error: {page_url} - {str(e)}")
                        continue
            
            print(f"     ✅ {domain}: Checked {pages_checked}/{len(prioritized_pages)} pages")
            
            return {
                "url": url,
                "timestamp": datetime.now().isoformat(),
                "email": best_email,
                "social_media": best_social,
                "pages_discovered": len(available_pages),
                "pages_checked": pages_checked,
                "efficiency": f"{pages_checked}/{len(available_pages)}",
                "success": True
            }
        
        except Exception as e:
            return {
                "url": url,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _discover_pages_robust(self, base_url: str) -> List[Tuple[str, int]]:
        """Robustly discover available pages with multiple fallback strategies."""
        
        available_pages = []
        
        try:
            # Try intelligent discovery first
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=8)) as session:
                tasks = []
                
                for pattern, priority in self.page_patterns:
                    if pattern == '':
                        page_url = base_url
                    else:
                        page_url = urljoin(base_url, pattern)
                    
                    tasks.append(self._check_page_robust(session, page_url, priority))
                
                # Check all pages concurrently
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for result in results:
                    if isinstance(result, tuple) and result[0]:  # (exists, url, priority)
                        available_pages.append((result[1], result[2]))
        
        except Exception:
            # If discovery fails completely, use fallback pages
            print(f"     ⚠️  Page discovery failed, using fallback pages")
            for pattern in self.fallback_pages:
                if pattern == '':
                    page_url = base_url
                else:
                    page_url = urljoin(base_url, pattern)

                # Assign reasonable priorities to fallback pages
                priority = 10 if pattern == '' else 8 if 'contact' in pattern else 6
                available_pages.append((page_url, priority))

        # Ensure we always have at least the main page
        if not available_pages:
            print(f"     ⚠️  No pages discovered, adding main page as fallback")
            available_pages.append((base_url, 10))

        return available_pages
    
    async def _check_page_robust(self, session: aiohttp.ClientSession, url: str, priority: int) -> Tuple[bool, str, int]:
        """Robustly check if a page exists with multiple strategies."""
        try:
            # Try HEAD request first
            async with session.head(url, allow_redirects=True, ssl=False) as response:
                if response.status < 400:
                    return (True, url, priority)
                elif response.status in [403, 405]:  # Forbidden or Method Not Allowed
                    # Try GET request as fallback
                    try:
                        async with session.get(url, allow_redirects=True, ssl=False) as get_response:
                            exists = get_response.status < 400
                            return (exists, url, priority)
                    except:
                        # If GET also fails, assume high-priority pages exist
                        return (priority >= 8, url, priority)
                else:
                    return (False, url, priority)
        except Exception as e:
            # If all requests fail, be more generous with assumptions
            # Always assume main page exists, and contact pages for high priority
            if priority >= 10:  # Main page
                return (True, url, priority)
            elif priority >= 8:  # Contact pages
                return (True, url, priority)
            return (False, url, priority)
    
    def _prioritize_pages_smart(self, available_pages: List[Tuple[str, int]]) -> List[Tuple[str, int]]:
        """Smart prioritization based on URL patterns and discovery results."""
        
        scored_pages = []
        
        for page_url, base_priority in available_pages:
            score = base_priority
            url_lower = page_url.lower()
            
            # Boost scores based on URL content
            if url_lower.endswith(('/', '')):
                score += 2  # Main pages get boost
            if 'contact' in url_lower:
                score += 3  # Contact pages get big boost
            if 'about' in url_lower:
                score += 1  # About pages get small boost
            
            # Penalize very long or complex URLs
            if len(page_url) > 100 or page_url.count('/') > 5:
                score -= 2
            
            scored_pages.append((page_url, score))
        
        # Sort by score (highest first)
        scored_pages.sort(key=lambda x: x[1], reverse=True)
        
        return scored_pages
    
    async def _extract_from_page_ultimate(self, crawler: AsyncWebCrawler, url: str) -> Dict:
        """Ultimate extraction combining CSS selectors and enhanced regex."""
        
        # Enhanced CSS extraction strategy
        css_schema = {
            "name": "Ultimate Contact",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "emails",
                    "selector": "a[href^='mailto:'], [data-email], [href*='@']",
                    "type": "list",
                    "fields": [{"name": "email", "type": "attribute", "attribute": "href"}]
                },
                {
                    "name": "social",
                    "selector": "a[href*='instagram.com'], a[href*='facebook.com'], a[href*='twitter.com'], a[href*='x.com']",
                    "type": "list",
                    "fields": [{"name": "url", "type": "attribute", "attribute": "href"}]
                }
            ]
        }
        
        strategy = JsonCssExtractionStrategy(css_schema, verbose=False)
        config = CrawlerRunConfig(extraction_strategy=strategy)
        
        result = await crawler.arun(url=url, config=config)
        
        best_email = None
        best_social = None
        
        if result.success:
            # Process CSS results
            if result.extracted_content:
                try:
                    data = json.loads(result.extracted_content)
                    if data and len(data) > 0:
                        css_data = data[0]
                        
                        # Extract emails
                        emails = set()
                        for email_item in css_data.get('emails', []):
                            if isinstance(email_item, dict):
                                email = email_item.get('email', '').replace('mailto:', '').strip().lower()
                                if self._is_valid_email(email):
                                    emails.add(email)
                        
                        if emails:
                            best_email = self._select_best_email(list(emails))
                        
                        # Extract social links
                        social_links = set()
                        for social_item in css_data.get('social', []):
                            if isinstance(social_item, dict):
                                social_url = social_item.get('url', '').strip()
                                if social_url and self._is_valid_social_url(social_url):
                                    social_links.add(social_url)
                        
                        if social_links:
                            best_social = self._select_best_social(list(social_links))
                
                except json.JSONDecodeError:
                    pass
            
            # Enhanced regex fallback for maximum coverage
            if (not best_email or not best_social) and hasattr(result, 'cleaned_html'):
                content = result.cleaned_html
                
                # Ultimate email regex patterns
                if not best_email:
                    email_patterns = [
                        r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b',
                        r'(?:email|contact|reach|write)[\s:]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                        r'mailto:([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                    ]
                    
                    all_emails = set()
                    for pattern in email_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        for match in matches:
                            email = match if isinstance(match, str) else match[0] if isinstance(match, tuple) else str(match)
                            if self._is_valid_email(email.lower()):
                                all_emails.add(email.lower())
                    
                    if all_emails:
                        best_email = self._select_best_email(list(all_emails))
                
                # Ultimate social regex
                if not best_social:
                    social_pattern = r'https?://(?:www\.)?(?:instagram|facebook|twitter|x)\.com/[^\s<>"\']+' 
                    social_matches = re.findall(social_pattern, content, re.IGNORECASE)
                    valid_socials = [s for s in social_matches[:5] if self._is_valid_social_url(s)]
                    if valid_socials:
                        best_social = self._select_best_social(valid_socials)
        
        return {
            'email': best_email,
            'social': best_social
        }
    
    def _is_valid_email(self, email: str) -> bool:
        """Ultimate email validation."""
        if not email or len(email) < 5 or len(email) > 50:
            return False
        
        # Enhanced blacklist
        blacklist = ['.svg', '.png', '.jpg', '.jpeg', '.gif', '.css', '.js', '.pdf', '.doc', '.zip']
        if any(ext in email for ext in blacklist):
            return False
        
        # Must have exactly one @
        if '@' not in email or email.count('@') != 1:
            return False
        
        # Enhanced format validation
        return bool(re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email))
    
    def _is_valid_social_url(self, url: str) -> bool:
        """Ultimate social media URL validation."""
        if not url or len(url) < 10:
            return False
        
        platforms = ['instagram.com', 'facebook.com', 'twitter.com', 'x.com']
        if not any(platform in url.lower() for platform in platforms):
            return False
        
        # Avoid post/photo/status URLs
        avoid_patterns = ['/p/', '/posts/', '/photo/', '/status/', '/tweet/', '/reel/']
        if any(pattern in url.lower() for pattern in avoid_patterns):
            return False
        
        return True
    
    def _select_best_email(self, emails: List[str]) -> Optional[Dict]:
        """Ultimate email selection with enhanced priority."""
        if not emails:
            return None
        
        # Enhanced priority prefixes
        priority_prefixes = ['contact', 'info', 'hello', 'admin', 'support', 'talk', 'team']
        
        for prefix in priority_prefixes:
            for email in emails:
                if email.startswith(f"{prefix}@"):
                    return {"email": email, "confidence": 0.95}
        
        # Return shortest, most professional email
        best = min(emails, key=lambda x: (len(x), x.count('.'), 'noreply' in x))
        confidence = 0.85 if 'noreply' not in best else 0.70
        return {"email": best, "confidence": confidence}
    
    def _select_best_social(self, social_links: List[str]) -> Optional[Dict]:
        """Ultimate social media selection."""
        if not social_links:
            return None
        
        # Platform priority
        priority = ['instagram.com', 'facebook.com', 'twitter.com', 'x.com']
        
        for platform in priority:
            for link in social_links:
                if platform in link.lower():
                    platform_name = platform.split('.')[0]
                    if platform_name == 'x':
                        platform_name = 'twitter'  # Normalize X to Twitter
                    handle = self._extract_handle(link)
                    return {
                        "platform": platform_name,
                        "url": link,
                        "handle": handle,
                        "confidence": 0.90
                    }
        
        return None
    
    def _extract_handle(self, url: str) -> Optional[str]:
        """Extract social media handle."""
        try:
            parsed = urlparse(url)
            path_parts = [p for p in parsed.path.strip('/').split('/') if p]
            if path_parts:
                return f"@{path_parts[0]}"
        except:
            pass
        return None
    
    def export_to_csv(self, results: List[Dict], filename: str):
        """Export results to CSV with ultimate details."""
        if not results:
            return
        
        rows = []
        for result in results:
            if 'error' in result:
                continue
            
            email = result.get('email', {})
            social = result.get('social_media', {})
            
            row = {
                'url': result.get('url', ''),
                'email': email.get('email', '') if email else '',
                'email_confidence': email.get('confidence', '') if email else '',
                'social_platform': social.get('platform', '') if social else '',
                'social_handle': social.get('handle', '') if social else '',
                'social_url': social.get('url', '') if social else '',
                'social_confidence': social.get('confidence', '') if social else '',
                'pages_discovered': result.get('pages_discovered', 0),
                'pages_checked': result.get('pages_checked', 0),
                'efficiency': result.get('efficiency', ''),
                'timestamp': result.get('timestamp', '')
            }
            rows.append(row)
        
        if rows:
            fieldnames = list(rows[0].keys())
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(rows)
            print(f"📊 Results exported to {filename}")
    
    def print_summary(self, results: List[Dict]):
        """Print ultimate extraction summary."""
        total = len(results)
        successful = len([r for r in results if 'error' not in r])
        errors = total - successful
        
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        
        total_discovered = sum(r.get('pages_discovered', 0) for r in results if 'error' not in r)
        total_checked = sum(r.get('pages_checked', 0) for r in results if 'error' not in r)
        
        avg_discovered = total_discovered / successful if successful > 0 else 0
        avg_checked = total_checked / successful if successful > 0 else 0
        efficiency = (1 - (total_checked / total_discovered)) * 100 if total_discovered > 0 else 0
        
        print(f"\n📊 ULTIMATE EXTRACTION SUMMARY:")
        print(f"   • Total URLs: {total}")
        print(f"   • Successful: {successful}")
        print(f"   • Errors: {errors}")
        
        if successful > 0:
            print(f"   • Emails found: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)")
            print(f"   • Social media found: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)")
            print(f"   • Both found: {len([r for r in results if 'error' not in r and r.get('email') and r.get('social_media')])}/{successful}")
            print(f"   • Average pages discovered: {avg_discovered:.1f}")
            print(f"   • Average pages checked: {avg_checked:.1f}")
            print(f"   • Efficiency gain: {efficiency:.1f}% fewer pages checked than discovered")


# Ultimate function for production use
async def extract_contacts_ultimate(urls: Union[str, List[str]], 
                                  batch_size: int = 50,
                                  max_concurrent: int = 6) -> List[Dict]:
    """
    Ultimate contact extraction with smart discovery and early stopping.
    
    Args:
        urls: URLs to process
        batch_size: URLs per batch
        max_concurrent: Concurrent requests per batch
        
    Returns:
        List of extraction results
    """
    extractor = UltimateContactExtractor(batch_size=batch_size, max_concurrent=max_concurrent)
    return await extractor.extract_ultimate(urls)
