[package]
name = "reacher_sqs"
version = "0.11.6"
edition = "2018"
license = "AGPL-3.0"
publish = false

[dependencies]
check-if-email-exists = { path = "../core", features = ["sentry"] }
lambda_runtime = "0.13"
reacher_backend = { path = "../backend" }
reqwest = { version = "0.12.15", default-features = false, features = [
    "rustls-tls",
] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.40", features = ["macros"] }
tracing = "0.1.40"
tracing-subscriber = { version = "0.3.18", features = ["json"] }
