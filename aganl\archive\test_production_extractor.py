"""
Test the production-ready fast extractor
"""

import asyncio
import csv
from datetime import datetime
from typing import List
from production_fast_extractor import ProductionFastExtractor


def load_test_urls() -> List[str]:
    """Load URLs from test_data.csv"""
    urls = []
    try:
        with open("test_data.csv", 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                url = row.get('website_url', '').strip()
                if url and url.startswith(('http://', 'https://')):
                    urls.append(url)
        return urls
    except Exception as e:
        print(f"Error loading URLs: {e}")
        return []


async def test_production_extractor():
    """Test the production extractor with all URLs."""
    print("🏭 PRODUCTION FAST EXTRACTOR TEST")
    print("=" * 60)
    print("Optimized for:")
    print("• Stability with large batches")
    print("• Controlled concurrency")
    print("• Fast email and social extraction only")
    print("• Production reliability")
    print("=" * 60)
    
    urls = load_test_urls()
    if not urls:
        print("No URLs found")
        return
    
    print(f"🎯 Processing {len(urls)} URLs with production extractor...")
    
    start_time = datetime.now()
    
    # Production settings: smaller batches, lower concurrency for stability
    extractor = ProductionFastExtractor(batch_size=25, max_concurrent=4)
    results = await extractor.extract_production(urls)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # Print summary
    extractor.print_summary(results)
    
    # Show some successful results
    successful_results = [r for r in results if 'error' not in r and (r.get('email') or r.get('social_media'))]
    
    if successful_results:
        print(f"\n🏆 SAMPLE SUCCESSFUL EXTRACTIONS:")
        for result in successful_results[:10]:
            url = result['url']
            email = result.get('email')
            social = result.get('social_media')
            
            domain = url.split('/')[2].replace('www.', '')
            
            email_text = email['email'] if email else "None"
            social_text = f"{social['platform']} {social.get('handle', '')}" if social else "None"
            
            print(f"   • {domain}")
            print(f"     📧 {email_text}")
            print(f"     🌐 {social_text}")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"production_results_{timestamp}.csv"
    extractor.export_to_csv(results, filename)
    
    print(f"\n🎉 PRODUCTION TEST COMPLETED!")
    print(f"   • Results exported to: {filename}")
    print(f"   • Ready for thousands of URLs!")
    
    return results


async def quick_test():
    """Quick test with just a few URLs."""
    print("⚡ QUICK PRODUCTION TEST")
    print("=" * 40)
    
    # Test with known good URLs
    test_urls = [
        "https://thejamescafe.com/",
        "https://convivecoffee.com/",
        "http://www.yinzcoffee.com/",
        "http://www.grimwizard.com/"
    ]
    
    print(f"Testing {len(test_urls)} URLs...")
    
    extractor = ProductionFastExtractor(batch_size=10, max_concurrent=3)
    results = await extractor.extract_production(test_urls)
    
    print(f"\n📧 QUICK TEST RESULTS:")
    for i, result in enumerate(results, 1):
        if 'error' in result:
            print(f"{i}. ❌ {result['url']}: {result['error']}")
            continue
        
        url = result['url']
        email = result.get('email')
        social = result.get('social_media')
        
        domain = url.split('/')[2].replace('www.', '')
        
        print(f"\n{i}. 🏪 {domain}")
        if email:
            print(f"   📧 {email['email']} (confidence: {email['confidence']:.1%})")
        else:
            print(f"   📧 No email found")
        
        if social:
            print(f"   🌐 {social['platform'].title()}: {social.get('handle', social['url'])}")
        else:
            print(f"   🌐 No social media found")
    
    extractor.print_summary(results)
    
    return results


async def main():
    """Main test function."""
    print("🏭 PRODUCTION FAST EXTRACTOR TESTS")
    print("Stable, reliable, ready for thousands of URLs")
    print("=" * 60)
    
    print("\nChoose test mode:")
    print("1. Quick test (4 URLs)")
    print("2. Full production test (all URLs)")
    
    try:
        choice = input("\nEnter choice (1-2) or press Enter for option 1: ").strip()
        if not choice:
            choice = "1"
    except KeyboardInterrupt:
        print("\n\nTest cancelled.")
        return
    
    try:
        if choice == "1":
            await quick_test()
        elif choice == "2":
            await test_production_extractor()
        else:
            print("Invalid choice. Running quick test.")
            await quick_test()
    
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🎉 Production extractor testing completed!")
    print("\nProduction features:")
    print("✅ Batch processing for stability")
    print("✅ Controlled concurrency (no browser crashes)")
    print("✅ Timeout handling")
    print("✅ Error recovery")
    print("✅ Fast email and social extraction")
    print("✅ Ready for thousands of URLs")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n🏭 Production test interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
