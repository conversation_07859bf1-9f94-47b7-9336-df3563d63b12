{"db_name": "PostgreSQL", "query": "\n\t\t\t\t\tINSERT INTO v1_task_result (payload, job_id, extra, error)\n\t\t\t\t\tVALUES ($1, $2, $3, $4)\n\t\t\t\t\tRETURNING id\n\t\t\t\t\t", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Int4"}], "parameters": {"Left": ["Jsonb", "Int4", "Jsonb", "Text"]}, "nullable": [false]}, "hash": "068521cf9e77f563b3791cce500d95660c56e852770a4eac47576089e704322a"}