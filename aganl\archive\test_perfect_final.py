"""
Test the perfect contact extractor - the ultimate solution
"""

import asyncio
from perfect_contact_extractor import PerfectContactExtractor


async def test_perfect_extractor():
    """Test the perfect extractor that combines guaranteed coverage with smart stopping."""
    print("⭐ PERFECT EXTRACTOR TEST")
    print("Guaranteed coverage + smart stopping")
    print("=" * 50)
    
    test_urls = [
        "https://www.defer.coffee/strip-district",
        "https://thejamescafe.com/",
        "https://convivecoffee.com/",
        "http://www.yinzcoffee.com/"
    ]
    
    extractor = PerfectContactExtractor(batch_size=5, max_concurrent=3)
    results = await extractor.extract_perfect(test_urls)
    
    print(f"\nPerfect Results:")
    for i, result in enumerate(results, 1):
        if 'error' not in result:
            url = result['url']
            email = result.get('email')
            social = result.get('social_media')
            pages_available = result.get('pages_available', 0)
            pages_checked = result.get('pages_checked', 0)
            
            domain = url.split('/')[2].replace('www.', '')
            
            print(f"{i}. {domain}: {pages_checked}/{pages_available} pages checked")
            if email:
                print(f"   📧 {email['email']} (confidence: {email['confidence']:.1%})")
            if social:
                print(f"   🌐 {social['platform']} {social.get('handle', '')}")
            if not email and not social:
                print(f"   ❌ No contacts found")
        else:
            print(f"{i}. Error: {result['error']}")
    
    # Check Defer Coffee specifically
    defer_result = next((r for r in results if 'defer.coffee' in r['url']), None)
    if defer_result and defer_result.get('email'):
        print(f"\n✅ SUCCESS: Defer Coffee email found: {defer_result['email']['email']}")
    else:
        print(f"\n❌ ISSUE: Defer Coffee email still missing")
    
    # Summary
    extractor.print_summary(results)
    
    return results


async def main():
    """Main test function."""
    try:
        await test_perfect_extractor()
        
        print(f"\n⭐ PERFECT EXTRACTOR FEATURES:")
        print("✅ Guaranteed page checking (main + contact + contact-us + about)")
        print("✅ Smart early stopping when both contacts found")
        print("✅ Comprehensive extraction (CSS + regex)")
        print("✅ Perfect for production use")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⭐ Perfect test interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
