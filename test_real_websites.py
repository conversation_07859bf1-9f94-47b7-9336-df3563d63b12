"""
Test script to test scraping with real websites from the Pittsburgh dataset.
"""

import asyncio
import pandas as pd
from datetime import datetime
from aganl.perfect_contact_extractor import PerfectContactExtractor

async def test_real_websites():
    """Test scraping with real websites from the dataset."""
    print("🔍 TESTING REAL WEBSITES FROM DATASET")
    print("=" * 50)
    
    # Load some real URLs from the dataset
    try:
        df = pd.read_csv("pittsburgh_coffee_prospects_FINAL_20250914_164553.csv")
        urls = df['website'].dropna().head(10).tolist()  # Test first 10 URLs
        print(f"Loaded {len(urls)} URLs from dataset")
        
        for i, url in enumerate(urls):
            print(f"  {i+1}. {url}")
        
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return
    
    print(f"\n🚀 Testing with conservative settings...")
    
    # Test with very conservative settings first
    extractor = PerfectContactExtractor(batch_size=1, max_concurrent=1)
    
    try:
        results = await extractor.extract_perfect(urls)
        
        print(f"\n📊 RESULTS:")
        success_count = 0
        error_count = 0
        error_types = {}
        
        for result in results:
            url = result.get('url', 'Unknown')
            if 'error' in result:
                error_count += 1
                error = result['error']
                print(f"❌ {url}")
                print(f"   Error: {error}")
                
                # Categorize error types
                if 'DNS resolution failed' in error:
                    error_types['DNS Issues'] = error_types.get('DNS Issues', 0) + 1
                elif 'Connection failed' in error or 'Connection aborted' in error:
                    error_types['Connection Issues'] = error_types.get('Connection Issues', 0) + 1
                elif 'timeout' in error.lower():
                    error_types['Timeouts'] = error_types.get('Timeouts', 0) + 1
                elif 'SSL' in error:
                    error_types['SSL Issues'] = error_types.get('SSL Issues', 0) + 1
                else:
                    error_types['Other'] = error_types.get('Other', 0) + 1
            else:
                success_count += 1
                print(f"✅ {url}")
                if result.get('email'):
                    print(f"   Email: {result['email']}")
                if result.get('phone'):
                    print(f"   Phone: {result['phone']}")
                print(f"   Pages checked: {result.get('pages_checked', 0)}")
        
        print(f"\n📈 SUMMARY:")
        print(f"✅ Successful: {success_count}")
        print(f"❌ Failed: {error_count}")
        print(f"Success rate: {(success_count / len(results)) * 100:.1f}%")
        
        if error_types:
            print(f"\n🔍 ERROR BREAKDOWN:")
            for error_type, count in error_types.items():
                print(f"   • {error_type}: {count}")
        
        return results
        
    except Exception as e:
        print(f"❌ CRITICAL ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

async def test_high_concurrency():
    """Test with higher concurrency to see if that causes issues."""
    print("\n🔍 TESTING HIGH CONCURRENCY")
    print("=" * 50)
    
    # Load a smaller set for concurrency testing
    try:
        df = pd.read_csv("pittsburgh_coffee_prospects_FINAL_20250914_164553.csv")
        urls = df['website'].dropna().head(5).tolist()  # Test 5 URLs
        print(f"Testing {len(urls)} URLs with high concurrency...")
        
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return
    
    # Test with the same settings as the overnight script
    extractor = PerfectContactExtractor(batch_size=15, max_concurrent=20)
    
    try:
        results = await extractor.extract_perfect(urls)
        
        error_count = len([r for r in results if 'error' in r])
        success_count = len([r for r in results if 'error' not in r])
        
        print(f"\n📈 HIGH CONCURRENCY RESULTS:")
        print(f"✅ Successful: {success_count}")
        print(f"❌ Failed: {error_count}")
        
        if error_count > 0:
            print(f"\nErrors with high concurrency:")
            for result in results:
                if 'error' in result:
                    print(f"   • {result['url']}: {result['error']}")
        
        return results
        
    except Exception as e:
        print(f"❌ HIGH CONCURRENCY ERROR: {str(e)}")
        return None

async def main():
    """Run all tests."""
    print(f"🚀 REAL WEBSITE SCRAPING TEST")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Test 1: Conservative settings with real websites
    results1 = await test_real_websites()
    
    # Test 2: High concurrency test
    results2 = await test_high_concurrency()
    
    print(f"\n✅ TESTING COMPLETE")
    
    if results1 and results2:
        # Compare results
        errors1 = len([r for r in results1 if 'error' in r])
        errors2 = len([r for r in results2 if 'error' in r])
        
        print(f"\n🔍 COMPARISON:")
        print(f"Conservative settings errors: {errors1}/{len(results1)}")
        print(f"High concurrency errors: {errors2}/{len(results2)}")
        
        if errors2 > errors1:
            print("⚠️  High concurrency appears to cause more errors!")
        elif errors1 > 0:
            print("⚠️  Some websites are inherently problematic")
        else:
            print("✅ No issues detected - script should work fine")

if __name__ == "__main__":
    asyncio.run(main())
