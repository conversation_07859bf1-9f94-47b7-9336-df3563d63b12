"""
Results Combiner - Combine chunk results into final CSV files
"""

import pandas as pd
import os
import json
from datetime import datetime
from typing import Dict, List

class ResultsCombiner:
    """Combine chunk results into final output files."""
    
    def __init__(self, results_dir: str):
        self.results_dir = results_dir
    
    def combine_results(self, output_prefix: str = None) -> Dict:
        """
        Combine all chunk results into final CSV files.
        
        Args:
            output_prefix: Prefix for output files
            
        Returns:
            Dictionary with combination statistics
        """
        print(f"🔗 COMBINING RESULTS FROM: {self.results_dir}")
        print("=" * 60)
        
        # Find all result files
        result_files = [
            f for f in os.listdir(self.results_dir) 
            if f.startswith('results_chunk_') and f.endswith('.csv')
        ]
        
        if not result_files:
            print("❌ No result files found!")
            return {}
        
        result_files.sort()  # Ensure proper order
        print(f"📄 Found {len(result_files)} result files")
        
        # Load and combine all results
        print("\n📖 Loading and combining results...")
        all_results = []
        
        for i, file in enumerate(result_files, 1):
            file_path = os.path.join(self.results_dir, file)
            chunk_df = pd.read_csv(file_path)
            all_results.append(chunk_df)
            print(f"   ✅ Loaded {file}: {len(chunk_df)} records")
        
        # Combine all dataframes
        combined_df = pd.concat(all_results, ignore_index=True)
        total_records = len(combined_df)
        
        print(f"\n📊 Combined dataset: {total_records:,} records")
        
        # Analyze results
        print("\n🔍 Analyzing results...")
        
        # Count successful extractions
        successful_extractions = combined_df['extraction_success'].sum()
        failed_extractions = total_records - successful_extractions
        
        # Count contacts found
        emails_found = (combined_df['extracted_email'] != '').sum()
        phones_found = (combined_df['extracted_phone'] != '').sum()
        facebook_found = (combined_df['extracted_social_facebook'] != '').sum()
        instagram_found = (combined_df['extracted_social_instagram'] != '').sum()
        twitter_found = (combined_df['extracted_social_twitter'] != '').sum()
        
        # Companies with any contact info
        has_email = combined_df['extracted_email'] != ''
        has_phone = combined_df['extracted_phone'] != ''
        has_any_contact = has_email | has_phone
        companies_with_contacts = has_any_contact.sum()
        companies_without_contacts = total_records - companies_with_contacts
        
        print(f"   • Successful extractions: {successful_extractions:,} ({successful_extractions/total_records*100:.1f}%)")
        print(f"   • Failed extractions: {failed_extractions:,} ({failed_extractions/total_records*100:.1f}%)")
        print(f"   • Emails found: {emails_found:,} ({emails_found/total_records*100:.1f}%)")
        print(f"   • Phones found: {phones_found:,} ({phones_found/total_records*100:.1f}%)")
        print(f"   • Facebook found: {facebook_found:,}")
        print(f"   • Instagram found: {instagram_found:,}")
        print(f"   • Twitter found: {twitter_found:,}")
        print(f"   • Companies with contacts: {companies_with_contacts:,} ({companies_with_contacts/total_records*100:.1f}%)")
        print(f"   • Companies without contacts: {companies_without_contacts:,} ({companies_without_contacts/total_records*100:.1f}%)")
        
        # Generate output filenames
        if output_prefix is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_prefix = f"pittsburgh_prospects_extracted_{timestamp}"
        
        # Create output files
        print(f"\n💾 Creating output files...")
        
        # 1. Complete results file
        complete_file = f"{output_prefix}_complete.csv"
        combined_df.to_csv(complete_file, index=False)
        print(f"   ✅ Complete results: {complete_file} ({total_records:,} records)")
        
        # 2. Companies with emails
        companies_with_emails = combined_df[has_email].copy()
        if len(companies_with_emails) > 0:
            emails_file = f"{output_prefix}_with_emails.csv"
            companies_with_emails.to_csv(emails_file, index=False)
            print(f"   📧 Companies with emails: {emails_file} ({len(companies_with_emails):,} records)")
        else:
            emails_file = None
            print("   📧 No companies with emails found")
        
        # 3. Companies with phones only (no emails)
        companies_with_phones_only = combined_df[has_phone & ~has_email].copy()
        if len(companies_with_phones_only) > 0:
            phones_file = f"{output_prefix}_phones_only.csv"
            companies_with_phones_only.to_csv(phones_file, index=False)
            print(f"   📞 Companies with phones only: {phones_file} ({len(companies_with_phones_only):,} records)")
        else:
            phones_file = None
            print("   📞 No companies with phones only found")
        
        # 4. Companies without any contact info
        companies_no_contacts = combined_df[~has_any_contact].copy()
        if len(companies_no_contacts) > 0:
            no_contacts_file = f"{output_prefix}_no_contacts.csv"
            companies_no_contacts.to_csv(no_contacts_file, index=False)
            print(f"   ❌ Companies without contacts: {no_contacts_file} ({len(companies_no_contacts):,} records)")
        else:
            no_contacts_file = None
            print("   ❌ All companies have some contact info!")
        
        # Create summary report
        summary = {
            'combination_completed_at': datetime.now().isoformat(),
            'results_directory': self.results_dir,
            'chunk_files_processed': len(result_files),
            'total_records': total_records,
            'successful_extractions': int(successful_extractions),
            'failed_extractions': int(failed_extractions),
            'contacts_found': {
                'emails': int(emails_found),
                'phones': int(phones_found),
                'facebook': int(facebook_found),
                'instagram': int(instagram_found),
                'twitter': int(twitter_found)
            },
            'segmentation': {
                'companies_with_emails': int(emails_found),
                'companies_with_phones_only': int(len(companies_with_phones_only)),
                'companies_without_contacts': int(companies_without_contacts)
            },
            'output_files': {
                'complete_results': complete_file,
                'companies_with_emails': emails_file,
                'companies_phones_only': phones_file,
                'companies_no_contacts': no_contacts_file
            }
        }
        
        # Save summary
        summary_file = f"{output_prefix}_summary.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"   📋 Summary report: {summary_file}")
        
        print(f"\n🎉 COMBINATION COMPLETED!")
        print(f"   • Total records processed: {total_records:,}")
        print(f"   • Lead files created based on your preferences:")
        if emails_file:
            print(f"     📧 Companies with emails: {emails_file}")
        if phones_file:
            print(f"     📞 Companies with phones only: {phones_file}")
        print(f"   • Complete dataset: {complete_file}")
        
        return summary

def main():
    """Main function."""
    # Look for results directories
    results_dirs = [d for d in os.listdir('.') if d.endswith('_results') and os.path.isdir(d)]
    
    if not results_dirs:
        print("❌ No results directories found!")
        print("Please run pausable_scraper.py first to generate results.")
        return
    
    if len(results_dirs) > 1:
        print("🤔 Multiple results directories found:")
        for i, dir_name in enumerate(results_dirs, 1):
            print(f"   {i}. {dir_name}")
        
        try:
            choice = int(input("Select directory number: ")) - 1
            results_dir = results_dirs[choice]
        except (ValueError, IndexError):
            print("Invalid choice. Using first directory.")
            results_dir = results_dirs[0]
    else:
        results_dir = results_dirs[0]
    
    print(f"📂 Using results directory: {results_dir}")
    
    # Ask for output prefix
    output_prefix = input("Enter output file prefix (or press Enter for default): ").strip()
    if not output_prefix:
        output_prefix = None
    
    # Combine results
    combiner = ResultsCombiner(results_dir)
    try:
        summary = combiner.combine_results(output_prefix)
        
        if summary:
            print(f"\n✅ SUCCESS! Results combined and segmented according to your preferences.")
            
    except Exception as e:
        print(f"❌ Error combining results: {str(e)}")

if __name__ == "__main__":
    main()
