"""
Quick test of the ultimate extractor with improved fallback logic
"""

import asyncio
from ultimate_contact_extractor import UltimateContactExtractor


async def quick_ultimate_test():
    """Quick test to verify the ultimate extractor works properly."""
    print("🚀 QUICK ULTIMATE TEST")
    print("Testing improved fallback logic")
    print("=" * 50)
    
    test_urls = [
        "https://www.defer.coffee/strip-district",
        "https://thejamescafe.com/",
        "https://convivecoffee.com/"
    ]
    
    extractor = UltimateContactExtractor(batch_size=5, max_concurrent=3)
    results = await extractor.extract_ultimate(test_urls)
    
    print(f"\nResults:")
    for i, result in enumerate(results, 1):
        if 'error' not in result:
            url = result['url']
            email = result.get('email')
            social = result.get('social_media')
            pages_discovered = result.get('pages_discovered', 0)
            pages_checked = result.get('pages_checked', 0)
            
            domain = url.split('/')[2].replace('www.', '')
            
            print(f"{i}. {domain}: {pages_discovered} discovered, {pages_checked} checked")
            if email:
                print(f"   📧 {email['email']}")
            if social:
                print(f"   🌐 {social['platform']} {social.get('handle', '')}")
            if not email and not social:
                print(f"   ❌ No contacts found")
        else:
            print(f"{i}. Error: {result['error']}")
    
    # Check if Defer Coffee email was found
    defer_result = next((r for r in results if 'defer.coffee' in r['url']), None)
    if defer_result and defer_result.get('email'):
        print(f"\n✅ SUCCESS: Found Defer Coffee email: {defer_result['email']['email']}")
    else:
        print(f"\n❌ ISSUE: Defer Coffee email still not found")
    
    return results


async def main():
    """Main test function."""
    try:
        await quick_ultimate_test()
        
        print(f"\n🎯 ULTIMATE EXTRACTOR STATUS:")
        print("✅ Smart page discovery with robust fallbacks")
        print("✅ Intelligent prioritization")
        print("✅ Early stopping when successful")
        print("✅ Guaranteed fallback to critical pages")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n🚀 Quick test interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
