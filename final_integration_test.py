"""
Final Integration Test - Test all components working together
"""
import asyncio
import os
import json
from chunk_scraper import ChunkedDataset<PERSON>craper

async def final_integration_test():
    print('🧪 FINAL INTEGRATION TEST')
    print('='*60)
    print('Testing all components working together')
    
    try:
        # Clean up any previous test data
        test_results_dir = 'final_test_results'
        if os.path.exists(test_results_dir):
            import shutil
            shutil.rmtree(test_results_dir)
        
        # Initialize scraper
        scraper = ChunkedDatasetScraper(
            chunks_dir='pittsburgh_coffee_prospects_FINAL_20250914_164553_CLEAN_chunks',
            results_dir=test_results_dir,
            batch_size=3,
            max_concurrent=2
        )
        
        print('✅ 1. Scraper initialized successfully')
        
        # Test manifest loading
        manifest = scraper.load_chunk_manifest()
        print(f'✅ 2. Manifest loaded: {len(manifest["chunks"])} chunks found')
        
        # Test progress tracking
        progress = scraper.load_progress()
        print('✅ 3. Progress tracking initialized')
        
        # Test URL extraction
        urls = scraper.get_chunk_urls('chunk_006_of_006.csv')
        print(f'✅ 4. URL extraction: {len(urls)} URLs from chunk 6')
        
        # Test small-scale processing (just 3 URLs)
        test_urls = urls[:3]
        print(f'\\n🔥 Testing extraction with {len(test_urls)} URLs...')
        
        results = await scraper.extractor.extract_perfect(test_urls)
        print(f'✅ 5. Extraction completed: {len(results)} results')
        
        # Analyze results
        successful = [r for r in results if 'error' not in r]
        with_emails = [r for r in successful if r.get('email')]
        with_phones = [r for r in successful if r.get('phone')]
        
        print(f'   • Successful: {len(successful)}/{len(results)}')
        print(f'   • With emails: {len(with_emails)}')
        print(f'   • With phones: {len(with_phones)}')
        
        # Test CSV export
        test_filename = f'{test_results_dir}/integration_test_results.csv'
        scraper.extractor.export_to_csv(results, test_filename)
        
        if os.path.exists(test_filename):
            print('✅ 6. CSV export successful')
            
            # Verify CSV content
            with open(test_filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f'   • CSV has {len(lines)} lines (including header)')
        else:
            print('❌ 6. CSV export failed')
            
        # Test chunk data structure
        mock_chunk_data = {
            'chunk': 997,
            'results': results,
            'urls_processed': len(test_urls),
            'duration': 15.0,
            'rate': len(test_urls) / 15.0
        }
        
        save_info = scraper.save_chunk_results(mock_chunk_data)
        if save_info:
            print('✅ 7. Chunk results saving successful')
            print(f'   • Emails found: {save_info["emails_found"]}')
            print(f'   • Phones found: {save_info["phones_found"]}')
        else:
            print('❌ 7. Chunk results saving failed')
            
        # Test progress update
        progress['completed_chunks'].append(997)
        progress['total_processed'] += len(test_urls)
        progress['total_with_emails'] += len(with_emails)
        progress['total_with_phones'] += len(with_phones)
        scraper.save_progress(progress)
        
        # Verify progress was saved
        updated_progress = scraper.load_progress()
        if 997 in updated_progress['completed_chunks']:
            print('✅ 8. Progress tracking update successful')
        else:
            print('❌ 8. Progress tracking update failed')
            
        # Test file structure
        expected_files = [
            'scraping_progress.json',
            'integration_test_results.csv'
        ]
        
        missing_files = []
        for filename in expected_files:
            if not os.path.exists(f'{test_results_dir}/{filename}'):
                missing_files.append(filename)
                
        if not missing_files:
            print('✅ 9. All expected files created')
        else:
            print(f'❌ 9. Missing files: {missing_files}')
            
        # Final verification
        print('\\n🎉 FINAL INTEGRATION TEST RESULTS:')
        print('='*60)
        print('✅ All core components working correctly!')
        print('✅ Data extraction functional')
        print('✅ CSV export working')
        print('✅ Progress tracking operational')
        print('✅ File management working')
        print('\\n🚀 THE SCRIPT IS READY FOR PRODUCTION USE!')
        
        return True
        
    except Exception as e:
        print(f'❌ Integration test failed: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(final_integration_test())
    if success:
        print('\\n✅ ALL TESTS PASSED - SCRIPT IS READY!')
    else:
        print('\\n❌ TESTS FAILED - NEEDS DEBUGGING')
