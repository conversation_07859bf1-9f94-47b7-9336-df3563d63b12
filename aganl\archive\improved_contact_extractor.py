"""
Improved Contact Information Extractor using crawl4ai's built-in strategies
Focuses on business name, email, phone, address, and social media with proper validation.
"""

import asyncio
import json
import re
import csv
from datetime import datetime
from typing import Dict, List, Optional, Union
from urllib.parse import urlparse, urljoin

from crawl4ai import Async<PERSON>eb<PERSON>raw<PERSON>, CrawlerRunConfig
from crawl4ai import JsonCssExtractionStrategy, RegexExtractionStrategy


class ImprovedContactExtractor:
    """
    Improved contact extractor using crawl4ai's built-in extraction strategies
    with proper validation to avoid false positives.
    """
    
    def __init__(self):
        """Initialize the improved contact extractor."""
        self.validation_patterns = self._setup_validation_patterns()
    
    def _setup_validation_patterns(self) -> Dict:
        """Setup validation patterns to filter out false positives."""
        return {
            'email_blacklist': [
                # Common false positive patterns
                r'.*\.(svg|png|jpg|jpeg|gif|webp|ico|css|js)$',
                r'.*@2x\.(svg|png|jpg|jpeg|gif|webp)$',
                r'.*icon.*@.*\.(svg|png|jpg|jpeg|gif|webp)$',
                r'.*logo.*@.*\.(svg|png|jpg|jpeg|gif|webp)$',
                r'.*image.*@.*\.(svg|png|jpg|jpeg|gif|webp)$',
                # Generic/placeholder emails
                r'^(test|example|sample|demo)@.*',
                r'^.*@(test|example|sample|demo)\..*',
                # Very short or suspicious patterns
                r'^.{1,2}@.*',
                r'^.*@.{1,2}\..*'
            ],
            'phone_patterns': [
                # Valid US phone number patterns (more restrictive)
                r'^\+?1?[-.\s]?\(?([2-9][0-9]{2})\)?[-.\s]?([2-9][0-9]{2})[-.\s]?([0-9]{4})$',  # US format
                r'^\(?([2-9][0-9]{2})\)?[-.\s]?([2-9][0-9]{2})[-.\s]?([0-9]{4})$',  # US without country code
                r'^\+?([1-9]\d{0,2})[-.\s]?\(?(\d{2,4})\)?[-.\s]?(\d{2,4})[-.\s]?(\d{2,6})$'  # International
            ],
            'phone_blacklist': [
                # Patterns that look like phone numbers but aren't
                r'^\d{1,2}\.\d+$',  # Coordinates like 40.4549761
                r'^\d+\.\d{6,}$',   # Long decimal numbers
                r'^[0-9]{1,3}\.[0-9]{6,}$',  # Coordinates
                r'^[0-9]{13,}$',    # Very long numbers (likely not phone)
                r'^[0-9]{1,6}$',    # Too short numbers
            ],
            'address_keywords': [
                'street', 'st', 'avenue', 'ave', 'road', 'rd', 'boulevard', 'blvd',
                'lane', 'ln', 'drive', 'dr', 'court', 'ct', 'place', 'pl', 'way',
                'circle', 'cir', 'square', 'sq', 'suite', 'ste', 'apartment', 'apt'
            ]
        }
    
    async def extract_contacts(self, urls: Union[str, List[str]]) -> List[Dict]:
        """
        Extract contact information from URLs using crawl4ai's built-in strategies.
        
        Args:
            urls: Single URL or list of URLs to extract contacts from
            
        Returns:
            List of dictionaries containing extracted and validated contact information
        """
        if isinstance(urls, str):
            urls = [urls]
        
        results = []
        
        async with AsyncWebCrawler(verbose=True) as crawler:
            for url in urls:
                try:
                    print(f"\n{'='*60}")
                    print(f"Extracting contacts from: {url}")
                    print(f"{'='*60}")
                    
                    # Step 1: Extract using RegexExtractionStrategy for common patterns
                    regex_result = await self._extract_with_regex(crawler, url)
                    
                    # Step 2: Extract using CSS strategy for structured data
                    css_result = await self._extract_with_css(crawler, url)
                    
                    # Step 3: Get basic page info
                    basic_result = await crawler.arun(url=url, bypass_cache=True)
                    
                    # Step 4: Combine and validate results
                    combined_data = self._combine_and_validate_results(
                        regex_result, css_result, basic_result, url
                    )
                    
                    results.append(combined_data)
                    
                    # Display results
                    self._display_extraction_results(combined_data)
                    
                except Exception as e:
                    print(f"Error extracting from {url}: {str(e)}")
                    results.append({
                        "url": url,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat(),
                        "contacts": {}
                    })
        
        return results
    
    async def _extract_with_regex(self, crawler: AsyncWebCrawler, url: str) -> Dict:
        """Extract using crawl4ai's RegexExtractionStrategy."""
        
        # Use crawl4ai's built-in regex patterns
        strategy = RegexExtractionStrategy(
            pattern=(
                RegexExtractionStrategy.Email |
                RegexExtractionStrategy.PhoneUS |
                RegexExtractionStrategy.PhoneIntl |
                RegexExtractionStrategy.Url
            )
        )
        
        config = CrawlerRunConfig(extraction_strategy=strategy)
        
        result = await crawler.arun(url=url, config=config)
        
        extracted_data = {"emails": [], "phones": [], "urls": []}
        
        if result.success and result.extracted_content:
            try:
                data = json.loads(result.extracted_content)
                for item in data:
                    label = item.get('label', '').lower()
                    value = item.get('value', '')
                    
                    if 'email' in label:
                        extracted_data["emails"].append(value)
                    elif 'phone' in label:
                        extracted_data["phones"].append(value)
                    elif 'url' in label:
                        extracted_data["urls"].append(value)
                        
            except json.JSONDecodeError:
                print("Failed to parse regex extraction results")
        
        return extracted_data
    
    async def _extract_with_css(self, crawler: AsyncWebCrawler, url: str) -> Dict:
        """Extract using CSS selectors for structured contact information."""
        
        schema = {
            "name": "Contact Information",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "business_name",
                    "selector": "h1, .company-name, .business-name, .site-title, title",
                    "type": "text"
                },
                {
                    "name": "contact_emails",
                    "selector": "a[href^='mailto:']",
                    "type": "list",
                    "fields": [{"name": "email", "type": "attribute", "attribute": "href"}]
                },
                {
                    "name": "contact_phones",
                    "selector": "a[href^='tel:']",
                    "type": "list", 
                    "fields": [{"name": "phone", "type": "attribute", "attribute": "href"}]
                },
                {
                    "name": "addresses",
                    "selector": ".address, .location, [itemtype*='PostalAddress'], .contact-address",
                    "type": "list",
                    "fields": [{"name": "address", "type": "text"}]
                },
                {
                    "name": "social_links",
                    "selector": "a[href*='facebook.com'], a[href*='twitter.com'], a[href*='instagram.com'], a[href*='linkedin.com'], a[href*='youtube.com']",
                    "type": "list",
                    "fields": [{"name": "url", "type": "attribute", "attribute": "href"}]
                }
            ]
        }
        
        strategy = JsonCssExtractionStrategy(schema, verbose=True)
        config = CrawlerRunConfig(extraction_strategy=strategy)
        
        result = await crawler.arun(url=url, config=config)
        
        extracted_data = {}
        
        if result.success and result.extracted_content:
            try:
                data = json.loads(result.extracted_content)
                if data and len(data) > 0:
                    extracted_data = data[0]  # Take first result
            except json.JSONDecodeError:
                print("Failed to parse CSS extraction results")
        
        return extracted_data
    
    def _combine_and_validate_results(self, regex_data: Dict, css_data: Dict, 
                                    basic_result, url: str) -> Dict:
        """Combine results from different strategies and validate them."""
        
        # Get page title
        page_title = getattr(basic_result, 'title', '') or css_data.get('business_name', [''])[0] if css_data.get('business_name') else ''
        
        # Combine and validate emails
        all_emails = set()
        
        # From regex
        all_emails.update(regex_data.get("emails", []))
        
        # From CSS (clean mailto: prefix)
        css_emails = css_data.get("contact_emails", [])
        for email_item in css_emails:
            if isinstance(email_item, dict):
                email = email_item.get("email", "")
                if email.startswith("mailto:"):
                    email = email[7:]  # Remove mailto: prefix
                all_emails.add(email)
        
        # Validate emails
        validated_emails = self._validate_emails(list(all_emails))
        
        # Combine and validate phones
        all_phones = set()
        
        # From regex
        all_phones.update(regex_data.get("phones", []))
        
        # From CSS (clean tel: prefix)
        css_phones = css_data.get("contact_phones", [])
        for phone_item in css_phones:
            if isinstance(phone_item, dict):
                phone = phone_item.get("phone", "")
                if phone.startswith("tel:"):
                    phone = phone[4:]  # Remove tel: prefix
                all_phones.add(phone)
        
        # Validate phones
        validated_phones = self._validate_phones(list(all_phones))
        
        # Process addresses
        addresses = []
        css_addresses = css_data.get("addresses", [])
        for addr_item in css_addresses:
            if isinstance(addr_item, dict):
                address = addr_item.get("address", "").strip()
                if self._validate_address(address):
                    addresses.append({
                        "address": address,
                        "confidence": 0.8
                    })
        
        # Process social media
        social_media = []
        css_social = css_data.get("social_links", [])
        for social_item in css_social:
            if isinstance(social_item, dict):
                social_url = social_item.get("url", "")
                platform = self._identify_social_platform(social_url)
                if platform:
                    social_media.append({
                        "platform": platform,
                        "url": social_url,
                        "handle": self._extract_social_handle(social_url)
                    })
        
        return {
            "url": url,
            "timestamp": datetime.now().isoformat(),
            "title": page_title,
            "contacts": {
                "business_name": page_title,
                "emails": validated_emails,
                "phones": validated_phones,
                "addresses": addresses,
                "social_media": social_media
            },
            "metadata": {
                "success": basic_result.success if hasattr(basic_result, 'success') else True,
                "status_code": getattr(basic_result, 'status_code', None),
                "extraction_methods": ["regex", "css"]
            }
        }
    
    def _validate_emails(self, emails: List[str]) -> List[Dict]:
        """Validate email addresses and filter out false positives."""
        validated = []
        
        for email in emails:
            email = email.strip().lower()
            
            # Skip empty emails
            if not email:
                continue
            
            # Check against blacklist patterns
            is_blacklisted = False
            for pattern in self.validation_patterns['email_blacklist']:
                if re.match(pattern, email, re.IGNORECASE):
                    is_blacklisted = True
                    break
            
            if is_blacklisted:
                continue
            
            # Basic email format validation
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if re.match(email_pattern, email):
                validated.append({
                    "email": email,
                    "confidence": 0.9
                })
        
        return validated
    
    def _validate_phones(self, phones: List[str]) -> List[Dict]:
        """Validate phone numbers and filter out false positives."""
        validated = []

        for phone in phones:
            phone = phone.strip()

            if not phone:
                continue

            # Check against blacklist patterns first
            is_blacklisted = False
            for blacklist_pattern in self.validation_patterns['phone_blacklist']:
                if re.match(blacklist_pattern, phone):
                    is_blacklisted = True
                    break

            if is_blacklisted:
                continue

            # Check against valid phone patterns
            for pattern in self.validation_patterns['phone_patterns']:
                if re.match(pattern, phone):
                    validated.append({
                        "phone": phone,
                        "confidence": 0.8
                    })
                    break

        return validated
    
    def _validate_address(self, address: str) -> bool:
        """Validate if text looks like a real address."""
        if not address or len(address.strip()) < 10:
            return False
        
        address_lower = address.lower()
        
        # Check if it contains address keywords
        has_address_keyword = any(keyword in address_lower 
                                for keyword in self.validation_patterns['address_keywords'])
        
        # Check if it has numbers (street numbers)
        has_numbers = bool(re.search(r'\d+', address))
        
        return has_address_keyword and has_numbers
    
    def _identify_social_platform(self, url: str) -> Optional[str]:
        """Identify social media platform from URL."""
        platforms = {
            'facebook.com': 'facebook',
            'twitter.com': 'twitter', 
            'x.com': 'twitter',
            'instagram.com': 'instagram',
            'linkedin.com': 'linkedin',
            'youtube.com': 'youtube',
            'tiktok.com': 'tiktok'
        }
        
        for domain, platform in platforms.items():
            if domain in url.lower():
                return platform
        return None
    
    def _extract_social_handle(self, url: str) -> Optional[str]:
        """Extract social media handle from URL."""
        try:
            parsed = urlparse(url)
            path_parts = parsed.path.strip('/').split('/')
            if path_parts and path_parts[0]:
                return f"@{path_parts[0]}"
        except:
            pass
        return None
    
    def _display_extraction_results(self, result: Dict):
        """Display extraction results in a formatted way."""
        contacts = result.get('contacts', {})
        
        print(f"✅ Page: {result.get('title', 'No title')}")
        
        # Business name
        business_name = contacts.get('business_name', '')
        if business_name:
            print(f"🏢 Business: {business_name}")
        
        # Emails
        emails = contacts.get('emails', [])
        if emails:
            print(f"📧 Found {len(emails)} email(s):")
            for email_info in emails:
                print(f"   • {email_info['email']} (confidence: {email_info['confidence']})")
        
        # Phones
        phones = contacts.get('phones', [])
        if phones:
            print(f"📞 Found {len(phones)} phone(s):")
            for phone_info in phones:
                print(f"   • {phone_info['phone']} (confidence: {phone_info['confidence']})")
        
        # Addresses
        addresses = contacts.get('addresses', [])
        if addresses:
            print(f"📍 Found {len(addresses)} address(es):")
            for addr_info in addresses:
                addr_text = addr_info['address'][:60] + "..." if len(addr_info['address']) > 60 else addr_info['address']
                print(f"   • {addr_text}")
        
        # Social media
        social_media = contacts.get('social_media', [])
        if social_media:
            print(f"🌐 Found {len(social_media)} social media link(s):")
            for social_info in social_media:
                print(f"   • {social_info['platform'].title()}: {social_info.get('handle', social_info['url'])}")
        
        total_contacts = len(emails) + len(phones) + len(addresses) + len(social_media)
        if total_contacts == 0:
            print("⚠️  No contact information found")
        else:
            print(f"✨ Total contact items: {total_contacts}")
    
    def export_to_json(self, results: List[Dict], filename: str):
        """Export results to JSON file."""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"📄 Results exported to {filename}")
    
    def export_to_csv(self, results: List[Dict], filename: str):
        """Export results to CSV file."""
        if not results:
            return
        
        flattened_data = []
        for result in results:
            if 'error' in result:
                continue
                
            base_info = {
                'url': result.get('url', ''),
                'business_name': result.get('contacts', {}).get('business_name', ''),
                'title': result.get('title', ''),
                'timestamp': result.get('timestamp', '')
            }
            
            contacts = result.get('contacts', {})
            
            # Add a summary row
            summary_row = base_info.copy()
            summary_row.update({
                'contact_type': 'SUMMARY',
                'email_count': len(contacts.get('emails', [])),
                'phone_count': len(contacts.get('phones', [])),
                'address_count': len(contacts.get('addresses', [])),
                'social_count': len(contacts.get('social_media', []))
            })
            flattened_data.append(summary_row)
            
            # Add individual contact items
            for email_info in contacts.get('emails', []):
                row = base_info.copy()
                row.update({
                    'contact_type': 'email',
                    'contact_value': email_info['email'],
                    'confidence': email_info['confidence']
                })
                flattened_data.append(row)
            
            for phone_info in contacts.get('phones', []):
                row = base_info.copy()
                row.update({
                    'contact_type': 'phone',
                    'contact_value': phone_info['phone'],
                    'confidence': phone_info['confidence']
                })
                flattened_data.append(row)
            
            for addr_info in contacts.get('addresses', []):
                row = base_info.copy()
                row.update({
                    'contact_type': 'address',
                    'contact_value': addr_info['address'],
                    'confidence': addr_info.get('confidence', 0.8)
                })
                flattened_data.append(row)
        
        if flattened_data:
            # Get all possible fieldnames
            all_fieldnames = set()
            for row in flattened_data:
                all_fieldnames.update(row.keys())
            
            fieldnames = sorted(list(all_fieldnames))
            
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(flattened_data)
            
            print(f"📊 Results exported to {filename}")


# Convenience function
async def extract_contacts_from_urls(urls: Union[str, List[str]]) -> List[Dict]:
    """
    Quick function to extract contacts from URLs.
    
    Args:
        urls: Single URL or list of URLs
        
    Returns:
        List of extraction results
    """
    extractor = ImprovedContactExtractor()
    return await extractor.extract_contacts(urls)
