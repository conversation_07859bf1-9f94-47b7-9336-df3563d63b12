"""
Chunked Dataset Scraper UI
Interactive UI for processing chunks of 100 URLs at a time.
Prevents scraper breakage from long runs.
"""

import asyncio
import pandas as pd
import json
import os
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from datetime import datetime
from pathlib import Path
from threading import Thread
from aganl.perfect_contact_extractor import PerfectContactExtractor


class ChunkScraperUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Chunk Scraper UI - 100 URLs per Chunk")
        self.root.geometry("800x700")
        
        # Configuration
        self.chunks_dir = Path("pittsburgh_coffee_prospects_FINAL_20250914_164553_CLEAN_chunks")
        self.results_dir = Path("chunk_ui_results")
        self.chunk_size = 100  # Process 100 URLs at a time
        self.batch_size = 15   # Smaller batches for stability
        self.max_concurrent = 6  # Conservative concurrency
        
        # Create results directory
        self.results_dir.mkdir(exist_ok=True)
        
        # State variables
        self.current_chunk_data = []
        self.current_mini_chunk = 0
        self.total_mini_chunks = 0
        self.is_processing = False
        self.extractor = None
        
        self.setup_ui()
        self.load_chunk_files()
        
    def setup_ui(self):
        """Set up the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Chunk Scraper UI", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Chunk selection frame
        chunk_frame = ttk.LabelFrame(main_frame, text="Chunk Selection", padding="10")
        chunk_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(chunk_frame, text="Select Chunk File:").grid(row=0, column=0, sticky=tk.W)
        self.chunk_var = tk.StringVar()
        self.chunk_combo = ttk.Combobox(chunk_frame, textvariable=self.chunk_var, width=40, state="readonly")
        self.chunk_combo.grid(row=0, column=1, padx=(10, 0), sticky=(tk.W, tk.E))
        self.chunk_combo.bind('<<ComboboxSelected>>', self.on_chunk_selected)
        
        # Load chunk button
        self.load_btn = ttk.Button(chunk_frame, text="Load Chunk", command=self.load_selected_chunk)
        self.load_btn.grid(row=0, column=2, padx=(10, 0))
        
        # Chunk info frame
        info_frame = ttk.LabelFrame(main_frame, text="Chunk Information", padding="10")
        info_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.info_text = tk.Text(info_frame, height=4, width=70, state=tk.DISABLED)
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # Processing controls frame
        control_frame = ttk.LabelFrame(main_frame, text="Processing Controls", padding="10")
        control_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Mini-chunk selection
        ttk.Label(control_frame, text="Mini-Chunk (100 URLs):").grid(row=0, column=0, sticky=tk.W)
        self.mini_chunk_var = tk.StringVar()
        self.mini_chunk_combo = ttk.Combobox(control_frame, textvariable=self.mini_chunk_var, width=20, state="readonly")
        self.mini_chunk_combo.grid(row=0, column=1, padx=(10, 0))
        
        # Process button
        self.process_btn = ttk.Button(control_frame, text="Process Mini-Chunk", command=self.start_processing)
        self.process_btn.grid(row=0, column=2, padx=(10, 0))
        self.process_btn.config(state=tk.DISABLED)
        
        # Progress bar
        ttk.Label(control_frame, text="Progress:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), padx=(10, 0), pady=(10, 0))
        
        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="Processing Status", padding="10")
        status_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.status_text = scrolledtext.ScrolledText(status_frame, height=15, width=70)
        self.status_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Results frame
        results_frame = ttk.LabelFrame(main_frame, text="Results Summary", padding="10")
        results_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.results_text = tk.Text(results_frame, height=3, width=70, state=tk.DISABLED)
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # Configure grid weights
        main_frame.columnconfigure(1, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        chunk_frame.columnconfigure(1, weight=1)
        control_frame.columnconfigure(1, weight=1)
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(0, weight=1)
        results_frame.columnconfigure(0, weight=1)
        
    def load_chunk_files(self):
        """Load available chunk files."""
        try:
            chunk_files = []
            if self.chunks_dir.exists():
                for file in self.chunks_dir.glob("chunk_*.csv"):
                    chunk_files.append(file.name)
            
            chunk_files.sort()
            self.chunk_combo['values'] = chunk_files
            
            if chunk_files:
                self.chunk_combo.current(0)
                self.log_status(f"Found {len(chunk_files)} chunk files")
            else:
                self.log_status("No chunk files found!")
                
        except Exception as e:
            self.log_status(f"Error loading chunk files: {e}")
    
    def on_chunk_selected(self, event=None):
        """Handle chunk selection."""
        self.process_btn.config(state=tk.DISABLED)
        self.mini_chunk_combo['values'] = []
        self.update_info_display("Select a chunk and click 'Load Chunk'")
    
    def load_selected_chunk(self):
        """Load the selected chunk and split into mini-chunks."""
        try:
            chunk_file = self.chunk_var.get()
            if not chunk_file:
                messagebox.showwarning("Warning", "Please select a chunk file first")
                return
            
            self.log_status(f"Loading chunk: {chunk_file}")
            
            # Load the chunk CSV
            chunk_path = self.chunks_dir / chunk_file
            df = pd.read_csv(chunk_path)
            
            # Extract valid URLs
            urls = df['website'].dropna().tolist()
            valid_urls = [url.strip() for url in urls if isinstance(url, str) and url.strip().startswith(('http://', 'https://'))]
            
            self.current_chunk_data = valid_urls
            self.total_mini_chunks = (len(valid_urls) + self.chunk_size - 1) // self.chunk_size  # Ceiling division
            
            # Create mini-chunk options
            mini_chunk_options = []
            for i in range(self.total_mini_chunks):
                start_idx = i * self.chunk_size
                end_idx = min((i + 1) * self.chunk_size, len(valid_urls))
                mini_chunk_options.append(f"Mini-Chunk {i+1} (URLs {start_idx+1}-{end_idx})")
            
            self.mini_chunk_combo['values'] = mini_chunk_options
            if mini_chunk_options:
                self.mini_chunk_combo.current(0)
                self.process_btn.config(state=tk.NORMAL)
            
            # Update info display
            info_text = f"""Chunk File: {chunk_file}
Total URLs: {len(valid_urls)}
Mini-Chunks: {self.total_mini_chunks} (100 URLs each)
Ready to process!"""
            
            self.update_info_display(info_text)
            self.log_status(f"Loaded {len(valid_urls)} URLs, split into {self.total_mini_chunks} mini-chunks")
            
        except Exception as e:
            self.log_status(f"Error loading chunk: {e}")
            messagebox.showerror("Error", f"Failed to load chunk: {e}")
    
    def start_processing(self):
        """Start processing the selected mini-chunk."""
        if self.is_processing:
            messagebox.showwarning("Warning", "Processing is already in progress")
            return
        
        try:
            mini_chunk_selection = self.mini_chunk_var.get()
            if not mini_chunk_selection:
                messagebox.showwarning("Warning", "Please select a mini-chunk to process")
                return
            
            # Extract mini-chunk index
            mini_chunk_idx = int(mini_chunk_selection.split()[1]) - 1
            
            # Get URLs for this mini-chunk
            start_idx = mini_chunk_idx * self.chunk_size
            end_idx = min((mini_chunk_idx + 1) * self.chunk_size, len(self.current_chunk_data))
            urls_to_process = self.current_chunk_data[start_idx:end_idx]
            
            self.log_status(f"Starting processing of {len(urls_to_process)} URLs...")
            self.process_btn.config(state=tk.DISABLED)
            self.is_processing = True
            self.progress_var.set(0)
            
            # Start processing in a separate thread
            thread = Thread(target=self.process_mini_chunk_thread, args=(urls_to_process, mini_chunk_idx + 1))
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            self.log_status(f"Error starting processing: {e}")
            messagebox.showerror("Error", f"Failed to start processing: {e}")
            self.is_processing = False
            self.process_btn.config(state=tk.NORMAL)
    
    def process_mini_chunk_thread(self, urls, mini_chunk_num):
        """Process mini-chunk in a separate thread."""
        try:
            # Run the async processing
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            results = loop.run_until_complete(self.process_mini_chunk_async(urls, mini_chunk_num))
            loop.close()
            
            # Update UI on main thread
            self.root.after(0, self.processing_completed, results, mini_chunk_num)
            
        except Exception as e:
            self.root.after(0, self.processing_failed, str(e))
    
    async def process_mini_chunk_async(self, urls, mini_chunk_num):
        """Async processing of mini-chunk."""
        try:
            # Initialize extractor with conservative settings
            if not self.extractor:
                self.extractor = PerfectContactExtractor(
                    batch_size=self.batch_size,
                    max_concurrent=self.max_concurrent
                )
            
            self.root.after(0, self.log_status, f"Processing {len(urls)} URLs with conservative settings...")
            self.root.after(0, self.log_status, f"Batch size: {self.batch_size}, Max concurrent: {self.max_concurrent}")
            
            # Process URLs
            start_time = datetime.now()
            results = await self.extractor.extract_perfect(urls)
            end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds()
            rate = len(urls) / duration if duration > 0 else 0
            
            self.root.after(0, self.log_status, f"Processing completed in {duration:.1f}s ({rate:.2f} URLs/sec)")
            
            # Save results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mini_chunk_{mini_chunk_num:03d}_results_{timestamp}.csv"
            filepath = self.results_dir / filename
            
            self.extractor.export_to_csv(results, str(filepath))
            
            # Count results
            successful = [r for r in results if 'error' not in r]
            with_emails = [r for r in successful if r.get('email')]
            with_phones = [r for r in successful if r.get('phone')]
            
            result_summary = {
                'total_urls': len(urls),
                'successful': len(successful),
                'with_emails': len(with_emails),
                'with_phones': len(with_phones),
                'duration': duration,
                'rate': rate,
                'filename': filename
            }
            
            return result_summary
            
        except Exception as e:
            raise e
    
    def processing_completed(self, result_summary, mini_chunk_num):
        """Handle successful processing completion."""
        try:
            self.is_processing = False
            self.process_btn.config(state=tk.NORMAL)
            self.progress_var.set(100)
            
            # Log results
            self.log_status(f"✅ Mini-chunk {mini_chunk_num} completed successfully!")
            self.log_status(f"   • Total URLs: {result_summary['total_urls']}")
            self.log_status(f"   • Successful: {result_summary['successful']}")
            self.log_status(f"   • Emails found: {result_summary['with_emails']}")
            self.log_status(f"   • Phones found: {result_summary['with_phones']}")
            self.log_status(f"   • Processing rate: {result_summary['rate']:.2f} URLs/sec")
            self.log_status(f"   • Results saved: {result_summary['filename']}")
            
            # Update results display
            results_text = f"""Last Processing Results:
URLs Processed: {result_summary['total_urls']} | Successful: {result_summary['successful']}
Emails Found: {result_summary['with_emails']} | Phones Found: {result_summary['with_phones']}
Rate: {result_summary['rate']:.2f} URLs/sec | File: {result_summary['filename']}"""
            
            self.update_results_display(results_text)
            
            messagebox.showinfo("Success", f"Mini-chunk {mini_chunk_num} processed successfully!\n\n"
                                         f"Emails: {result_summary['with_emails']}\n"
                                         f"Phones: {result_summary['with_phones']}\n"
                                         f"Results saved to: {result_summary['filename']}")
            
        except Exception as e:
            self.log_status(f"Error in processing completion: {e}")
    
    def processing_failed(self, error_msg):
        """Handle processing failure."""
        self.is_processing = False
        self.process_btn.config(state=tk.NORMAL)
        self.progress_var.set(0)
        
        self.log_status(f"❌ Processing failed: {error_msg}")
        messagebox.showerror("Processing Failed", f"An error occurred during processing:\n\n{error_msg}")
    
    def log_status(self, message):
        """Add a message to the status log."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.status_text.insert(tk.END, log_message)
        self.status_text.see(tk.END)
        self.root.update_idletasks()
    
    def update_info_display(self, text):
        """Update the info display."""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, text)
        self.info_text.config(state=tk.DISABLED)
    
    def update_results_display(self, text):
        """Update the results display."""
        self.results_text.config(state=tk.NORMAL)
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(1.0, text)
        self.results_text.config(state=tk.DISABLED)


def main():
    root = tk.Tk()
    app = ChunkScraperUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
