# Chunk Scraper UI - 100 URLs per Mini-Chunk

A user-friendly interface for processing your dataset in small, manageable chunks of 100 URLs each. This prevents scraper breakage from long runs and gives you full control over the processing.

## 🚀 Quick Start

### Option 1: Double-click the batch file
```
launch_chunk_ui.bat
```

### Option 2: Run from command line
```bash
python chunk_ui.py
```

## 📋 How It Works

### 1. **Chunk Selection**
- Select any of your 6 original chunk files (chunk_001_of_006.csv, etc.)
- Click "Load Chunk" to split it into mini-chunks of 100 URLs each

### 2. **Mini-Chunk Processing**
- Each original chunk (1000 URLs) becomes 10 mini-chunks (100 URLs each)
- Select which mini-chunk to process from the dropdown
- Click "Process Mini-Chunk" to start scraping

### 3. **Safe Processing**
- **Conservative settings**: 15 URLs per batch, 6 concurrent requests
- **Short runs**: ~2-5 minutes per mini-chunk
- **Immediate saves**: Results saved after each mini-chunk
- **No memory buildup**: Fresh start for each mini-chunk

## 🎯 Benefits

### ✅ **Prevents Scraper Breakage**
- Short processing sessions (100 URLs vs 1000+)
- Conservative concurrency settings
- Fresh browser contexts for each run

### ✅ **Full Control**
- Process one mini-chunk at a time
- See results immediately
- Stop/start whenever you want
- No long overnight runs that might fail

### ✅ **Better Results Tracking**
- Real-time progress display
- Immediate feedback on emails/phones found
- Individual result files for each mini-chunk
- Easy to identify which mini-chunks need reprocessing

## 📊 UI Features

### **Chunk Information Panel**
Shows:
- Selected chunk file
- Total URLs in chunk
- Number of mini-chunks created
- Processing status

### **Processing Controls**
- Mini-chunk selector dropdown
- Process button with safety checks
- Progress bar for current operation
- Conservative processing settings

### **Live Status Log**
- Real-time processing updates
- Timestamped log entries
- Error reporting
- Success confirmations

### **Results Summary**
- URLs processed count
- Emails and phones found
- Processing rate
- Output filename

## 📁 File Organization

### **Input Files**
```
pittsburgh_coffee_prospects_FINAL_20250914_164553_CLEAN_chunks/
├── chunk_001_of_006.csv (1000 URLs)
├── chunk_002_of_006.csv (1000 URLs)
├── chunk_003_of_006.csv (1000 URLs)
├── chunk_004_of_006.csv (1000 URLs)
├── chunk_005_of_006.csv (1000 URLs)
└── chunk_006_of_006.csv (200 URLs)
```

### **Output Files**
```
chunk_ui_results/
├── mini_chunk_001_results_20250919_080000.csv
├── mini_chunk_002_results_20250919_080500.csv
├── mini_chunk_003_results_20250919_081000.csv
└── ... (one file per mini-chunk processed)
```

## 🔧 Managing Results

### **Combine Results**
After processing multiple mini-chunks, use:
```bash
python combine_results.py
```

This will:
- ✅ Combine all mini-chunk results
- ✅ Remove duplicates
- ✅ Create separate files for emails-only and phones-only
- ✅ Generate processing summary
- ✅ Calculate success rates

### **Result Files Created**
- `combined_all_results_TIMESTAMP.csv` - All results
- `combined_emails_only_TIMESTAMP.csv` - Companies with emails
- `combined_phones_only_TIMESTAMP.csv` - Companies with phones  
- `combined_email_and_phone_TIMESTAMP.csv` - Companies with both
- `processing_summary_TIMESTAMP.txt` - Statistics summary

## ⚙️ Processing Settings

### **Optimized for Stability**
- **Mini-chunk size**: 100 URLs (vs 1000 in original)
- **Batch size**: 15 URLs (vs 30 in original)
- **Max concurrent**: 6 requests (vs 12 in original)
- **Processing time**: ~2-5 minutes per mini-chunk

### **Expected Performance**
- **Rate**: ~0.5-1.5 URLs/second
- **Time per mini-chunk**: 2-5 minutes
- **Total time for full dataset**: ~4-10 hours (spread across multiple sessions)

## 🎯 Recommended Workflow

### **Day 1: Process 10-20 mini-chunks**
1. Launch UI: `python chunk_ui.py`
2. Load chunk_001_of_006.csv
3. Process mini-chunks 1-5 (500 URLs)
4. Check results and take a break
5. Process mini-chunks 6-10 (500 URLs)

### **Day 2: Continue with next chunk**
1. Load chunk_002_of_006.csv  
2. Process mini-chunks 1-10 (1000 URLs)
3. Combine results: `python combine_results.py`

### **Repeat until complete**
- Process at your own pace
- No pressure for overnight runs
- Immediate feedback on results
- Easy to resume if interrupted

## 🚨 Troubleshooting

### **If Processing Fails**
1. ✅ **Just restart the UI** - no data lost
2. ✅ **Select the same mini-chunk** - try again
3. ✅ **Results are saved immediately** - no work lost
4. ✅ **Check the status log** - see exact error

### **If UI Won't Start**
1. Check Python is working: `python --version`
2. Check dependencies: `python test_ui.py`
3. Check chunk files exist in the chunks directory

### **If No Results Found**
- This is normal for some URLs (dead sites, no contact info)
- The scraper is working correctly
- Check the status log for details

## 📈 Success Expectations

Based on testing:
- **~15-25% of URLs** will have email addresses
- **~10-20% of URLs** will have phone numbers  
- **~5-10% of URLs** will have both email and phone
- **Some mini-chunks** may find 0 contacts (normal for dead sites)

## 🎉 Advantages Over Original Script

| Feature | Original Script | New UI System |
|---------|----------------|---------------|
| **Chunk Size** | 1000 URLs | 100 URLs |
| **Processing Time** | 30-60 minutes | 2-5 minutes |
| **Failure Recovery** | Restart entire chunk | Restart mini-chunk only |
| **Progress Visibility** | Limited | Real-time updates |
| **Control** | All-or-nothing | Process at your pace |
| **Memory Usage** | Builds up over time | Fresh start each time |
| **Results Safety** | Save at end only | Save immediately |

The new system is much more reliable and user-friendly for processing your large dataset!
