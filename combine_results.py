"""
Results Combiner for Mini-Chunks
Combines multiple mini-chunk result files into consolidated reports.
"""

import pandas as pd
import os
from pathlib import Path
from datetime import datetime
import glob


def combine_mini_chunk_results(results_dir="chunk_ui_results"):
    """Combine all mini-chunk results into consolidated files."""
    results_path = Path(results_dir)
    
    if not results_path.exists():
        print(f"❌ Results directory not found: {results_dir}")
        return
    
    # Find all mini-chunk result files
    result_files = list(results_path.glob("mini_chunk_*_results_*.csv"))
    
    if not result_files:
        print(f"❌ No mini-chunk result files found in {results_dir}")
        return
    
    print(f"📁 Found {len(result_files)} mini-chunk result files")
    
    # Read and combine all results
    all_results = []
    total_urls = 0
    total_emails = 0
    total_phones = 0
    
    for file_path in sorted(result_files):
        try:
            df = pd.read_csv(file_path)
            all_results.append(df)
            
            # Count statistics
            total_urls += len(df)
            total_emails += len(df[df['email'].notna() & (df['email'] != '')])
            total_phones += len(df[df['phone'].notna() & (df['phone'] != '')])
            
            print(f"   ✅ {file_path.name}: {len(df)} URLs")
            
        except Exception as e:
            print(f"   ❌ Error reading {file_path.name}: {e}")
    
    if not all_results:
        print("❌ No valid result files to combine")
        return
    
    # Combine all DataFrames
    combined_df = pd.concat(all_results, ignore_index=True)
    
    # Remove duplicates based on URL
    original_count = len(combined_df)
    combined_df = combined_df.drop_duplicates(subset=['url'], keep='first')
    deduplicated_count = len(combined_df)
    
    print(f"\n📊 COMBINATION RESULTS:")
    print(f"   • Total URLs processed: {total_urls}")
    print(f"   • After deduplication: {deduplicated_count}")
    print(f"   • Duplicates removed: {original_count - deduplicated_count}")
    print(f"   • Total emails found: {total_emails}")
    print(f"   • Total phones found: {total_phones}")
    
    # Save combined results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # All results
    all_results_file = results_path / f"combined_all_results_{timestamp}.csv"
    combined_df.to_csv(all_results_file, index=False)
    print(f"💾 All results saved: {all_results_file.name}")
    
    # Companies with emails only
    email_df = combined_df[combined_df['email'].notna() & (combined_df['email'] != '')]
    if not email_df.empty:
        email_file = results_path / f"combined_emails_only_{timestamp}.csv"
        email_df.to_csv(email_file, index=False)
        print(f"📧 Email results saved: {email_file.name} ({len(email_df)} companies)")
    
    # Companies with phones only
    phone_df = combined_df[combined_df['phone'].notna() & (combined_df['phone'] != '')]
    if not phone_df.empty:
        phone_file = results_path / f"combined_phones_only_{timestamp}.csv"
        phone_df.to_csv(phone_file, index=False)
        print(f"📞 Phone results saved: {phone_file.name} ({len(phone_df)} companies)")
    
    # Companies with both email and phone
    both_df = combined_df[
        (combined_df['email'].notna() & (combined_df['email'] != '')) &
        (combined_df['phone'].notna() & (combined_df['phone'] != ''))
    ]
    if not both_df.empty:
        both_file = results_path / f"combined_email_and_phone_{timestamp}.csv"
        both_df.to_csv(both_file, index=False)
        print(f"🎯 Email+Phone results saved: {both_file.name} ({len(both_df)} companies)")
    
    # Summary report
    summary_file = results_path / f"processing_summary_{timestamp}.txt"
    with open(summary_file, 'w') as f:
        f.write(f"Mini-Chunk Processing Summary\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"=" * 50 + "\n\n")
        f.write(f"Files Processed: {len(result_files)}\n")
        f.write(f"Total URLs: {total_urls}\n")
        f.write(f"After Deduplication: {deduplicated_count}\n")
        f.write(f"Duplicates Removed: {original_count - deduplicated_count}\n")
        f.write(f"Emails Found: {total_emails}\n")
        f.write(f"Phones Found: {total_phones}\n")
        f.write(f"Both Email+Phone: {len(both_df) if not both_df.empty else 0}\n")
        f.write(f"\nSuccess Rate: {(total_emails + total_phones) / total_urls * 100:.1f}%\n")
        f.write(f"Email Rate: {total_emails / total_urls * 100:.1f}%\n")
        f.write(f"Phone Rate: {total_phones / total_urls * 100:.1f}%\n")
    
    print(f"📋 Summary saved: {summary_file.name}")
    print(f"\n🎉 Results combination completed successfully!")


def list_mini_chunk_files(results_dir="chunk_ui_results"):
    """List all mini-chunk result files."""
    results_path = Path(results_dir)
    
    if not results_path.exists():
        print(f"❌ Results directory not found: {results_dir}")
        return
    
    result_files = list(results_path.glob("mini_chunk_*_results_*.csv"))
    
    if not result_files:
        print(f"❌ No mini-chunk result files found in {results_dir}")
        return
    
    print(f"📁 Mini-Chunk Result Files ({len(result_files)} found):")
    print("=" * 60)
    
    total_urls = 0
    total_emails = 0
    total_phones = 0
    
    for file_path in sorted(result_files):
        try:
            df = pd.read_csv(file_path)
            emails = len(df[df['email'].notna() & (df['email'] != '')])
            phones = len(df[df['phone'].notna() & (df['phone'] != '')])
            
            total_urls += len(df)
            total_emails += emails
            total_phones += phones
            
            print(f"{file_path.name}")
            print(f"   URLs: {len(df)}, Emails: {emails}, Phones: {phones}")
            
        except Exception as e:
            print(f"{file_path.name} - ERROR: {e}")
    
    print("=" * 60)
    print(f"TOTALS: URLs: {total_urls}, Emails: {total_emails}, Phones: {total_phones}")


if __name__ == "__main__":
    print("🔧 Mini-Chunk Results Manager")
    print("=" * 40)
    
    while True:
        print("\nOptions:")
        print("1. List mini-chunk files")
        print("2. Combine all results")
        print("3. Exit")
        
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            list_mini_chunk_files()
        elif choice == "2":
            combine_mini_chunk_results()
        elif choice == "3":
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please enter 1, 2, or 3.")
