import asyncio
from chunk_scraper import Chunked<PERSON><PERSON><PERSON><PERSON><PERSON>raper
import pandas as pd

async def test_real_chunk():
    print('🧪 TEST 8: Real Single Chunk Processing (Small Sample)')
    print('='*50)
    
    try:
        # Create a mini chunk for testing
        scraper = ChunkedDatasetScraper(
            chunks_dir='pittsburgh_coffee_prospects_FINAL_20250914_164553_CLEAN_chunks',
            results_dir='test_results',
            batch_size=3,
            max_concurrent=2
        )
        
        # Get just 5 URLs from chunk 6 for testing
        all_urls = scraper.get_chunk_urls('chunk_006_of_006.csv')
        test_urls = all_urls[:5]
        
        print('   • Testing with', len(test_urls), 'URLs from chunk 6')
        
        # Create a temporary mini chunk file for testing
        df = pd.read_csv('pittsburgh_coffee_prospects_FINAL_20250914_164553_CLEAN_chunks/chunk_006_of_006.csv')
        mini_df = df.head(5)  # Just first 5 rows
        mini_chunk_path = 'pittsburgh_coffee_prospects_FINAL_20250914_164553_CLEAN_chunks/mini_chunk_test.csv'
        mini_df.to_csv(mini_chunk_path, index=False)

        print('   • Created mini chunk file with', len(mini_df), 'rows')

        # Test processing this mini chunk
        chunk_data = await scraper.process_chunk(998, 'mini_chunk_test.csv')
        
        print('✅ Mini chunk processing completed')
        print('   • Chunk number:', chunk_data['chunk'])
        print('   • URLs processed:', chunk_data['urls_processed'])
        print('   • Results count:', len(chunk_data['results']))
        print('   • Duration:', f"{chunk_data.get('duration', 0):.1f}s")
        print('   • Rate:', f"{chunk_data.get('rate', 0):.2f} URLs/sec")
        
        # Test saving the results
        save_info = scraper.save_chunk_results(chunk_data)
        
        if save_info:
            print('✅ Mini chunk results saved successfully')
            print('   • Emails found:', save_info['emails_found'])
            print('   • Phones found:', save_info['phones_found'])
        else:
            print('⚠️  No results to save (all failed or no contacts found)')
            
        return chunk_data
        
    except Exception as e:
        print('❌ Real chunk test failed:', e)
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(test_real_chunk())
