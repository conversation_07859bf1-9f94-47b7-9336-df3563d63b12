"""
Test the final production extractor on Defer Coffee
"""

import asyncio
from final_production_extractor import FinalProductionExtractor


async def test_defer_final():
    """Test Defer Coffee with the final guaranteed approach."""
    print("🎯 FINAL TEST - DEFER COFFEE")
    print("Guaranteed to check contact pages!")
    print("=" * 50)
    
    extractor = FinalProductionExtractor(batch_size=5, max_concurrent=2)
    results = await extractor.extract_final(['https://www.defer.coffee/strip-district'])
    
    result = results[0]
    if 'error' not in result:
        email = result.get('email')
        social = result.get('social_media')
        pages_checked = result.get('pages_checked', 0)
        pages_with_content = result.get('pages_with_content', 0)
        
        print(f"\nDefer Coffee Final Results:")
        print(f"Pages checked: {pages_checked}")
        print(f"Pages with content: {pages_with_content}")
        
        if email:
            print(f"✅ Email: {email['email']} (confidence: {email['confidence']:.1%})")
        else:
            print("❌ Email: Still not found")
        
        if social:
            print(f"✅ Social: {social['platform']} {social.get('handle', '')}")
        else:
            print("❌ Social: Not found")
    else:
        print(f"Error: {result['error']}")


async def test_multiple_final():
    """Test multiple sites with the final approach."""
    print("\n🚀 FINAL TEST - MULTIPLE SITES")
    print("=" * 50)
    
    test_urls = [
        "https://www.defer.coffee/strip-district",
        "https://thejamescafe.com/",
        "https://convivecoffee.com/",
        "http://www.yinzcoffee.com/"
    ]
    
    extractor = FinalProductionExtractor(batch_size=10, max_concurrent=3)
    results = await extractor.extract_final(test_urls)
    
    print(f"\nFinal Results Summary:")
    
    for i, result in enumerate(results, 1):
        if 'error' in result:
            print(f"{i}. ❌ {result['url']}: {result['error']}")
            continue
        
        url = result['url']
        email = result.get('email')
        social = result.get('social_media')
        pages_checked = result.get('pages_checked', 0)
        
        domain = url.split('/')[2].replace('www.', '')
        
        print(f"\n{i}. 🏪 {domain} ({pages_checked} pages checked)")
        
        if email:
            print(f"   📧 {email['email']} (confidence: {email['confidence']:.1%})")
        else:
            print(f"   📧 No email found")
        
        if social:
            print(f"   🌐 {social['platform']} {social.get('handle', '')} (confidence: {social['confidence']:.1%})")
        else:
            print(f"   🌐 No social found")
    
    # Summary
    extractor.print_summary(results)


async def main():
    """Main test function."""
    try:
        await test_defer_final()
        await test_multiple_final()
        
        print(f"\n🎉 Final testing completed!")
        print("This version guarantees checking:")
        print("✅ Main page")
        print("✅ /contact page")
        print("✅ /contact-us page")
        print("✅ /about page")
        print("✅ Enhanced regex for emails in plain text")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n🎯 Final test interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
