{"db_name": "PostgreSQL", "query": "\n\t\tSELECT id, created_at, total_records FROM v1_bulk_job\n\t\tWHERE id = $1\n\t\tLIMIT 1\n\t\t", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Int4"}, {"ordinal": 1, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 2, "name": "total_records", "type_info": "Int4"}], "parameters": {"Left": ["Int4"]}, "nullable": [false, false, false]}, "hash": "0968b040845ecce236576f65df3d3648f7ce03bc5ae0ebe9f36f878c309061c8"}